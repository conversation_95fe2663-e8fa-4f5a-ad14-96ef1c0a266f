subject_name,class_level_name,topic_title,question_text,question_type,correct_answer,explanation,difficulty,points,time_limit,choice_a,choice_b,choice_c,choice_d
ICT,Grade 8,Computer Basics,What does ICT stand for?,multiple_choice,b,ICT stands for Information and Communication Technology.,easy,10,45,Internet and Computer Technology,Information and Communication Technology,International Computer Technology,Integrated Communication Technology
ICT,Grade 8,Computer Basics,What is a computer?,multiple_choice,c,A computer is an electronic device that processes data and performs calculations.,easy,10,75,Only a calculator,Only a gaming device,Electronic device that processes data and performs calculations,Only a typing machine
ICT,Grade 8,Computer Basics,What are the main components of a computer system?,multiple_choice,a,The main components are hardware software and users.,medium,10,120,Hardware software and users,Only hardware and software,Only hardware and users,Only software and users
ICT,Grade 8,Computer Basics,What is hardware?,multiple_choice,d,Hardware refers to the physical parts of a computer that you can touch.,easy,10,90,Computer programs,Data stored in computer,Instructions for computer,Physical parts of computer you can touch
ICT,Grade 8,Computer Basics,What is software?,multiple_choice,b,Software consists of programs and instructions that tell the computer what to do.,medium,10,120,Physical parts of computer,Programs and instructions telling computer what to do,Data stored on computer,Users of the computer
ICT,Grade 8,Computer Basics,What is the difference between hardware and software?,multiple_choice,c,Hardware is physical components while software is programs and instructions.,medium,10,150,Hardware is programs software is physical,Both are the same thing,Hardware is physical software is programs and instructions,Hardware is data software is users
ICT,Grade 8,Computer Basics,What is data?,multiple_choice,a,Data is raw facts and figures that can be processed by a computer.,medium,10,90,Raw facts and figures processed by computer,Only numbers,Only text,Only pictures
ICT,Grade 8,Computer Basics,What is information?,multiple_choice,d,Information is processed data that has meaning and is useful.,medium,10,120,Same as data,Only facts,Only numbers,Processed data that has meaning and is useful
ICT,Grade 8,Computer Basics,What is the difference between data and information?,multiple_choice,b,Data is raw facts while information is processed data with meaning.,hard,10,150,Data and information are the same,Data is raw facts information is processed data with meaning,Data is processed information is raw,Data is useful information is not
ICT,Grade 8,Computer Basics,What is processing?,multiple_choice,c,Processing is the manipulation of data to produce useful information.,medium,10,120,Storing data,Displaying data,Manipulating data to produce useful information,Deleting data
ICT,Grade 8,Input Devices,What is an input device?,multiple_choice,a,An input device is used to enter data and instructions into a computer.,medium,10,120,Device used to enter data and instructions into computer,Device that displays information,Device that stores data,Device that prints documents
ICT,Grade 8,Input Devices,Which of the following is an input device?,multiple_choice,b,A keyboard is an input device used to type text and commands.,easy,10,60,Monitor,Keyboard,Printer,Speaker
ICT,Grade 8,Input Devices,What is the function of a mouse?,multiple_choice,c,A mouse is used to point click and select items on the computer screen.,easy,10,90,To type text,To display images,To point click and select items on screen,To print documents
ICT,Grade 8,Input Devices,What is a microphone used for?,multiple_choice,d,A microphone is used to input sound and voice into the computer.,medium,10,90,To display text,To print documents,To store data,To input sound and voice into computer
ICT,Grade 8,Input Devices,What is a scanner?,multiple_choice,a,A scanner is used to convert physical documents and images into digital format.,hard,10,150,Device converting physical documents to digital format,Device that prints documents,Device that displays images,Device that stores data
ICT,Grade 8,Input Devices,What is a webcam?,multiple_choice,b,A webcam is a camera used to capture video and images for computer input.,medium,10,120,Device for printing photos,Camera capturing video and images for computer input,Device for storing photos,Device for displaying photos
ICT,Grade 8,Input Devices,What is a joystick used for?,multiple_choice,c,A joystick is primarily used for playing games and controlling movement.,medium,10,90,For typing text,For printing documents,For playing games and controlling movement,For storing data
ICT,Grade 8,Input Devices,What is a touchscreen?,multiple_choice,d,A touchscreen is both an input and output device that responds to touch.,hard,10,150,Only an input device,Only an output device,Device that only displays,Both input and output device responding to touch
ICT,Grade 8,Input Devices,What is a graphics tablet?,multiple_choice,a,A graphics tablet is used for drawing and designing with a special pen.,hard,10,120,Device for drawing and designing with special pen,Device for typing,Device for printing,Device for storing images
ICT,Grade 8,Input Devices,What is voice recognition?,multiple_choice,b,Voice recognition is technology that converts spoken words into computer commands or text.,hard,10,180,Technology that creates voice,Technology converting spoken words to commands or text,Technology that plays music,Technology that records sound only
ICT,Grade 8,Output Devices,What is an output device?,multiple_choice,c,An output device displays or presents processed information from the computer.,medium,10,120,Device that inputs data,Device that stores data,Device that displays or presents processed information,Device that processes data
ICT,Grade 8,Output Devices,Which of the following is an output device?,multiple_choice,a,A monitor is an output device that displays visual information.,easy,10,75,Monitor,Keyboard,Mouse,Scanner
ICT,Grade 8,Output Devices,What is the function of a printer?,multiple_choice,d,A printer produces hard copies of documents and images on paper.,easy,10,90,To display information,To input data,To store data,To produce hard copies on paper
ICT,Grade 8,Output Devices,What is a speaker used for?,multiple_choice,b,Speakers are used to output sound and audio from the computer.,easy,10,75,To input sound,To output sound and audio from computer,To store sound,To record sound
ICT,Grade 8,Output Devices,What is the difference between a monitor and a printer?,multiple_choice,c,A monitor displays information on screen while a printer produces hard copies on paper.,medium,10,150,Both display information,Both print on paper,Monitor displays on screen printer produces hard copies,No difference between them
ICT,Grade 8,Output Devices,What is a projector?,multiple_choice,a,A projector displays computer images on a large screen or wall.,medium,10,120,Device displaying images on large screen or wall,Device that prints large documents,Device that stores large files,Device that inputs large data
ICT,Grade 8,Output Devices,What are headphones?,multiple_choice,d,Headphones are personal audio output devices worn on or in the ears.,medium,10,120,Input devices for sound,Devices for storing music,Devices for recording sound,Personal audio output devices worn on ears
ICT,Grade 8,Output Devices,What is a plotter?,multiple_choice,b,A plotter is a specialized printer used for printing large technical drawings.,hard,10,150,Regular document printer,Specialized printer for large technical drawings,Device for scanning large documents,Device for displaying large images
ICT,Grade 8,Output Devices,What is the difference between impact and non-impact printers?,multiple_choice,c,Impact printers strike paper while non-impact printers use other methods like ink spray.,hard,10,180,Impact printers are faster,Non-impact printers are louder,Impact strike paper non-impact use other methods,No difference between them
ICT,Grade 8,Output Devices,What is resolution in display devices?,multiple_choice,a,Resolution refers to the number of pixels that make up the display image.,hard,10,150,Number of pixels making up display image,Size of the screen,Brightness of display,Color quality of display
ICT,Grade 8,Storage Devices,What is storage in computing?,multiple_choice,b,Storage is the retention of data and programs for future use.,medium,10,90,Processing of data,Retention of data and programs for future use,Display of information,Input of data
ICT,Grade 8,Storage Devices,What are the two main types of storage?,multiple_choice,c,The two main types are primary storage (memory) and secondary storage.,hard,10,150,Fast and slow storage,Big and small storage,Primary storage memory and secondary storage,Internal and external storage
ICT,Grade 8,Storage Devices,What is primary storage?,multiple_choice,a,Primary storage is temporary memory used while the computer is running.,hard,10,150,Temporary memory used while computer running,Permanent storage for files,Storage for programs only,Storage for data only
ICT,Grade 8,Storage Devices,What is secondary storage?,multiple_choice,d,Secondary storage provides permanent storage for data and programs.,medium,10,120,Temporary storage,Storage for memory only,Storage for processing,Permanent storage for data and programs
ICT,Grade 8,Storage Devices,What is RAM?,multiple_choice,b,RAM (Random Access Memory) is temporary storage that loses data when power is off.,hard,10,150,Permanent storage device,Temporary storage losing data when power off,Storage for programs only,Storage device you can see
ICT,Grade 8,Storage Devices,What is ROM?,multiple_choice,c,ROM (Read Only Memory) is permanent storage that retains data when power is off.,hard,10,180,Temporary storage device,Storage you can write to,Permanent storage retaining data when power off,Storage for user files only
ICT,Grade 8,Storage Devices,What is a hard disk drive (HDD)?,multiple_choice,a,A hard disk drive is a secondary storage device that stores data on magnetic disks.,hard,10,150,Secondary storage using magnetic disks,Primary storage device,Temporary storage device,Input device for data
ICT,Grade 8,Storage Devices,What is a solid state drive (SSD)?,multiple_choice,d,A solid state drive uses flash memory chips for faster data access than HDDs.,hard,10,180,Same as hard disk drive,Temporary storage device,Primary storage device,Uses flash memory chips for faster access than HDDs
ICT,Grade 8,Storage Devices,What is a USB flash drive?,multiple_choice,b,A USB flash drive is a portable storage device that connects via USB port.,medium,10,120,Permanent computer storage,Portable storage device connecting via USB port,Input device,Output device
ICT,Grade 8,Storage Devices,What is cloud storage?,multiple_choice,c,Cloud storage is storing data on remote servers accessed via the internet.,hard,10,150,Storage on your computer,Storage on external drive,Storage on remote servers accessed via internet,Storage that disappears
ICT,Grade 8,Operating Systems,What is an operating system?,multiple_choice,a,An operating system is software that manages computer hardware and provides services to programs.,hard,10,180,Software managing hardware and providing services to programs,Only the computer screen,Physical parts of computer,Programs for games only
ICT,Grade 8,Operating Systems,What are the main functions of an operating system?,multiple_choice,d,Main functions include managing hardware resources files programs and user interface.,hard,10,180,Only managing files,Only managing programs,Only providing user interface,Managing hardware resources files programs user interface
ICT,Grade 8,Operating Systems,What is a user interface?,multiple_choice,b,A user interface is how users interact with the computer system.,medium,10,120,Physical parts of computer,How users interact with computer system,Programs stored on computer,Data processed by computer
ICT,Grade 8,Operating Systems,What is a GUI?,multiple_choice,c,GUI (Graphical User Interface) uses icons windows and menus for user interaction.,hard,10,150,Text-based interface only,Command-line interface,Uses icons windows menus for interaction,Voice-based interface only
ICT,Grade 8,Operating Systems,What is a file?,multiple_choice,a,A file is a collection of data stored with a specific name on a storage device.,medium,10,120,Collection of data stored with specific name,Physical storage device,Program running on computer,User of the computer
ICT,Grade 8,Operating Systems,What is a folder or directory?,multiple_choice,d,A folder is a container used to organize and store files.,medium,10,90,Type of file,Storage device,Program for organizing,Container used to organize and store files
ICT,Grade 8,Operating Systems,What is file management?,multiple_choice,b,File management involves organizing creating deleting and maintaining files and folders.,hard,10,150,Only creating files,Organizing creating deleting maintaining files and folders,Only deleting files,Only viewing files
ICT,Grade 8,Operating Systems,What are some examples of operating systems?,multiple_choice,c,Examples include Windows macOS Linux Android and iOS.,medium,10,120,Only Windows,Only macOS,Windows macOS Linux Android iOS,Only mobile systems
ICT,Grade 8,Operating Systems,What is multitasking?,multiple_choice,a,Multitasking is the ability to run multiple programs simultaneously.,hard,10,120,Ability to run multiple programs simultaneously,Running one program at a time,Using multiple computers,Having multiple users
ICT,Grade 8,Operating Systems,What is a device driver?,multiple_choice,d,A device driver is software that allows the operating system to communicate with hardware.,hard,10,180,Physical device,User of device,Program for users,Software allowing OS to communicate with hardware
ICT,Grade 8,Internet and Networks,What is a network?,multiple_choice,b,A network is a group of connected computers that can share resources and information.,medium,10,150,Single computer,Group of connected computers sharing resources and information,Internet connection only,Software program
ICT,Grade 8,Internet and Networks,What is the Internet?,multiple_choice,c,The Internet is a global network of interconnected computers and networks.,medium,10,120,Single computer network,Local area network only,Global network of interconnected computers and networks,Software application
ICT,Grade 8,Internet and Networks,What is the World Wide Web (WWW)?,multiple_choice,a,The World Wide Web is a system of linked documents accessed via the Internet.,hard,10,150,System of linked documents accessed via Internet,Same as the Internet,Email system only,File storage system
ICT,Grade 8,Internet and Networks,What is a website?,multiple_choice,d,A website is a collection of related web pages accessible via the Internet.,medium,10,120,Single web page,Internet connection,Web browser,Collection of related web pages accessible via Internet
ICT,Grade 8,Internet and Networks,What is a web browser?,multiple_choice,b,A web browser is software used to access and view websites on the Internet.,medium,10,120,Website creator,Software used to access and view websites,Internet connection,Web page content
ICT,Grade 8,Internet and Networks,What is a URL?,multiple_choice,c,URL (Uniform Resource Locator) is the address of a website or web page.,hard,10,120,Type of web browser,Internet connection,Address of website or web page,Web page content
ICT,Grade 8,Internet and Networks,What is email?,multiple_choice,a,Email (electronic mail) is a method of sending messages electronically via the Internet.,medium,10,120,Method of sending messages electronically via Internet,Physical mail system,Telephone communication,Face-to-face communication
ICT,Grade 8,Internet and Networks,What is an email address?,multiple_choice,d,An email address is a unique identifier for sending and receiving electronic messages.,medium,10,120,Physical address,Telephone number,Website address,Unique identifier for sending receiving electronic messages
ICT,Grade 8,Internet and Networks,What is social media?,multiple_choice,b,Social media are online platforms that allow people to create share content and interact.,hard,10,180,Traditional media like TV,Online platforms for creating sharing content and interacting,Email system only,File storage system
ICT,Grade 8,Internet and Networks,What is online safety?,multiple_choice,c,Online safety involves protecting yourself from dangers and risks while using the Internet.,hard,10,150,Using antivirus software only,Creating strong passwords only,Protecting yourself from dangers and risks while using Internet,Avoiding computers completely
ICT,Grade 8,Word Processing,What is word processing?,multiple_choice,a,Word processing is creating editing and formatting text documents using computer software.,medium,10,150,Creating editing formatting text documents using software,Only typing text,Only printing documents,Only saving files
ICT,Grade 8,Word Processing,What is Microsoft Word?,multiple_choice,b,Microsoft Word is a popular word processing software application.,easy,10,75,Spreadsheet software,Popular word processing software application,Presentation software,Database software
ICT,Grade 8,Word Processing,What is formatting in word processing?,multiple_choice,c,Formatting is changing the appearance of text such as font size color and style.,medium,10,120,Only typing text,Only saving documents,Changing appearance of text like font size color style,Only printing documents
ICT,Grade 8,Word Processing,What is a font?,multiple_choice,d,A font is a set of characters with a specific design and style.,medium,10,90,Size of text,Color of text,Alignment of text,Set of characters with specific design and style
ICT,Grade 8,Word Processing,What is copy and paste?,multiple_choice,a,Copy and paste allows you to duplicate text and move it to another location.,medium,10,120,Duplicate text and move to another location,Delete text permanently,Change text color,Print text on paper
ICT,Grade 8,Word Processing,What is spell check?,multiple_choice,b,Spell check is a feature that identifies and suggests corrections for misspelled words.,medium,10,150,Feature that counts words,Feature identifying and suggesting corrections for misspelled words,Feature that changes font,Feature that prints documents
ICT,Grade 8,Word Processing,What is a template?,multiple_choice,c,A template is a pre-designed document format that can be customized for specific use.,hard,10,150,Completed document,Blank document,Pre-designed document format that can be customized,Printed document
ICT,Grade 8,Word Processing,What is page layout?,multiple_choice,a,Page layout refers to the arrangement and formatting of content on a page.,hard,10,120,Arrangement and formatting of content on page,Only text content,Only images on page,Only page numbers
ICT,Grade 8,Word Processing,What are headers and footers?,multiple_choice,d,Headers and footers are text that appears at the top and bottom of every page.,medium,10,150,Text in the middle of page,Text on the sides of page,Text that changes on each page,Text appearing at top and bottom of every page
ICT,Grade 8,Word Processing,What is mail merge?,multiple_choice,b,Mail merge combines a document template with a data source to create personalized documents.,hard,10,180,Sending multiple emails,Combining document template with data source for personalized documents,Merging two documents,Printing multiple copies
ICT,Grade 8,Spreadsheets,What is a spreadsheet?,multiple_choice,c,A spreadsheet is a program that organizes data in rows and columns for calculations.,medium,10,120,Word processing program,Presentation program,Program organizing data in rows and columns for calculations,Database program
ICT,Grade 8,Spreadsheets,What is Microsoft Excel?,multiple_choice,a,Microsoft Excel is a popular spreadsheet software application.,easy,10,60,Popular spreadsheet software application,Word processing software,Presentation software,Database software
ICT,Grade 8,Spreadsheets,What is a cell in a spreadsheet?,multiple_choice,d,A cell is the intersection of a row and column where data can be entered.,medium,10,120,Entire row,Entire column,Entire spreadsheet,Intersection of row and column where data entered
ICT,Grade 8,Spreadsheets,What is a formula in spreadsheets?,multiple_choice,b,A formula is an expression that performs calculations on values in cells.,hard,10,150,Text entered in cell,Expression performing calculations on values in cells,Name of spreadsheet,Color of cell
ICT,Grade 8,Spreadsheets,What does the SUM function do?,multiple_choice,c,The SUM function adds up all the numbers in a selected range of cells.,medium,10,120,Subtracts numbers,Multiplies numbers,Adds up numbers in selected range of cells,Divides numbers
ICT,Grade 8,Spreadsheets,What is a chart in spreadsheets?,multiple_choice,a,A chart is a visual representation of data using graphs bars or lines.,medium,10,120,Visual representation of data using graphs bars lines,Text summary of data,List of numbers,Table of data
ICT,Grade 8,Spreadsheets,What is sorting in spreadsheets?,multiple_choice,d,Sorting arranges data in ascending or descending order based on selected criteria.,hard,10,150,Deleting data,Adding new data,Changing data format,Arranging data in ascending or descending order
ICT,Grade 8,Spreadsheets,What is filtering in spreadsheets?,multiple_choice,b,Filtering displays only the data that meets specific criteria while hiding the rest.,hard,10,180,Deleting unwanted data,Displaying only data meeting specific criteria while hiding rest,Changing data color,Copying data to new location
ICT,Grade 8,Spreadsheets,What is a range in spreadsheets?,multiple_choice,c,A range is a group of selected cells that can be used together in formulas.,medium,10,120,Single cell,Entire spreadsheet,Group of selected cells used together in formulas,Row or column header
ICT,Grade 8,Spreadsheets,What is conditional formatting?,multiple_choice,a,Conditional formatting changes cell appearance based on the values they contain.,hard,10,150,Changes cell appearance based on values they contain,Changes all cells to same format,Changes only text format,Changes only number format
ICT,Grade 8,Presentations,What is presentation software?,multiple_choice,b,Presentation software is used to create slideshows for displaying information to audiences.,medium,10,150,Software for word processing,Software for creating slideshows to display information,Software for calculations,Software for database management
ICT,Grade 8,Presentations,What is Microsoft PowerPoint?,multiple_choice,c,Microsoft PowerPoint is a popular presentation software application.,easy,10,75,Word processing software,Spreadsheet software,Popular presentation software application,Database software
ICT,Grade 8,Presentations,What is a slide?,multiple_choice,a,A slide is a single page or screen in a presentation that contains information.,easy,10,90,Single page or screen in presentation containing information,Entire presentation,Animation effect,Transition between pages
ICT,Grade 8,Presentations,What is a slide layout?,multiple_choice,d,A slide layout is a pre-designed arrangement of placeholders for content on a slide.,hard,10,150,Content on slide,Color scheme of slide,Animation on slide,Pre-designed arrangement of placeholders for content
ICT,Grade 8,Presentations,What is animation in presentations?,multiple_choice,b,Animation is the movement or effect applied to objects or text on slides.,medium,10,120,Static images on slides,Movement or effect applied to objects or text on slides,Background color of slides,Font style of text
ICT,Grade 8,Presentations,What is a transition in presentations?,multiple_choice,c,A transition is the effect that occurs when moving from one slide to another.,medium,10,120,Content within slide,Animation of objects,Effect when moving from one slide to another,Background of slide
ICT,Grade 8,Presentations,What is a template in presentations?,multiple_choice,a,A template is a pre-designed presentation format with consistent styling.,medium,10,120,Pre-designed presentation format with consistent styling,Individual slide,Animation effect,Transition effect
ICT,Grade 8,Presentations,What is slide sorter view?,multiple_choice,d,Slide sorter view shows thumbnails of all slides for easy organization and reordering.,hard,10,150,Normal editing view,Slideshow view,Notes view,Shows thumbnails of all slides for organization and reordering
ICT,Grade 8,Presentations,What is speaker notes?,multiple_choice,b,Speaker notes are additional information for the presenter that audience cannot see.,medium,10,150,Information audience can see,Additional information for presenter that audience cannot see,Slide titles,Slide animations
ICT,Grade 8,Presentations,What is slideshow mode?,multiple_choice,c,Slideshow mode displays the presentation full-screen for audience viewing.,medium,10,120,Editing mode,Design mode,Displays presentation full-screen for audience viewing,Print preview mode
ICT,Grade 8,Digital Citizenship,What is digital citizenship?,multiple_choice,a,Digital citizenship is responsible and ethical use of technology and digital resources.,hard,10,180,Responsible and ethical use of technology and digital resources,Only using computers,Only using internet,Only using mobile phones
ICT,Grade 8,Digital Citizenship,What is cyberbullying?,multiple_choice,b,Cyberbullying is using technology to harass intimidate or harm others online.,hard,10,150,Helping others online,Using technology to harass intimidate or harm others online,Learning online,Playing games online
ICT,Grade 8,Digital Citizenship,What is digital footprint?,multiple_choice,c,Digital footprint is the trail of data you leave behind when using digital devices and internet.,hard,10,180,Physical footprints,Shoe prints on computer,Trail of data left when using digital devices and internet,Fingerprints on screen
ICT,Grade 8,Digital Citizenship,What is privacy online?,multiple_choice,d,Online privacy is controlling what personal information you share on the internet.,hard,10,150,Sharing all personal information,Hiding from everyone online,Never using internet,Controlling what personal information you share online
ICT,Grade 8,Digital Citizenship,What is a strong password?,multiple_choice,a,A strong password is long complex and unique combining letters numbers and symbols.,hard,10,180,Long complex unique combining letters numbers symbols,Your name only,Simple word,Your birthday
ICT,Grade 8,Digital Citizenship,What is phishing?,multiple_choice,b,Phishing is attempting to steal personal information through fake emails or websites.,hard,10,180,Catching fish online,Attempting to steal personal information through fake emails or websites,Sending friendly emails,Creating websites
ICT,Grade 8,Digital Citizenship,What is malware?,multiple_choice,c,Malware is malicious software designed to damage or gain unauthorized access to computers.,hard,10,180,Good software,Helpful programs,Malicious software designed to damage or gain unauthorized access,Free software
ICT,Grade 8,Digital Citizenship,What is copyright?,multiple_choice,a,Copyright is legal protection for original creative works giving creators exclusive rights.,hard,10,180,Legal protection for original works giving creators exclusive rights,Permission to copy anything,Free use of all content,Sharing without permission
ICT,Grade 8,Digital Citizenship,What is plagiarism?,multiple_choice,d,Plagiarism is using someone else's work or ideas without giving proper credit.,hard,10,150,Creating original work,Giving credit to sources,Sharing your own work,Using someone else's work without giving proper credit
ICT,Grade 8,Digital Citizenship,What is netiquette?,multiple_choice,b,Netiquette is proper etiquette and polite behavior when communicating online.,hard,10,150,Internet connection rules,Proper etiquette and polite behavior when communicating online,Technical computer skills,Website design principles
ICT,Grade 8,Programming Basics,What is programming?,multiple_choice,c,Programming is writing instructions for computers to follow to solve problems or perform tasks.,hard,10,180,Using computer programs,Playing computer games,Writing instructions for computers to solve problems or perform tasks,Repairing computers
ICT,Grade 8,Programming Basics,What is a programming language?,multiple_choice,a,A programming language is a formal language used to write instructions for computers.,hard,10,150,Formal language for writing computer instructions,Human language,Mathematical language,Foreign language
ICT,Grade 8,Programming Basics,What is an algorithm?,multiple_choice,d,An algorithm is a step-by-step procedure for solving a problem or completing a task.,hard,10,150,Computer program,Programming language,Mathematical formula,Step-by-step procedure for solving problem or completing task
ICT,Grade 8,Programming Basics,What is a flowchart?,multiple_choice,b,A flowchart is a visual representation of an algorithm using symbols and arrows.,hard,10,150,Written instructions,Visual representation of algorithm using symbols and arrows,Computer program,Mathematical equation
ICT,Grade 8,Programming Basics,What is debugging?,multiple_choice,c,Debugging is finding and fixing errors in computer programs.,hard,10,120,Writing new programs,Running programs,Finding and fixing errors in programs,Deleting programs
ICT,Grade 8,Programming Basics,What is a variable in programming?,multiple_choice,a,A variable is a storage location with a name that holds data that can change.,hard,10,150,Storage location with name holding data that can change,Fixed number,Program instruction,Computer hardware
ICT,Grade 8,Programming Basics,What is a loop in programming?,multiple_choice,d,A loop is a programming structure that repeats a set of instructions.,hard,10,120,Single instruction,Program error,Program ending,Programming structure that repeats set of instructions
ICT,Grade 8,Programming Basics,What is a condition in programming?,multiple_choice,b,A condition is a statement that can be either true or false used to make decisions.,hard,10,150,Program instruction,Statement that can be true or false used for decisions,Program error,Program variable
ICT,Grade 8,Programming Basics,What is Scratch?,multiple_choice,c,Scratch is a visual programming language designed for beginners especially children.,hard,10,150,Text editor,Web browser,Visual programming language for beginners especially children,Operating system
ICT,Grade 8,Programming Basics,What are the basic programming concepts?,multiple_choice,a,Basic concepts include sequence selection (conditions) and iteration (loops).,hard,10,180,Sequence selection iteration,Only sequence,Only selection,Only iteration
ICT,Grade 8,Database Basics,What is a database?,multiple_choice,b,A database is an organized collection of related data stored electronically.,medium,10,120,Random collection of files,Organized collection of related data stored electronically,Single computer file,Program for calculations
ICT,Grade 8,Database Basics,What is data in a database context?,multiple_choice,c,Data in databases refers to facts and information stored in an organized manner.,medium,10,120,Random information,Computer programs,Facts and information stored in organized manner,Physical storage devices
ICT,Grade 8,Database Basics,What is a record in a database?,multiple_choice,a,A record is a complete set of information about one item or person in a database.,hard,10,150,Complete set of information about one item or person,Single piece of data,Database program,Storage device
ICT,Grade 8,Database Basics,What is a field in a database?,multiple_choice,d,A field is a single piece of information within a record.,medium,10,90,Complete database,Entire record,Database program,Single piece of information within record
ICT,Grade 8,Database Basics,What is a table in a database?,multiple_choice,b,A table is a collection of related records organized in rows and columns.,hard,10,150,Single record,Collection of related records in rows and columns,Database program,Storage device
ICT,Grade 8,Database Basics,What is the difference between data and information in databases?,multiple_choice,c,Data are raw facts while information is processed data that has meaning.,hard,10,150,Data and information are same,Data is processed information is raw,Data are raw facts information is processed data with meaning,Data is useful information is not
ICT,Grade 8,Database Basics,What is a primary key?,multiple_choice,a,A primary key is a unique identifier for each record in a database table.,hard,10,150,Unique identifier for each record in table,Most important field,First field in table,Password for database
ICT,Grade 8,Database Basics,What is sorting in databases?,multiple_choice,d,Sorting arranges records in a specific order based on field values.,medium,10,120,Deleting records,Adding new records,Changing field names,Arranging records in specific order based on field values
ICT,Grade 8,Database Basics,What is searching in databases?,multiple_choice,b,Searching finds specific records that match given criteria.,medium,10,120,Adding new data,Finding specific records matching given criteria,Deleting old data,Changing data format
ICT,Grade 8,Database Basics,What are some examples of database software?,multiple_choice,c,Examples include Microsoft Access MySQL Oracle and SQLite.,hard,10,150,Only Microsoft Word,Only Microsoft Excel,Microsoft Access MySQL Oracle SQLite,Only web browsers
ICT,Grade 8,Graphics and Multimedia,What is computer graphics?,multiple_choice,a,Computer graphics involves creating and manipulating visual images using computers.,medium,10,150,Creating and manipulating visual images using computers,Only drawing on paper,Only taking photographs,Only printing documents
ICT,Grade 8,Graphics and Multimedia,What is digital art?,multiple_choice,b,Digital art is artwork created using digital technology and computer software.,medium,10,120,Traditional painting only,Artwork created using digital technology and software,Only photography,Only sculpture
ICT,Grade 8,Graphics and Multimedia,What is image editing?,multiple_choice,c,Image editing is modifying digital images using specialized software.,medium,10,120,Taking photographs,Printing images,Modifying digital images using specialized software,Storing images
ICT,Grade 8,Graphics and Multimedia,What is Adobe Photoshop?,multiple_choice,d,Adobe Photoshop is professional image editing and graphics software.,medium,10,120,Word processing software,Spreadsheet software,Presentation software,Professional image editing and graphics software
ICT,Grade 8,Graphics and Multimedia,What is resolution in digital images?,multiple_choice,a,Resolution refers to the number of pixels in an image affecting its quality and clarity.,hard,10,180,Number of pixels in image affecting quality and clarity,Size of image file,Color depth of image,Format of image file
ICT,Grade 8,Graphics and Multimedia,What are pixels?,multiple_choice,b,Pixels are tiny dots of color that make up digital images.,medium,10,90,Large blocks of color,Tiny dots of color making up digital images,Image file formats,Image editing tools
ICT,Grade 8,Graphics and Multimedia,What is multimedia?,multiple_choice,c,Multimedia combines text images audio video and interactive elements.,hard,10,150,Only text content,Only image content,Combines text images audio video interactive elements,Only video content
ICT,Grade 8,Graphics and Multimedia,What is animation?,multiple_choice,a,Animation is creating the illusion of movement by displaying sequence of images rapidly.,hard,10,180,Creating illusion of movement by displaying sequence of images rapidly,Static images only,Single moving object,Sound effects only
ICT,Grade 8,Graphics and Multimedia,What is video editing?,multiple_choice,d,Video editing is manipulating and rearranging video footage to create new work.,hard,10,150,Only watching videos,Only recording videos,Only storing videos,Manipulating and rearranging video footage to create new work
ICT,Grade 8,Graphics and Multimedia,What are common image file formats?,multiple_choice,b,Common formats include JPEG PNG GIF BMP and TIFF.,hard,10,150,Only JPEG,JPEG PNG GIF BMP TIFF,Only PNG,Only GIF
ICT,Grade 8,E-commerce and Online Services,What is e-commerce?,multiple_choice,c,E-commerce is buying and selling goods and services over the internet.,medium,10,120,Traditional shopping only,Physical store shopping,Buying and selling goods and services over internet,Window shopping
ICT,Grade 8,E-commerce and Online Services,What is online shopping?,multiple_choice,a,Online shopping is purchasing products through websites and mobile apps.,easy,10,90,Purchasing products through websites and mobile apps,Shopping in physical stores,Looking at products online only,Comparing prices only
ICT,Grade 8,E-commerce and Online Services,What is digital payment?,multiple_choice,d,Digital payment is making financial transactions electronically without physical cash.,hard,10,150,Only cash payments,Only check payments,Only credit card payments,Making financial transactions electronically without physical cash
ICT,Grade 8,E-commerce and Online Services,What is online banking?,multiple_choice,b,Online banking allows customers to conduct banking transactions via the internet.,medium,10,150,Traditional banking only,Conducting banking transactions via internet,Only ATM banking,Only phone banking
ICT,Grade 8,E-commerce and Online Services,What is a digital wallet?,multiple_choice,c,A digital wallet is an electronic device or service for making electronic transactions.,hard,10,150,Physical wallet,Traditional bank account,Electronic device or service for making electronic transactions,Credit card only
ICT,Grade 8,E-commerce and Online Services,What is online learning?,multiple_choice,a,Online learning is education delivered through digital platforms and the internet.,medium,10,150,Education delivered through digital platforms and internet,Traditional classroom only,Reading books only,Face-to-face teaching only
ICT,Grade 8,E-commerce and Online Services,What is cloud computing?,multiple_choice,d,Cloud computing provides computing services over the internet including storage and software.,hard,10,180,Local computer storage only,Physical computer hardware,Traditional software installation,Computing services over internet including storage and software
ICT,Grade 8,E-commerce and Online Services,What is streaming?,multiple_choice,b,Streaming is delivering media content over the internet in real-time without downloading.,hard,10,180,Downloading files completely,Delivering media content over internet in real-time without downloading,Storing files locally,Playing files from CD
ICT,Grade 8,E-commerce and Online Services,What is social networking?,multiple_choice,c,Social networking is using online platforms to connect and interact with other people.,medium,10,150,Traditional face-to-face meetings,Phone conversations only,Using online platforms to connect and interact with people,Email communication only
ICT,Grade 8,E-commerce and Online Services,What are the benefits of online services?,multiple_choice,a,Benefits include convenience accessibility 24/7 availability and global reach.,hard,10,180,Convenience accessibility 24/7 availability global reach,Only convenience,Only accessibility,Only global reach
ICT,Grade 8,Computer Maintenance,What is computer maintenance?,multiple_choice,b,Computer maintenance involves keeping computer systems running efficiently and preventing problems.,hard,10,180,Only repairing broken computers,Keeping systems running efficiently and preventing problems,Only cleaning computer screens,Only updating software
ICT,Grade 8,Computer Maintenance,Why is regular maintenance important?,multiple_choice,c,Regular maintenance prevents problems improves performance and extends computer lifespan.,hard,10,180,Only to clean dust,Only to update software,Prevents problems improves performance extends lifespan,Only to save money
ICT,Grade 8,Computer Maintenance,What is disk cleanup?,multiple_choice,a,Disk cleanup removes unnecessary files to free up storage space and improve performance.,hard,10,180,Removes unnecessary files to free storage and improve performance,Only deletes user files,Only empties recycle bin,Only removes programs
ICT,Grade 8,Computer Maintenance,What is defragmentation?,multiple_choice,d,Defragmentation reorganizes data on hard drive to improve access speed and efficiency.,hard,10,180,Deletes files permanently,Adds more storage space,Creates backup copies,Reorganizes data on hard drive to improve access speed
ICT,Grade 8,Computer Maintenance,What is antivirus software?,multiple_choice,b,Antivirus software protects computers from malicious software and security threats.,medium,10,150,Software for creating viruses,Software protecting from malicious software and security threats,Software for speeding up computer,Software for organizing files
ICT,Grade 8,Computer Maintenance,What is a software update?,multiple_choice,c,A software update provides new features bug fixes and security improvements.,medium,10,150,Complete software replacement,New software installation,Provides new features bug fixes security improvements,Software removal
ICT,Grade 8,Computer Maintenance,What is backup?,multiple_choice,a,Backup is creating copies of important data to prevent loss in case of system failure.,hard,10,180,Creating copies of data to prevent loss in system failure,Deleting old files,Moving files to different location,Organizing files in folders
ICT,Grade 8,Computer Maintenance,What is system restore?,multiple_choice,d,System restore returns computer to previous working state when problems occur.,hard,10,150,Installing new software,Updating all programs,Cleaning temporary files,Returns computer to previous working state when problems occur
ICT,Grade 8,Computer Maintenance,What are signs that a computer needs maintenance?,multiple_choice,b,Signs include slow performance frequent crashes error messages and overheating.,hard,10,180,Only slow startup,Slow performance frequent crashes error messages overheating,Only software errors,Only hardware problems
ICT,Grade 8,Computer Maintenance,What is preventive maintenance?,multiple_choice,c,Preventive maintenance involves regular care to prevent problems before they occur.,hard,10,150,Fixing problems after they happen,Waiting for computer to break,Regular care to prevent problems before they occur,Only emergency repairs
ICT,Grade 8,Emerging Technologies,What is artificial intelligence (AI)?,multiple_choice,a,AI is technology that enables machines to perform tasks that typically require human intelligence.,hard,10,180,Technology enabling machines to perform tasks requiring human intelligence,Only computer games,Only internet browsing,Only word processing
ICT,Grade 8,Emerging Technologies,What is virtual reality (VR)?,multiple_choice,b,VR is technology that creates immersive simulated environments users can interact with.,hard,10,180,Traditional computer screen,Technology creating immersive simulated environments for interaction,Only 3D movies,Only computer games
ICT,Grade 8,Emerging Technologies,What is augmented reality (AR)?,multiple_choice,c,AR overlays digital information onto the real world through devices like smartphones.,hard,10,180,Complete virtual world,Traditional photography,Overlays digital information onto real world through devices,Only computer graphics
ICT,Grade 8,Emerging Technologies,What is robotics?,multiple_choice,d,Robotics is the design construction and operation of robots for various tasks.,hard,10,150,Only toy robots,Only industrial machines,Only computer programs,Design construction and operation of robots for various tasks
ICT,Grade 8,Emerging Technologies,What is the Internet of Things (IoT)?,multiple_choice,a,IoT connects everyday objects to the internet enabling them to send and receive data.,hard,10,180,Connects everyday objects to internet for sending receiving data,Only computer networks,Only mobile phones,Only smart TVs
ICT,Grade 8,Emerging Technologies,What is 3D printing?,multiple_choice,b,3D printing creates physical objects from digital designs by adding material layer by layer.,hard,10,180,Traditional paper printing,Creates physical objects from digital designs by adding material layers,Only printing documents,Only printing photos
ICT,Grade 8,Emerging Technologies,What is blockchain?,multiple_choice,c,Blockchain is a secure digital ledger technology used for recording transactions.,hard,10,180,Traditional database,Physical record book,Secure digital ledger technology for recording transactions,Only cryptocurrency
ICT,Grade 8,Emerging Technologies,What is machine learning?,multiple_choice,a,Machine learning enables computers to learn and improve from experience without explicit programming.,hard,10,180,Enables computers to learn and improve from experience without explicit programming,Only human learning,Only school education,Only memorizing information
ICT,Grade 8,Emerging Technologies,What is biometrics?,multiple_choice,d,Biometrics uses unique biological characteristics for identification and security purposes.,hard,10,180,Traditional passwords,Physical keys,Written signatures,Uses unique biological characteristics for identification and security
ICT,Grade 8,Emerging Technologies,What impact do emerging technologies have on society?,multiple_choice,b,They transform how we work communicate learn and solve problems in daily life.,hard,10,180,No impact on society,Transform how we work communicate learn solve problems in daily life,Only affect technology companies,Only affect computer users
