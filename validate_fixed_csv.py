#!/usr/bin/env python3
"""
Validate the fixed CSV file
"""
import csv

def validate_fixed_csv():
    """Validate the quality of the fixed CSV file"""
    
    valid_question_types = ['multiple_choice', 'fill_blank', 'true_false', 'short_answer']
    valid_difficulties = ['easy', 'medium', 'hard']
    
    total_rows = 0
    valid_rows = 0
    errors = []
    
    print("🔍 VALIDATING FIXED CSV FILE...")
    print("=" * 60)
    
    with open('Grade_9_questions_FIXED.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        
        for row_num, row in enumerate(reader, 1):
            if row_num == 1:  # Skip header
                continue
                
            total_rows += 1
            
            if len(row) < 14:
                errors.append(f"Row {row_num}: Insufficient fields ({len(row)} found, 14 required)")
                continue
            
            # Validate each field
            subject_name = row[0].strip()
            class_level_name = row[1].strip()
            topic_title = row[2].strip()
            question_text = row[3].strip()
            question_type = row[4].strip().lower()
            correct_answer = row[5].strip().lower()
            explanation = row[6].strip()
            difficulty = row[7].strip().lower()
            points = row[8].strip()
            time_limit = row[9].strip()
            
            row_valid = True
            
            # Check required fields are not empty
            if not subject_name:
                errors.append(f"Row {row_num}: Empty subject_name")
                row_valid = False
            if not class_level_name:
                errors.append(f"Row {row_num}: Empty class_level_name")
                row_valid = False
            if not question_text:
                errors.append(f"Row {row_num}: Empty question_text")
                row_valid = False
            
            # Check question_type
            if question_type not in valid_question_types:
                errors.append(f"Row {row_num}: Invalid question_type '{question_type}'")
                row_valid = False
            
            # Check difficulty
            if difficulty not in valid_difficulties:
                errors.append(f"Row {row_num}: Invalid difficulty '{difficulty}'")
                row_valid = False
            
            # Check points is numeric
            if not points.isdigit():
                errors.append(f"Row {row_num}: Points must be numeric, got '{points}'")
                row_valid = False
            
            # Check time_limit is numeric
            if not time_limit.isdigit():
                errors.append(f"Row {row_num}: Time limit must be numeric, got '{time_limit}'")
                row_valid = False
            
            if row_valid:
                valid_rows += 1
    
    print(f"📊 VALIDATION RESULTS:")
    print(f"   📁 Total data rows: {total_rows}")
    print(f"   ✅ Valid rows: {valid_rows}")
    print(f"   ❌ Invalid rows: {len(errors)}")
    print(f"   📈 Success rate: {(valid_rows/total_rows)*100:.1f}%")
    
    if errors:
        print(f"\n🔍 SAMPLE ERRORS (first 10):")
        for error in errors[:10]:
            print(f"   {error}")
        if len(errors) > 10:
            print(f"   ... and {len(errors) - 10} more errors")
    
    return valid_rows, len(errors)

if __name__ == "__main__":
    valid_count, error_count = validate_fixed_csv()
    
    if error_count == 0:
        print(f"\n🎉 PERFECT! All {valid_count} rows are valid!")
        print("✅ Your CSV file is ready for upload!")
    else:
        print(f"\n📋 SUMMARY:")
        print(f"   • {valid_count} rows are ready for upload")
        print(f"   • {error_count} rows need manual review")
        print("   • You can upload the valid rows and fix the rest later")
