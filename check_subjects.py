#!/usr/bin/env python3
"""
Check what subjects and topics are in the clean CSV
"""
import csv
from collections import defaultdict

def analyze_subjects():
    """Analyze subjects and topics in the clean CSV"""
    
    subjects = defaultdict(lambda: defaultdict(int))
    total_questions = 0
    
    print("📊 ANALYZING CLEAN CSV CONTENT...")
    print("=" * 60)
    
    with open('Grade_9_questions_CLEAN.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # Skip header
        
        for row in reader:
            if len(row) >= 3:
                subject = row[0].strip()
                topic = row[2].strip()
                subjects[subject][topic] += 1
                total_questions += 1
    
    print(f"📈 SUMMARY:")
    print(f"   Total Questions: {total_questions}")
    print(f"   Total Subjects: {len(subjects)}")
    print()
    
    for subject, topics in subjects.items():
        subject_total = sum(topics.values())
        print(f"📚 {subject} ({subject_total} questions):")
        for topic, count in sorted(topics.items()):
            print(f"   • {topic}: {count} questions")
        print()

if __name__ == "__main__":
    analyze_subjects()
