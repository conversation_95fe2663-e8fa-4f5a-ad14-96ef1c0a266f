{% extends 'base.html' %}

{% block title %}{{ subject.name }} Quizzes - Grade {{ grade_number }} - Pentora{% endblock %}

{% block extra_css %}
<style>
    /* Mobile-first design with proper contrast */
    .topic-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        min-height: 180px;
    }

    .topic-card:hover {
        transform: translateY(-2px) scale(1.01);
        box-shadow: 0 15px 20px -5px rgba(0, 0, 0, 0.15), 0 8px 8px -5px rgba(0, 0, 0, 0.08);
    }

    /* Text clamp for descriptions */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* Cute animations */
    @keyframes bounce-in {
        0% { transform: scale(0.8); opacity: 0; }
        50% { transform: scale(1.02); }
        100% { transform: scale(1); opacity: 1; }
    }

    .topic-card {
        animation: bounce-in 0.5s ease-out;
    }

    .topic-card:nth-child(1) { animation-delay: 0.1s; }
    .topic-card:nth-child(2) { animation-delay: 0.2s; }
    .topic-card:nth-child(3) { animation-delay: 0.3s; }
    .topic-card:nth-child(4) { animation-delay: 0.4s; }
    .topic-card:nth-child(5) { animation-delay: 0.5s; }
    .topic-card:nth-child(6) { animation-delay: 0.6s; }

    /* Ensure excellent text contrast for accessibility */
    .text-white {
        color: #ffffff !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        font-weight: 700;
    }

    /* Responsive grid adjustments */
    @media (max-width: 768px) {
        .topic-card {
            border-radius: 12px;
        }

        /* Stack cards on mobile */
        .grid.lg\\:grid-cols-3 {
            grid-template-columns: 1fr;
        }
    }

    @media (min-width: 768px) and (max-width: 1024px) {
        /* 2 columns on tablet */
        .grid.lg\\:grid-cols-3 {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    /* Tooltip styles */
    [data-tooltip] {
        position: relative;
    }

    [data-tooltip]:hover::after {
        content: attr(data-tooltip);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: #1f2937;
        color: white;
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        font-size: 0.875rem;
        white-space: nowrap;
        z-index: 10;
        margin-bottom: 5px;
        animation: fadeIn 0.3s ease-out;
    }

    [data-tooltip]:hover::before {
        content: '';
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 5px solid transparent;
        border-top-color: #1f2937;
        z-index: 10;
        animation: fadeIn 0.3s ease-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    /* Header layout improvements */
    .header-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        flex-wrap: wrap;
    }

    @media (max-width: 640px) {
        .header-content {
            flex-direction: column;
            text-align: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Clean Header Section -->
    <div class="bg-white border-b border-gray-200 py-6 sm:py-8">
        <div class="container mx-auto px-3 sm:px-4">
            <!-- Back Button -->
            <div class="mb-4">
                <a href="{% url 'subjects:quiz_list' %}" class="inline-flex items-center text-gray-600 hover:text-blue-600 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Quiz Subjects
                </a>
            </div>

            <div class="text-center">
                <div class="header-content mb-4">
                    <div class="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mr-4">
                        <i class="{{ subject.icon|default:'fas fa-book' }} text-3xl text-blue-600"></i>
                    </div>
                    <div class="text-left flex-1">
                        <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">
                            {{ subject.name }} Quizzes
                        </h1>
                        <p class="text-gray-600 text-sm sm:text-base">
                            {{ class_level.name }} • {{ quiz_topics|length }} Quiz Topic{{ quiz_topics|length|pluralize }}
                        </p>
                    </div>

                    <!-- Final Exam Button in Header -->
                    {% if user.is_authenticated %}
                    <div class="ml-4 hidden sm:block">
                        {% if all_quizzes_completed %}
                            <a href="{% url 'content:exam' class_level.id %}"
                               class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-all inline-flex items-center text-sm">
                                <i class="fas fa-graduation-cap mr-2"></i>
                                Final Exam
                            </a>
                        {% else %}
                            <div class="relative">
                                <button disabled
                                        class="bg-gray-300 text-gray-500 px-4 py-2 rounded-lg font-medium cursor-not-allowed inline-flex items-center text-sm"
                                        data-tooltip="Complete {{ completed_topics_count }}/{{ total_topics_count }} quiz topics first">
                                    <i class="fas fa-lock mr-2"></i>
                                    Final Exam
                                </button>
                            </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>

                {% if subject.description %}
                <p class="text-gray-700 text-sm sm:text-base max-w-2xl mx-auto">
                    {{ subject.description }}
                </p>
                {% endif %}

                <!-- Mobile Final Exam Button -->
                {% if user.is_authenticated %}
                <div class="mt-4 sm:hidden">
                    {% if all_quizzes_completed %}
                        <a href="{% url 'content:exam' class_level.id %}"
                           class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-all inline-flex items-center text-sm">
                            <i class="fas fa-graduation-cap mr-2"></i>
                            Take Final Exam
                        </a>
                    {% else %}
                        <div class="relative inline-block">
                            <button disabled
                                    class="bg-gray-300 text-gray-500 px-6 py-2 rounded-lg font-medium cursor-not-allowed inline-flex items-center text-sm"
                                    data-tooltip="Complete {{ completed_topics_count }}/{{ total_topics_count }} quiz topics first">
                                <i class="fas fa-lock mr-2"></i>
                                Final Exam ({{ completed_topics_count }}/{{ total_topics_count }})
                            </button>
                        </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container mx-auto px-4 sm:px-6 py-6 sm:py-8">
        <div class="max-w-6xl mx-auto">

            {% if not user.is_authenticated %}
            <!-- Login Prompt for Visitors -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-2xl p-6 sm:p-8 mb-8 text-center">
                <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-info-circle text-yellow-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">Sign in to Take Quizzes</h3>
                <p class="text-gray-600 mb-4">Create a free account to take quizzes and track your progress.</p>
                <a href="{% url 'users:login' %}" class="bg-purple-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-purple-700 transition-colors inline-flex items-center">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Sign In to Continue
                </a>
            </div>
            {% endif %}

            {% if quiz_topics %}
            <!-- Quiz Topics Grid -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                {% for topic_data in quiz_topics %}
                <div class="topic-card bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 border border-gray-200 overflow-hidden {% if topic_data.is_completed %}opacity-75{% endif %}">
                    <!-- Clean Topic Header -->
                    <div class="relative">
                        <div class="p-4 bg-gray-50 border-b border-gray-100">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3 flex-1 min-w-0">
                                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-brain text-lg text-blue-600"></i>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h4 class="text-gray-900 font-bold text-base sm:text-lg truncate">{{ topic_data.topic.title }}</h4>
                                    </div>
                                </div>
                                <!-- Completion Badge -->
                                <div class="flex-shrink-0 ml-2">
                                    {% if topic_data.is_completed %}
                                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium">
                                        <i class="fas fa-check mr-1"></i>Done
                                    </span>
                                    {% else %}
                                    <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium">
                                        Quiz
                                    </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Topic Content -->
                    <div class="p-4">
                        {% if topic_data.topic.description %}
                        <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ topic_data.topic.description }}</p>
                        {% endif %}

                        <!-- Progress Section -->
                        <div class="mb-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">Progress</span>
                                <span class="text-sm text-gray-600">
                                    {% if topic_data.is_completed %}
                                        <i class="fas fa-check-circle text-green-500 mr-1"></i>
                                        Complete
                                    {% elif topic_data.progress_percentage >= 50 %}
                                        <i class="fas fa-clock text-orange-500 mr-1"></i>
                                        In Progress
                                    {% elif topic_data.progress_percentage > 0 %}
                                        <i class="fas fa-play-circle text-blue-500 mr-1"></i>
                                        Started
                                    {% else %}
                                        <i class="fas fa-circle text-gray-300 mr-1"></i>
                                        Not Started
                                    {% endif %}
                                </span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                {% if topic_data.is_completed %}
                                    <div class="bg-green-500 h-2 rounded-full transition-all duration-300" style="width: 100%"></div>
                                {% elif topic_data.progress_percentage >= 50 %}
                                    <div class="bg-orange-500 h-2 rounded-full transition-all duration-300" style="width: {{ topic_data.progress_percentage }}%"></div>
                                {% elif topic_data.progress_percentage > 0 %}
                                    <div class="bg-blue-500 h-2 rounded-full transition-all duration-300" style="width: {{ topic_data.progress_percentage }}%"></div>
                                {% else %}
                                    <div class="bg-gray-300 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                {% endif %}
                            </div>

                            <!-- Best Score Display -->
                            {% if topic_data.best_score > 0 %}
                            <div class="mt-2 text-xs text-gray-600">
                                <i class="fas fa-star text-yellow-500 mr-1"></i>
                                Best Score: {{ topic_data.best_score|floatformat:0 }}%
                            </div>
                            {% endif %}
                        </div>

                        <!-- Topic Info -->
                        <div class="flex items-center justify-between mb-4 text-xs text-gray-500">
                            {% if topic_data.topic.difficulty_level %}
                            <span class="bg-gray-100 px-2 py-1 rounded-full">
                                <i class="fas fa-signal mr-1"></i>
                                {{ topic_data.topic.get_difficulty_level_display }}
                            </span>
                            {% endif %}

                            {% if topic_data.topic.estimated_duration %}
                            <span class="bg-gray-100 px-2 py-1 rounded-full">
                                <i class="fas fa-clock mr-1"></i>
                                {{ topic_data.topic.estimated_duration }} min
                            </span>
                            {% endif %}
                        </div>

                        <!-- Action Button -->
                        {% if user.is_authenticated %}
                        <a href="{% url 'content:quiz' topic_data.topic.id %}"
                           class="w-full {% if topic_data.is_completed %}bg-green-600 hover:bg-green-700{% elif topic_data.progress_percentage > 0 %}bg-orange-600 hover:bg-orange-700{% else %}bg-blue-600 hover:bg-blue-700{% endif %} text-white py-3 px-4 rounded-lg font-semibold hover:shadow-lg transition-all flex items-center justify-center group text-sm shadow-md">
                            {% if topic_data.is_completed %}
                                <i class="fas fa-redo mr-2 group-hover:scale-110 transition-transform"></i>
                                Retake Quiz
                            {% elif topic_data.progress_percentage > 0 %}
                                <i class="fas fa-play mr-2 group-hover:scale-110 transition-transform"></i>
                                Continue Quiz
                            {% else %}
                                <i class="fas fa-play mr-2 group-hover:scale-110 transition-transform"></i>
                                Start Quiz
                            {% endif %}
                        </a>
                        {% else %}
                        <button onclick="showLoginPrompt('{{ topic_data.topic.title }}')"
                                class="w-full bg-gray-400 hover:bg-gray-500 text-white py-3 px-4 rounded-lg font-semibold flex items-center justify-center text-sm">
                            <i class="fas fa-lock mr-2"></i>
                            Sign In to Take Quiz
                        </button>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <!-- Empty State -->
            <div class="text-center py-16">
                <div class="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-brain text-purple-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">No Quizzes Available Yet</h3>
                <p class="text-gray-600 mb-6">
                    Quiz topics for {{ subject.name }} - {{ class_level.name }} will be available soon!
                </p>
                <div class="flex flex-col sm:flex-row gap-3 justify-center">
                    <a href="{% url 'subjects:quiz_list' %}" class="bg-purple-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-purple-700 transition-colors inline-flex items-center justify-center">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Quiz Subjects
                    </a>
                    <a href="{% url 'subjects:simple_learn' %}" class="bg-blue-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-blue-700 transition-colors inline-flex items-center justify-center">
                        <i class="fas fa-book mr-2"></i>
                        Study Materials Instead
                    </a>
                </div>
            </div>
            {% endif %}

            <!-- Quick Actions -->
            <div class="mt-8 bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold text-gray-800 mb-4 text-center">
                    <i class="fas fa-bolt text-yellow-500 mr-2"></i>
                    Quick Actions
                </h3>
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-3">
                    <a href="{% url 'subjects:simple_learn' %}"
                       class="bg-gradient-to-r from-blue-500 to-cyan-500 text-white p-3 rounded-lg text-center hover:from-blue-600 hover:to-cyan-600 transition-all">
                        <i class="fas fa-book-open text-lg mb-1"></i>
                        <div class="font-semibold text-sm">Study Materials</div>
                        <div class="text-xs opacity-90">Learn first</div>
                    </a>
                    <a href="{% url 'core:dashboard' %}"
                       class="bg-gradient-to-r from-green-500 to-emerald-500 text-white p-3 rounded-lg text-center hover:from-green-600 hover:to-emerald-600 transition-all">
                        <i class="fas fa-chart-line text-lg mb-1"></i>
                        <div class="font-semibold text-sm">View Progress</div>
                        <div class="text-xs opacity-90">Check stats</div>
                    </a>
                    <a href="{% url 'subjects:quiz_list' %}"
                       class="bg-gradient-to-r from-purple-500 to-pink-500 text-white p-3 rounded-lg text-center hover:from-purple-600 hover:to-pink-600 transition-all">
                        <i class="fas fa-brain text-lg mb-1"></i>
                        <div class="font-semibold text-sm">Other Subjects</div>
                        <div class="text-xs opacity-90">More quizzes</div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Login Prompt Modal -->
{% if not user.is_authenticated %}
<div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg max-w-md w-full p-6">
        <div class="text-center">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-graduation-cap text-blue-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-2">Ready to test your knowledge?</h3>
            <p class="text-gray-600 mb-6" id="quizTopicName">Sign in to take this quiz and track your progress!</p>

            <div class="flex gap-3 justify-center">
                <a href="{% url 'users:register' %}" class="btn bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 rounded-lg">
                    <i class="fas fa-user-plus mr-2"></i>
                    Create Free Account
                </a>
                <a href="{% url 'users:login' %}" class="btn border border-gray-300 text-gray-600 hover:bg-gray-50 px-4 py-2 rounded-lg">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Sign In
                </a>
            </div>

            <button onclick="closeLoginPrompt()" class="mt-4 text-gray-500 hover:text-gray-700 text-sm">
                Maybe later
            </button>
        </div>
    </div>
</div>
{% endif %}

<script>
// Login modal functions
function showLoginPrompt(topicName) {
    {% if not user.is_authenticated %}
    document.getElementById('quizTopicName').textContent = `Sign in to take the "${topicName}" quiz and track your progress!`;
    document.getElementById('loginModal').classList.remove('hidden');
    {% endif %}
}

function closeLoginPrompt() {
    {% if not user.is_authenticated %}
    document.getElementById('loginModal').classList.add('hidden');
    {% endif %}
}
</script>
{% endblock %}
