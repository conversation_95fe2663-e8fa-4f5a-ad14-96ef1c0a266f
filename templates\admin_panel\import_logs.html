{% extends 'admin_panel/base.html' %}

{% block title %}Logs{% endblock %}
{% block page_title %}System Logs{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">{{ stats.total_imports }}</h4>
                        <p class="mb-0">Total Imports</p>
                    </div>
                    <i class="fas fa-file-import fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">{{ stats.successful_imports }}</h4>
                        <p class="mb-0">Successful</p>
                    </div>
                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">{{ stats.partial_imports }}</h4>
                        <p class="mb-0">Partial</p>
                    </div>
                    <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">{{ stats.failed_imports }}</h4>
                        <p class="mb-0">Failed</p>
                    </div>
                    <i class="fas fa-times-circle fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Actions -->
<div class="card mb-4">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>
                Filter Import Logs
            </h5>
            <div>
                <a href="{% url 'admin_panel:csv_import' %}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus me-2"></i>
                    New Import
                </a>
            </div>
        </div>
    </div>
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="status" class="form-label">Status</label>
                <select name="status" id="status" class="form-select">
                    <option value="">All Statuses</option>
                    <option value="completed" {% if current_status_filter == 'completed' %}selected{% endif %}>Completed</option>
                    <option value="partial" {% if current_status_filter == 'partial' %}selected{% endif %}>Partial</option>
                    <option value="failed" {% if current_status_filter == 'failed' %}selected{% endif %}>Failed</option>
                    <option value="processing" {% if current_status_filter == 'processing' %}selected{% endif %}>Processing</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="import_type" class="form-label">Import Type</label>
                <select name="import_type" id="import_type" class="form-select">
                    <option value="">All Types</option>
                    <option value="questions" {% if current_import_type_filter == 'questions' %}selected{% endif %}>Questions</option>
                    <option value="study_notes" {% if current_import_type_filter == 'study_notes' %}selected{% endif %}>Study Notes</option>
                    <option value="subjects" {% if current_import_type_filter == 'subjects' %}selected{% endif %}>Subjects</option>
                    <option value="topics" {% if current_import_type_filter == 'topics' %}selected{% endif %}>Topics</option>
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search me-2"></i>
                    Filter
                </button>
                <a href="{% url 'admin_panel:import_logs' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i>
                    Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Import Logs Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Import History
        </h5>
    </div>
    <div class="card-body">
        {% if logs %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Import Type</th>
                        <th>File Name</th>
                        <th>Status</th>
                        <th>Progress</th>
                        <th>Started</th>
                        <th>Imported By</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in logs %}
                    <tr>
                        <td>
                            <span class="badge bg-info">{{ log.get_import_type_display }}</span>
                        </td>
                        <td>
                            <strong>{{ log.file_name }}</strong>
                        </td>
                        <td>
                            {% if log.status == 'completed' %}
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>
                                    Completed
                                </span>
                            {% elif log.status == 'partial' %}
                                <span class="badge bg-warning">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    Partial
                                </span>
                            {% elif log.status == 'failed' %}
                                <span class="badge bg-danger">
                                    <i class="fas fa-times me-1"></i>
                                    Failed
                                </span>
                            {% elif log.status == 'processing' %}
                                <span class="badge bg-primary">
                                    <i class="fas fa-spinner fa-spin me-1"></i>
                                    Processing
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">{{ log.get_status_display }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="progress me-2" style="width: 100px; height: 8px;">
                                    <div class="progress-bar
                                        {% if log.status == 'completed' %}bg-success
                                        {% elif log.status == 'partial' %}bg-warning
                                        {% elif log.status == 'failed' %}bg-danger
                                        {% else %}bg-primary{% endif %}"
                                        style="width: {{ log.success_rate }}%"></div>
                                </div>
                                <small class="text-muted">
                                    {{ log.successful_rows }}/{{ log.total_rows }}
                                    {% if log.total_rows > 0 %}({{ log.success_rate|floatformat:1 }}%){% endif %}
                                </small>
                            </div>
                        </td>
                        <td>
                            <small>
                                {{ log.started_at|date:"M d, Y H:i" }}
                            </small>
                        </td>
                        <td>
                            {% if log.imported_by %}
                                {{ log.imported_by.full_name }}
                            {% else %}
                                <em class="text-muted">Unknown</em>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'admin_panel:import_log_detail' log.id %}"
                                   class="btn btn-outline-primary" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button type="button" class="btn btn-outline-danger"
                                        onclick="deleteLog('{{ log.id }}', '{{ log.file_name }}')"
                                        title="Delete Log">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
        <nav aria-label="Import logs pagination" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if current_status_filter %}&status={{ current_status_filter }}{% endif %}{% if current_import_type_filter %}&import_type={{ current_import_type_filter }}{% endif %}">First</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if current_status_filter %}&status={{ current_status_filter }}{% endif %}{% if current_import_type_filter %}&import_type={{ current_import_type_filter }}{% endif %}">Previous</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">
                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                    </span>
                </li>

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if current_status_filter %}&status={{ current_status_filter }}{% endif %}{% if current_import_type_filter %}&import_type={{ current_import_type_filter }}{% endif %}">Next</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if current_status_filter %}&status={{ current_status_filter }}{% endif %}{% if current_import_type_filter %}&import_type={{ current_import_type_filter }}{% endif %}">Last</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-file-import fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No import logs found</h5>
            <p class="text-muted">Start by importing some data to see logs here.</p>
            <a href="{% url 'admin_panel:csv_import' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Start Import
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Import Log</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the import log for <strong id="deleteFileName"></strong>?</p>
                <p class="text-muted small">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post" id="deleteForm" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Delete Log</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function deleteLog(logId, fileName) {
    document.getElementById('deleteFileName').textContent = fileName;
    document.getElementById('deleteForm').action = `/my-admin/logs/${logId}/delete/`;

    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// Auto-refresh for processing logs
document.addEventListener('DOMContentLoaded', function() {
    // Find badges that contain "Processing" text
    const badges = document.querySelectorAll('.badge');
    const processingLogs = Array.from(badges).filter(badge =>
        badge.textContent.trim().includes('Processing')
    );

    if (processingLogs.length > 0) {
        setTimeout(function() {
            window.location.reload();
        }, 10000); // Refresh every 10 seconds if there are processing logs
    }
});
</script>
{% endblock %}
