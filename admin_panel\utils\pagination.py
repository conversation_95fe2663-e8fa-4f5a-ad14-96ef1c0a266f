"""
Optimized pagination utilities for better performance with large datasets
"""

from django.core.cache import cache
from django.core.paginator import Paginator
from django.db.models import QuerySet
import hashlib


class OptimizedPaginator:
    """
    High-performance paginator that avoids expensive COUNT operations
    and uses caching for better performance with large datasets.
    """
    
    def __init__(self, queryset, per_page, cache_timeout=300):
        self.queryset = queryset
        self.per_page = per_page
        self.cache_timeout = cache_timeout
        
    def get_cache_key(self, page_number, filters=None):
        """Generate cache key for pagination results"""
        query_hash = hashlib.md5(str(self.queryset.query).encode()).hexdigest()
        filter_hash = hashlib.md5(str(filters or {}).encode()).hexdigest()
        return f"pagination_{query_hash}_{filter_hash}_{page_number}_{self.per_page}"
    
    def get_page(self, page_number, filters=None):
        """Get page with caching and optimized queries"""
        page_number = int(page_number or 1)
        cache_key = self.get_cache_key(page_number, filters)
        
        # Try to get from cache first
        cached_result = cache.get(cache_key)
        if cached_result:
            return cached_result
        
        # Calculate offset
        offset = (page_number - 1) * self.per_page
        
        # Get one extra item to check if there's a next page
        items = list(self.queryset[offset:offset + self.per_page + 1])
        
        has_next = len(items) > self.per_page
        if has_next:
            items = items[:-1]  # Remove the extra item
        
        has_previous = page_number > 1
        
        # Create page object
        page = OptimizedPage(
            object_list=items,
            number=page_number,
            has_next=has_next,
            has_previous=has_previous,
            per_page=self.per_page
        )
        
        # Cache the result
        cache.set(cache_key, page, self.cache_timeout)
        
        return page


class OptimizedPage:
    """
    Page object that mimics Django's Page but without expensive operations
    """

    def __init__(self, object_list, number, has_next, has_previous, per_page):
        self.object_list = object_list
        self.number = number
        self.has_next = has_next
        self.has_previous = has_previous
        self.per_page = per_page
        self.has_other_pages = has_next or has_previous

    def __iter__(self):
        """Make the page object iterable like Django's Page"""
        return iter(self.object_list)

    def __len__(self):
        """Return the number of objects on this page"""
        return len(self.object_list)

    def __getitem__(self, index):
        """Allow indexing into the page object"""
        return self.object_list[index]

    def previous_page_number(self):
        """Return the previous page number"""
        return self.number - 1 if self.has_previous else None

    def next_page_number(self):
        """Return the next page number"""
        return self.number + 1 if self.has_next else None

    def start_index(self):
        """Return the 1-based index of the first object on this page"""
        return (self.number - 1) * self.per_page + 1

    def end_index(self):
        """Return the 1-based index of the last object on this page"""
        return self.start_index() + len(self.object_list) - 1


class CachedCountPaginator(Paginator):
    """
    Paginator that caches the count operation for better performance
    """
    
    def __init__(self, object_list, per_page, cache_timeout=600, **kwargs):
        super().__init__(object_list, per_page, **kwargs)
        self.cache_timeout = cache_timeout
        
    @property
    def count(self):
        """Get count with caching"""
        if hasattr(self, '_cached_count'):
            return self._cached_count
            
        # Generate cache key based on query
        if isinstance(self.object_list, QuerySet):
            query_hash = hashlib.md5(str(self.object_list.query).encode()).hexdigest()
            cache_key = f"paginator_count_{query_hash}"
            
            cached_count = cache.get(cache_key)
            if cached_count is not None:
                self._cached_count = cached_count
                return cached_count
                
            # Calculate count and cache it
            count = super().count
            cache.set(cache_key, count, self.cache_timeout)
            self._cached_count = count
            return count
        else:
            return super().count


def get_optimized_page_range(page_number, total_pages, window=5):
    """
    Get optimized page range for pagination display
    """
    page_number = int(page_number)
    
    if total_pages <= window:
        return list(range(1, total_pages + 1))
    
    # Calculate start and end of the window
    start = max(1, page_number - window // 2)
    end = min(total_pages, start + window - 1)
    
    # Adjust start if we're near the end
    if end - start < window - 1:
        start = max(1, end - window + 1)
    
    return list(range(start, end + 1))


def cache_invalidation_key(model_name, filters=None):
    """
    Generate cache invalidation key for model changes
    """
    filter_hash = hashlib.md5(str(filters or {}).encode()).hexdigest()
    return f"cache_invalidation_{model_name}_{filter_hash}"


def invalidate_pagination_cache(model_name, filters=None):
    """
    Invalidate pagination cache when data changes
    """
    # This would be called when questions are added/deleted/modified
    cache_key = cache_invalidation_key(model_name, filters)
    cache.delete(cache_key)
    
    # Also delete related cache keys
    # In a real implementation, you'd track cache keys more systematically
    pass
