#!/usr/bin/env python3
"""
Quick validation of the final CSV file
"""
import csv

def quick_validation():
    """Quick validation of correct_answer format"""
    
    print("🔍 QUICK VALIDATION OF FINAL CSV...")
    print("=" * 50)
    
    valid_mc_answers = ['a', 'b', 'c', 'd']
    valid_tf_answers = ['true', 'false']
    
    total_questions = 0
    valid_answers = 0
    invalid_answers = []
    
    with open('Grade_9_questions_FINAL.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # Skip header
        
        for row_num, row in enumerate(reader, 2):
            total_questions += 1
            
            if len(row) >= 6:
                question_type = row[4].strip().lower()
                correct_answer = row[5].strip().lower()
                
                is_valid = False
                
                if question_type == 'multiple_choice':
                    if correct_answer in valid_mc_answers:
                        is_valid = True
                    else:
                        invalid_answers.append(f"Row {row_num}: MC answer '{correct_answer}' (should be a,b,c,d)")
                
                elif question_type == 'true_false':
                    if correct_answer in valid_tf_answers:
                        is_valid = True
                    else:
                        invalid_answers.append(f"Row {row_num}: T/F answer '{correct_answer}' (should be true/false)")
                
                elif question_type in ['short_answer', 'fill_blank']:
                    is_valid = True  # These can have any text
                
                if is_valid:
                    valid_answers += 1
    
    print(f"📊 RESULTS:")
    print(f"   Total questions: {total_questions}")
    print(f"   ✅ Valid answers: {valid_answers}")
    print(f"   ❌ Invalid answers: {len(invalid_answers)}")
    print(f"   📈 Success rate: {(valid_answers/total_questions)*100:.1f}%")
    
    if invalid_answers:
        print(f"\n❌ INVALID ANSWERS (first 10):")
        for error in invalid_answers[:10]:
            print(f"   {error}")
        if len(invalid_answers) > 10:
            print(f"   ... and {len(invalid_answers) - 10} more")
    else:
        print(f"\n🎉 ALL ANSWERS ARE PROPERLY FORMATTED!")
        print(f"✅ File is ready for upload!")

if __name__ == "__main__":
    quick_validation()
