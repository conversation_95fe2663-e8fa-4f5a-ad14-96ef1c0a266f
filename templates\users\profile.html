{% extends 'base.html' %}

{% block title %}Profile - Pentora{% endblock %}

{% block content %}
<div class="min-h-screen bg-base-200">
    <div class="container mx-auto px-3 py-4 max-w-6xl">
        <!-- Mobile-First Header -->
        <div class="mb-6">
            <div class="flex flex-col space-y-3">
                <h1 class="text-2xl md:text-3xl font-bold text-center md:text-left">My Profile</h1>
                <p class="text-base-content/70 text-center md:text-left text-sm md:text-base">Manage your account settings and preferences</p>
                <div class="flex justify-center md:justify-start">
                    <button class="btn btn-primary btn-sm md:btn-md w-full max-w-xs md:w-auto" onclick="toggleEditMode()">
                        <i class="fas fa-edit mr-2"></i>
                        <span id="editButtonText">Edit Profile</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile-First Layout -->
        <div class="flex flex-col lg:grid lg:grid-cols-3 gap-4 md:gap-6">
            <!-- Compact Profile Card -->
            <div class="lg:col-span-1 order-1 lg:order-1">
                <div class="card bg-base-100 shadow-lg">
                    <div class="card-body p-3 md:p-4 text-center">
                        <!-- Compact Profile Picture -->
                        <div class="avatar mb-3">
                            <div class="w-16 md:w-20 rounded-full ring ring-primary ring-offset-base-100 ring-offset-1">
                                {% if user.profile_picture %}
                                    <img src="{{ user.profile_picture.url }}" alt="Profile Picture" />
                                {% else %}
                                    <div class="bg-primary text-primary-content w-16 h-16 md:w-20 md:h-20 rounded-full flex items-center justify-center text-lg md:text-xl font-bold">
                                        {{ user.first_name|first }}{{ user.last_name|first }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Name and Email -->
                        <div class="mb-3">
                            <h2 class="text-base md:text-lg font-bold">{{ user.full_name }}</h2>
                            <p class="text-xs md:text-sm text-base-content/70 break-all">{{ user.email }}</p>
                        </div>

                        <!-- Compact Badges -->
                        <div class="flex flex-wrap justify-center gap-1 mb-3">
                            <!-- Current Class Level -->
                            {% if user.current_class_level %}
                                <div class="badge badge-primary badge-sm gap-1">
                                    <i class="fas fa-graduation-cap text-xs"></i>
                                    <span class="text-xs">{{ user.get_current_class_level_display }}</span>
                                </div>
                            {% else %}
                                <div class="badge badge-neutral badge-sm gap-1">
                                    <i class="fas fa-user-graduate text-xs"></i>
                                    <span class="text-xs">No Grade</span>
                                </div>
                            {% endif %}

                            <!-- Email Verification Status -->
                            {% if user.is_email_verified %}
                                <div class="badge badge-success badge-sm gap-1">
                                    <i class="fas fa-check-circle text-xs"></i>
                                    <span class="text-xs">Verified</span>
                                </div>
                            {% else %}
                                <div class="badge badge-warning badge-sm gap-1">
                                    <i class="fas fa-exclamation-triangle text-xs"></i>
                                    <span class="text-xs">Not Verified</span>
                                </div>
                            {% endif %}

                            <!-- Gender -->
                            {% if user.gender %}
                                <div class="badge badge-info badge-sm gap-1">
                                    <i class="fas fa-venus-mars text-xs"></i>
                                    <span class="text-xs">{{ user.get_gender_display }}</span>
                                </div>
                            {% endif %}

                            <!-- Country -->
                            {% if user.country %}
                                <div class="badge badge-secondary badge-sm gap-1">
                                    <i class="fas fa-globe text-xs"></i>
                                    <span class="text-xs">{{ user.get_country_display }}</span>
                                </div>
                            {% endif %}
                        </div>

                        <!-- Member Since -->
                        <div class="text-xs text-base-content/60">
                            Member since {{ user.date_joined|date:"M Y" }}
                        </div>
                    </div>
                </div>

                <!-- Compact Learning Stats -->
                <div class="card bg-base-100 shadow-lg mt-3">
                    <div class="card-body p-3 md:p-4">
                        <h3 class="text-sm md:text-base font-bold mb-3 text-center">📊 Learning Stats</h3>
                        <div class="grid grid-cols-3 gap-2">
                            <div class="text-center bg-base-200 rounded-lg p-2">
                                <div class="text-xs text-base-content/70">Topics</div>
                                <div class="text-primary text-lg font-bold" id="levels-completed">0</div>
                            </div>
                            <div class="text-center bg-base-200 rounded-lg p-2">
                                <div class="text-xs text-base-content/70">Time</div>
                                <div class="text-secondary text-lg font-bold" id="study-time">0h</div>
                            </div>
                            <div class="text-center bg-base-200 rounded-lg p-2">
                                <div class="text-xs text-base-content/70">Awards</div>
                                <div class="text-accent text-lg font-bold" id="achievements">0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Details -->
            <div class="lg:col-span-2 order-2 lg:order-2">
                <div class="space-y-4 md:space-y-6">
                    <!-- Personal Information -->
                    <div class="card bg-base-100 shadow-lg">
                        <div class="card-body p-4 md:p-6">
                            <h3 class="card-title text-base md:text-lg mb-4">Personal Information</h3>
                            <form id="profileForm" method="post" action="{% url 'users:update_profile' %}">
                                {% csrf_token %}
                                <div class="space-y-4">
                                    <!-- First and Last Name Row -->
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="label">
                                                <span class="label-text font-semibold text-sm md:text-base">First Name</span>
                                            </label>
                                            <input type="text" name="first_name" value="{{ user.first_name }}"
                                                   class="input input-bordered w-full profile-input text-sm md:text-base" readonly>
                                        </div>
                                        <div>
                                            <label class="label">
                                                <span class="label-text font-semibold text-sm md:text-base">Last Name</span>
                                            </label>
                                            <input type="text" name="last_name" value="{{ user.last_name }}"
                                                   class="input input-bordered w-full profile-input text-sm md:text-base" readonly>
                                        </div>
                                    </div>

                                    <!-- Email Address -->
                                    <div>
                                        <label class="label">
                                            <span class="label-text font-semibold text-sm md:text-base">Email Address</span>
                                        </label>
                                        <input type="email" name="email" value="{{ user.email }}"
                                               class="input input-bordered w-full profile-input text-sm md:text-base break-all" readonly>
                                    </div>

                                    <!-- Username -->
                                    <div>
                                        <label class="label">
                                            <span class="label-text font-semibold text-sm md:text-base">Username</span>
                                            <span class="label-text-alt text-xs">(Optional - for easy login)</span>
                                        </label>
                                        <input type="text" name="username" value="{{ user.username|default:'' }}"
                                               placeholder="Choose a username for easy login"
                                               class="input input-bordered w-full profile-input text-sm md:text-base" readonly>
                                        <div class="label">
                                            <span class="label-text-alt text-xs text-base-content/60">
                                                If set, you can login with either your username or email address
                                            </span>
                                        </div>
                                    </div>

                                    <!-- Phone Number -->
                                    <div>
                                        <label class="label">
                                            <span class="label-text font-semibold text-sm md:text-base">Phone Number</span>
                                            <span class="label-text-alt text-xs">(Optional)</span>
                                        </label>
                                        <input type="tel" name="phone_number" value="{{ user.phone_number|default:'' }}"
                                               placeholder="Enter your phone number"
                                               class="input input-bordered w-full profile-input text-sm md:text-base" readonly>
                                    </div>

                                    <!-- Grade and Date of Birth Row -->
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="label">
                                                <span class="label-text font-semibold text-sm md:text-base">Current Grade</span>
                                            </label>
                                            <select name="current_class_level" class="select select-bordered w-full profile-input text-sm md:text-base" disabled>
                                                <option value="">Select Grade</option>
                                                {% for value, label in user.CLASS_LEVEL_CHOICES %}
                                                    <option value="{{ value }}" {% if user.current_class_level == value %}selected{% endif %}>
                                                        {{ label }}
                                                    </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div>
                                            <label class="label">
                                                <span class="label-text font-semibold text-sm md:text-base">Date of Birth</span>
                                                <span class="label-text-alt text-xs">(Optional)</span>
                                            </label>
                                            <input type="date" name="date_of_birth" value="{{ user.date_of_birth|date:'Y-m-d'|default:'' }}"
                                                   class="input input-bordered w-full profile-input text-sm md:text-base" readonly>
                                        </div>
                                    </div>

                                    <!-- Gender and Country Row -->
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
                                        <div>
                                            <label class="label">
                                                <span class="label-text font-semibold text-sm md:text-base">Gender</span>
                                                <span class="label-text-alt text-xs">(Optional)</span>
                                            </label>
                                            <select name="gender" class="select select-bordered w-full profile-input text-sm md:text-base" disabled>
                                                <option value="">Select Gender</option>
                                                <option value="M" {% if user.gender == 'M' %}selected{% endif %}>Male</option>
                                                <option value="F" {% if user.gender == 'F' %}selected{% endif %}>Female</option>
                                                <option value="O" {% if user.gender == 'O' %}selected{% endif %}>Other</option>
                                                <option value="P" {% if user.gender == 'P' %}selected{% endif %}>Prefer not to say</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="label">
                                                <span class="label-text font-semibold text-sm md:text-base">Country</span>
                                                <span class="label-text-alt text-xs">(Optional)</span>
                                            </label>
                                            <select name="country" class="select select-bordered w-full profile-input text-sm md:text-base" disabled>
                                                <option value="">Select Country</option>
                                                <option value="GH" {% if user.country == 'GH' %}selected{% endif %}>Ghana</option>
                                                <option value="NG" {% if user.country == 'NG' %}selected{% endif %}>Nigeria</option>
                                                <option value="KE" {% if user.country == 'KE' %}selected{% endif %}>Kenya</option>
                                                <option value="ZA" {% if user.country == 'ZA' %}selected{% endif %}>South Africa</option>
                                                <option value="EG" {% if user.country == 'EG' %}selected{% endif %}>Egypt</option>
                                                <option value="MA" {% if user.country == 'MA' %}selected{% endif %}>Morocco</option>
                                                <option value="TZ" {% if user.country == 'TZ' %}selected{% endif %}>Tanzania</option>
                                                <option value="UG" {% if user.country == 'UG' %}selected{% endif %}>Uganda</option>
                                                <option value="ZW" {% if user.country == 'ZW' %}selected{% endif %}>Zimbabwe</option>
                                                <option value="BW" {% if user.country == 'BW' %}selected{% endif %}>Botswana</option>
                                                <option value="US" {% if user.country == 'US' %}selected{% endif %}>United States</option>
                                                <option value="GB" {% if user.country == 'GB' %}selected{% endif %}>United Kingdom</option>
                                                <option value="CA" {% if user.country == 'CA' %}selected{% endif %}>Canada</option>
                                                <option value="AU" {% if user.country == 'AU' %}selected{% endif %}>Australia</option>
                                                <option value="DE" {% if user.country == 'DE' %}selected{% endif %}>Germany</option>
                                                <option value="FR" {% if user.country == 'FR' %}selected{% endif %}>France</option>
                                                <option value="IN" {% if user.country == 'IN' %}selected{% endif %}>India</option>
                                                <option value="CN" {% if user.country == 'CN' %}selected{% endif %}>China</option>
                                                <option value="JP" {% if user.country == 'JP' %}selected{% endif %}>Japan</option>
                                                <option value="BR" {% if user.country == 'BR' %}selected{% endif %}>Brazil</option>
                                                <option value="OTHER" {% if user.country == 'OTHER' %}selected{% endif %}>Other</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Save/Cancel buttons (hidden by default) -->
                                <div id="editActions" class="flex flex-col md:flex-row gap-3 mt-6 hidden">
                                    <button type="submit" class="btn btn-primary btn-sm md:btn-md flex-1">
                                        <i class="fas fa-save mr-2"></i>
                                        Save Changes
                                    </button>
                                    <button type="button" class="btn btn-outline btn-sm md:btn-md flex-1" onclick="cancelEdit()">
                                        <i class="fas fa-times mr-2"></i>
                                        Cancel
                                    </button>
                                </div>
                        </form>
                    </div>
                </div>

                <!-- Learning Preferences -->
                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h3 class="card-title">Learning Preferences</h3>
                        <div class="space-y-4 mt-4">
                            <div>
                                <label class="label">
                                    <span class="label-text font-semibold">Preferred Language</span>
                                </label>
                                <select class="select select-bordered w-full" disabled>
                                    <option>English</option>
                                </select>
                            </div>
                            <div>
                                <label class="label">
                                    <span class="label-text font-semibold">Learning Goals</span>
                                </label>
                                <textarea class="textarea textarea-bordered w-full h-24" readonly>{{ user.learning_goals|default:'No goals set yet' }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subscription Status -->
                {% if billing_enabled %}
                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h3 class="card-title">
                            <i class="fas fa-crown mr-2"></i>
                            Subscription Status
                        </h3>
                        <div class="mt-4">
                            {% if user_subscription.is_active %}
                                <div class="alert alert-success">
                                    <div class="flex items-center">
                                        <i class="fas fa-check-circle text-xl mr-3"></i>
                                        <div>
                                            <h4 class="font-semibold">{{ user_subscription.plan.name }} Plan Active</h4>
                                            <p class="text-sm opacity-75">
                                                Valid until {{ user_subscription.current_period_end|date:"F d, Y" }}
                                                • {{ user_subscription.billing_cycle|title }} billing
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex gap-3 mt-4">
                                    <a href="{% url 'billing:dashboard' %}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-cog mr-2"></i>
                                        Manage Subscription
                                    </a>
                                    <a href="{% url 'billing:plans' %}" class="btn btn-outline btn-sm">
                                        <i class="fas fa-arrow-up mr-2"></i>
                                        Upgrade Plan
                                    </a>
                                </div>
                            {% else %}
                                <div class="alert alert-warning">
                                    <div class="flex items-center">
                                        <i class="fas fa-exclamation-triangle text-xl mr-3"></i>
                                        <div>
                                            <h4 class="font-semibold">Free Plan</h4>
                                            <p class="text-sm opacity-75">
                                                Upgrade to unlock all subjects and features
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex gap-3 mt-4">
                                    <a href="{% url 'billing:plans' %}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-crown mr-2"></i>
                                        View Plans
                                    </a>
                                    <a href="{% url 'billing:dashboard' %}" class="btn btn-outline btn-sm">
                                        <i class="fas fa-chart-line mr-2"></i>
                                        Billing Dashboard
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Account Settings -->
                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h3 class="card-title">Account Settings</h3>
                        <div class="space-y-4 mt-4">
                            {% if not user.is_email_verified %}
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <div>
                                    <h4 class="font-bold">Email Verification Required</h4>
                                    <div class="text-xs">Please verify your email to access all features.</div>
                                </div>
                                <div>
                                    <a href="{% url 'users:resend_verification' %}" class="btn btn-sm btn-warning">
                                        Resend Verification
                                    </a>
                                </div>
                            </div>
                            {% else %}
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                <div>
                                    <h4 class="font-bold">Email Verified</h4>
                                    <div class="text-xs">Your email address has been successfully verified.</div>
                                </div>
                            </div>
                            {% endif %}

                            <div class="flex flex-col sm:flex-row gap-4">
                                <button class="btn btn-outline flex-1">
                                    <i class="fas fa-key mr-2"></i>
                                    Change Password
                                </button>
                                <button class="btn btn-outline btn-error flex-1">
                                    <i class="fas fa-trash mr-2"></i>
                                    Delete Account
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let isEditMode = false;

function toggleEditMode() {
    isEditMode = !isEditMode;
    const inputs = document.querySelectorAll('.profile-input');
    const editActions = document.getElementById('editActions');
    const editButtonText = document.getElementById('editButtonText');

    if (isEditMode) {
        // Enable edit mode
        inputs.forEach(input => {
            if (input.tagName === 'SELECT') {
                input.disabled = false;
            } else {
                input.readOnly = false;
            }
            input.classList.add('input-primary');
        });
        editActions.classList.remove('hidden');
        editButtonText.textContent = 'Cancel Edit';
    } else {
        // Disable edit mode
        inputs.forEach(input => {
            if (input.tagName === 'SELECT') {
                input.disabled = true;
            } else {
                input.readOnly = true;
            }
            input.classList.remove('input-primary');
        });
        editActions.classList.add('hidden');
        editButtonText.textContent = 'Edit Profile';
    }
}

function cancelEdit() {
    // Reset form to original values
    document.getElementById('profileForm').reset();
    toggleEditMode();
}

// Load real stats on page load
document.addEventListener('DOMContentLoaded', function() {
    loadUserStats();
});

function loadUserStats() {
    fetch('/api/user/stats/')
        .then(response => response.json())
        .then(data => {
            // Update stats if API is available
            if (data.levels_completed !== undefined) {
                document.getElementById('levels-completed').textContent = data.levels_completed;
            }
            if (data.study_time !== undefined) {
                document.getElementById('study-time').textContent = data.study_time;
            }
            if (data.achievements !== undefined) {
                document.getElementById('achievements').textContent = data.achievements;
            }
        })
        .catch(error => {
            console.log('Stats API not available, using default values');
        });
}
</script>
{% endblock %}
