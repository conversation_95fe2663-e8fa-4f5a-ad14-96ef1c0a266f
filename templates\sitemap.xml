<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
        http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">

    <!-- High Priority Pages -->
    {% for page in high_priority_urls %}
    <url>
        <loc>{{ domain }}{{ page.url }}</loc>
        <lastmod>{{ current_date|date:"Y-m-d" }}</lastmod>
        <changefreq>{{ page.changefreq }}</changefreq>
        <priority>{{ page.priority }}</priority>
    </url>
    {% endfor %}

    <!-- Medium Priority Pages -->
    {% for page in medium_priority_urls %}
    <url>
        <loc>{{ domain }}{{ page.url }}</loc>
        <lastmod>{{ current_date|date:"Y-m-d" }}</lastmod>
        <changefreq>{{ page.changefreq }}</changefreq>
        <priority>{{ page.priority }}</priority>
    </url>
    {% endfor %}

    <!-- Dynamic Subject Pages -->
    {% for subject in subjects %}
    {% if subject.slug %}
    <url>
        <loc>{{ domain }}/subjects/{{ subject.slug }}/</loc>
        <lastmod>{% if subject.updated_at %}{{ subject.updated_at|date:"Y-m-d" }}{% else %}{{ current_date|date:"Y-m-d" }}{% endif %}</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
    </url>
    {% endif %}
    {% endfor %}

    <!-- Dynamic Grade/Subject Pages -->
    {% for level in class_levels %}
    {% if level.level_number and level.subject and level.subject.slug %}
    <url>
        <loc>{{ domain }}/subjects/grade-{{ level.level_number }}/{{ level.subject.slug }}/</loc>
        <lastmod>{% if level.updated_at %}{{ level.updated_at|date:"Y-m-d" }}{% else %}{{ current_date|date:"Y-m-d" }}{% endif %}</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.7</priority>
    </url>
    {% endif %}
    {% endfor %}

    <!-- Dynamic Topic Pages -->
    {% for topic in topics %}
    {% if topic.slug and topic.class_level and topic.class_level.subject and topic.class_level.subject.slug %}
    <url>
        <loc>{{ domain }}/subjects/grade-{{ topic.class_level.level_number }}/{{ topic.class_level.subject.slug }}/{{ topic.slug }}/</loc>
        <lastmod>{% if topic.updated_at %}{{ topic.updated_at|date:"Y-m-d" }}{% else %}{{ current_date|date:"Y-m-d" }}{% endif %}</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.6</priority>
    </url>
    {% endif %}
    {% endfor %}

</urlset>
