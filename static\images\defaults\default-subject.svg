<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="subjectGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="100" cy="100" r="100" fill="url(#subjectGradient)"/>
  
  <!-- Book icon -->
  <g fill="white" opacity="0.9">
    <!-- Book cover -->
    <rect x="60" y="50" width="80" height="100" rx="4" ry="4"/>
    <!-- Book spine -->
    <rect x="60" y="50" width="8" height="100" fill="rgba(255,255,255,0.3)"/>
    <!-- Pages -->
    <rect x="75" y="65" width="50" height="3" fill="rgba(0,0,0,0.1)"/>
    <rect x="75" y="75" width="45" height="3" fill="rgba(0,0,0,0.1)"/>
    <rect x="75" y="85" width="50" height="3" fill="rgba(0,0,0,0.1)"/>
    <rect x="75" y="95" width="40" height="3" fill="rgba(0,0,0,0.1)"/>
    <rect x="75" y="105" width="50" height="3" fill="rgba(0,0,0,0.1)"/>
    <rect x="75" y="115" width="35" height="3" fill="rgba(0,0,0,0.1)"/>
    <rect x="75" y="125" width="45" height="3" fill="rgba(0,0,0,0.1)"/>
  </g>
  
  <!-- Subtle border -->
  <circle cx="100" cy="100" r="98" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
</svg>
