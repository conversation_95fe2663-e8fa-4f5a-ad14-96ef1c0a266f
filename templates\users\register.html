{% extends 'base.html' %}

{% block title %}Join <PERSON> - Start Learning Today{% endblock %}

{% block extra_css %}
<style>
    .register-container {
        background: linear-gradient(135deg, #F9FAFB 0%, #E5E7EB 100%);
        min-height: calc(100vh - 80px);
    }

    .register-card {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        border: 1px solid #E5E7EB;
        max-width: 500px;
        margin: 0 auto;
    }

    .form-input {
        border: 2px solid #E5E7EB;
        border-radius: 0.5rem;
        padding: 0.625rem;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        background: #F9FAFB;
    }

    .form-input:focus {
        border-color: var(--primary-blue);
        background: white;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        outline: none;
    }

    .form-label {
        font-weight: 600;
        color: var(--text-dark);
        margin-bottom: 0.25rem;
        font-size: 0.75rem;
    }

    .btn-primary-custom {
        background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
        border: none;
        border-radius: 0.75rem;
        padding: 1rem;
        font-weight: 600;
        font-size: 1rem;
        color: white;
        transition: all 0.2s ease;
    }

    .btn-primary-custom:hover {
        transform: translateY(-1px);
        box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.4);
    }

    .welcome-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.3);
    }

    .form-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    @media (min-width: 640px) {
        .form-grid {
            grid-template-columns: 1fr 1fr;
        }
        .form-grid .full-width {
            grid-column: 1 / -1;
        }
    }

    .password-strength {
        height: 4px;
        border-radius: 2px;
        margin-top: 0.5rem;
        transition: all 0.3s ease;
    }

    .strength-weak { background: #EF4444; width: 25%; }
    .strength-fair { background: #F59E0B; width: 50%; }
    .strength-good { background: #10B981; width: 75%; }
    .strength-strong { background: #059669; width: 100%; }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 px-4 py-6">
    <div class="w-full max-w-md mx-auto">
        <!-- Welcome Header -->
        <div class="text-center mb-6">
            <div class="welcome-icon mx-auto mb-4">
                <i class="fas fa-graduation-cap text-white text-2xl"></i>
            </div>
            <h1 class="text-2xl font-bold text-gray-800 mb-2">Join Pentora Today!</h1>
            <p class="text-gray-600">An easy way to learn and grow — no matter your background</p>
        </div>

        <!-- Registration Form -->
        <div class="register-card p-6">
            <form method="post" id="registration-form">
                {% csrf_token %}

                <!-- Name Fields -->
                <div class="grid grid-cols-2 gap-2 mb-3">
                    <div>
                        <label class="form-label block flex items-center">
                            <i class="fas fa-user text-blue-500 mr-1 text-xs"></i>
                            First Name *
                        </label>
                        <input type="text" name="first_name" placeholder="First name" class="form-input w-full" required autofocus>
                    </div>
                    <div>
                        <label class="form-label block flex items-center">
                            <i class="fas fa-user text-blue-500 mr-1 text-xs"></i>
                            Last Name *
                        </label>
                        <input type="text" name="last_name" placeholder="Last name" class="form-input w-full" required>
                    </div>
                </div>

                <!-- Email -->
                <div class="mb-3">
                    <label class="form-label block flex items-center">
                        <i class="fas fa-envelope text-green-500 mr-1 text-xs"></i>
                        Email Address *
                    </label>
                    <input type="email" name="email" placeholder="<EMAIL>" class="form-input w-full" required autocomplete="email">
                </div>

                <!-- Password -->
                <div class="mb-3">
                    <label class="form-label block flex items-center">
                        <i class="fas fa-lock text-red-500 mr-1 text-xs"></i>
                        Password *
                    </label>
                    <div class="relative">
                        <input type="password" name="password1" placeholder="Create a strong password" class="form-input w-full pr-10" required autocomplete="new-password" id="password1" onkeyup="checkPasswordStrength()">
                        <button type="button" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 text-sm" onclick="togglePassword('password1', 'password1-icon')">
                            <i class="fas fa-eye" id="password1-icon"></i>
                        </button>
                    </div>
                    <div class="password-strength bg-gray-200" id="password-strength"></div>
                    <div class="text-xs text-gray-500 mt-1" id="password-feedback">Password must be at least 8 characters long</div>
                </div>

                <!-- Confirm Password -->
                <div class="mb-3">
                    <label class="form-label block flex items-center">
                        <i class="fas fa-lock text-red-500 mr-1 text-xs"></i>
                        Confirm Password *
                    </label>
                    <div class="relative">
                        <input type="password" name="password2" placeholder="Confirm your password" class="form-input w-full pr-10" required autocomplete="new-password" id="password2" onkeyup="checkPasswordMatch()">
                        <button type="button" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 text-sm" onclick="togglePassword('password2', 'password2-icon')">
                            <i class="fas fa-eye" id="password2-icon"></i>
                        </button>
                    </div>
                    <div class="text-xs mt-1" id="password-match-feedback"></div>
                </div>

                <!-- Optional Information (Collapsible) -->
                <div class="mb-3">
                    <button type="button" class="w-full text-left text-xs text-gray-600 hover:text-gray-800 flex items-center justify-between py-2" onclick="toggleOptional()">
                        <span><i class="fas fa-star text-yellow-500 mr-1"></i> Optional Information</span>
                        <i class="fas fa-chevron-down text-xs" id="optional-icon"></i>
                    </button>
                    <div id="optional-section" class="hidden mt-2 space-y-2">
                        <div>
                            <label class="form-label block flex items-center">
                                <i class="fas fa-calendar text-blue-500 mr-1 text-xs"></i>
                                Date of Birth
                            </label>
                            <input type="date" name="date_of_birth" class="form-input w-full" max="{{ today|date:'Y-m-d' }}">
                        </div>
                        <div>
                            <label class="form-label block flex items-center">
                                <i class="fas fa-venus-mars text-purple-500 mr-1 text-xs"></i>
                                Gender
                            </label>
                            <select name="gender" class="form-input w-full">
                                <option value="">Select Gender</option>
                                <option value="M">Male</option>
                                <option value="F">Female</option>
                                <option value="O">Other</option>
                                <option value="P">Prefer not to say</option>
                            </select>
                        </div>
                        <div>
                            <label class="form-label block flex items-center">
                                <i class="fas fa-globe text-green-500 mr-1 text-xs"></i>
                                Country
                            </label>
                            <select name="country" class="form-input w-full">
                                <option value="">Select Country</option>
                                <option value="GH">Ghana</option>
                                <option value="NG">Nigeria</option>
                                <option value="KE">Kenya</option>
                                <option value="ZA">South Africa</option>
                                <option value="EG">Egypt</option>
                                <option value="MA">Morocco</option>
                                <option value="TZ">Tanzania</option>
                                <option value="UG">Uganda</option>
                                <option value="ZW">Zimbabwe</option>
                                <option value="BW">Botswana</option>
                                <option value="US">United States</option>
                                <option value="GB">United Kingdom</option>
                                <option value="CA">Canada</option>
                                <option value="AU">Australia</option>
                                <option value="DE">Germany</option>
                                <option value="FR">France</option>
                                <option value="IN">India</option>
                                <option value="CN">China</option>
                                <option value="JP">Japan</option>
                                <option value="BR">Brazil</option>
                                <option value="OTHER">Other</option>
                            </select>
                        </div>
                        <div>
                            <label class="form-label block flex items-center">
                                <i class="fas fa-bullseye text-orange-500 mr-1 text-xs"></i>
                                Learning Goals
                            </label>
                            <textarea name="learning_goals" placeholder="What do you hope to achieve?" class="form-input w-full h-12 resize-none text-xs" maxlength="200"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Terms and Conditions -->
                <div class="mb-4">
                    <div class="flex items-start">
                        <input type="checkbox" name="terms" id="terms" class="w-3 h-3 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mt-1" required>
                        <label for="terms" class="ml-2 text-xs text-gray-700">
                            I agree to Pentora's
                            <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Terms of Service</a>
                            and
                            <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Privacy Policy</a>
                            <span class="text-red-500">*</span>
                        </label>
                    </div>
                </div>

                <!-- Submit Button -->
                <button type="submit" class="btn-primary-custom w-full mb-3 py-2" id="submit-btn" data-custom-loading disabled>
                    <i class="fas fa-rocket mr-2"></i>
                    Start My Learning Journey
                </button>
            </form>

            <!-- Login Link -->
            <div class="text-center">
                <p class="text-sm text-gray-600 mb-3">Already have an account?</p>
                <a href="{% url 'users:login' %}" class="btn btn-outline w-full border-2 border-gray-200 hover:border-blue-300 hover:bg-blue-50 text-gray-700 hover:text-blue-600 font-medium py-3 rounded-xl">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Sign In to Your Account
                </a>
            </div>
        </div>

        <!-- Benefits Section -->
        <div class="mt-4 grid grid-cols-3 gap-2">
            <div class="bg-white rounded-lg p-2 text-center border border-gray-200">
                <i class="fas fa-graduation-cap text-blue-500 text-sm mb-1"></i>
                <h4 class="font-semibold text-gray-800 text-xs">Quality Education</h4>
                <p class="text-xs text-gray-600">GES-aligned</p>
            </div>
            <div class="bg-white rounded-lg p-2 text-center border border-gray-200">
                <i class="fas fa-mobile-alt text-green-500 text-sm mb-1"></i>
                <h4 class="font-semibold text-gray-800 text-xs">Learn Anywhere</h4>
                <p class="text-xs text-gray-600">Mobile-friendly</p>
            </div>
            <div class="bg-white rounded-lg p-2 text-center border border-gray-200">
                <i class="fas fa-heart text-red-500 text-sm mb-1"></i>
                <h4 class="font-semibold text-gray-800 text-xs">Completely Free</h4>
                <p class="text-xs text-gray-600">No hidden costs</p>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(inputId, iconId) {
    const passwordInput = document.getElementById(inputId);
    const passwordIcon = document.getElementById(iconId);

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        passwordIcon.className = 'fas fa-eye';
    }
}

function toggleOptional() {
    const section = document.getElementById('optional-section');
    const icon = document.getElementById('optional-icon');

    if (section.classList.contains('hidden')) {
        section.classList.remove('hidden');
        icon.className = 'fas fa-chevron-up';
    } else {
        section.classList.add('hidden');
        icon.className = 'fas fa-chevron-down';
    }
}

function checkPasswordStrength() {
    const password = document.getElementById('password1').value;
    const strengthBar = document.getElementById('password-strength');
    const feedback = document.getElementById('password-feedback');

    let strength = 0;
    let message = '';

    if (password.length >= 8) strength++;
    if (password.match(/[a-z]/)) strength++;
    if (password.match(/[A-Z]/)) strength++;
    if (password.match(/[0-9]/)) strength++;
    if (password.match(/[^a-zA-Z0-9]/)) strength++;

    strengthBar.className = 'password-strength bg-gray-200';

    if (password.length === 0) {
        message = 'Password must be at least 8 characters long';
    } else if (strength <= 2) {
        strengthBar.classList.add('strength-weak');
        message = 'Weak password. Add uppercase, numbers, or symbols.';
    } else if (strength === 3) {
        strengthBar.classList.add('strength-fair');
        message = 'Fair password. Consider adding more variety.';
    } else if (strength === 4) {
        strengthBar.classList.add('strength-good');
        message = 'Good password!';
    } else {
        strengthBar.classList.add('strength-strong');
        message = 'Strong password!';
    }

    feedback.textContent = message;
    feedback.className = strength >= 3 ? 'text-xs text-green-600 mt-1' : 'text-xs text-gray-500 mt-1';

    checkFormValidity();
}

function checkPasswordMatch() {
    const password1 = document.getElementById('password1').value;
    const password2 = document.getElementById('password2').value;
    const feedback = document.getElementById('password-match-feedback');

    if (password2.length === 0) {
        feedback.textContent = '';
        feedback.className = 'text-xs mt-1';
    } else if (password1 === password2) {
        feedback.textContent = 'Passwords match!';
        feedback.className = 'text-xs text-green-600 mt-1';
    } else {
        feedback.textContent = 'Passwords do not match';
        feedback.className = 'text-xs text-red-500 mt-1';
    }

    checkFormValidity();
}

function checkFormValidity() {
    const form = document.getElementById('registration-form');
    const submitBtn = document.getElementById('submit-btn');
    const password1 = document.getElementById('password1').value;
    const password2 = document.getElementById('password2').value;
    const terms = document.getElementById('terms').checked;

    const requiredFields = form.querySelectorAll('input[required]');
    let allValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            allValid = false;
        }
    });

    const passwordsMatch = password1 === password2 && password1.length >= 8;
    const formValid = allValid && passwordsMatch && terms;

    submitBtn.disabled = !formValid;
    submitBtn.className = formValid
        ? 'btn-primary-custom w-full'
        : 'btn-primary-custom w-full opacity-50 cursor-not-allowed';
}

// Add event listeners
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('registration-form');
    const submitBtn = document.getElementById('submit-btn');
    const inputs = form.querySelectorAll('input, textarea');

    inputs.forEach(input => {
        input.addEventListener('input', checkFormValidity);
        input.addEventListener('change', checkFormValidity);
    });

    // Set today's date as max for date of birth
    const today = new Date().toISOString().split('T')[0];
    document.querySelector('input[name="date_of_birth"]').max = today;

    // Handle form submission with proper error handling
    form.addEventListener('submit', function(e) {
        console.log('Registration form submitted');

        if (submitBtn.disabled) {
            e.preventDefault();
            return;
        }

        // Show loading state
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Creating Account...';
        submitBtn.disabled = true;

        // Add timeout to reset button if something goes wrong
        const timeoutId = setTimeout(() => {
            console.log('Registration form timeout');
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;

            // Show error message
            const errorDiv = document.createElement('div');
            errorDiv.className = 'alert alert-error mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg';
            errorDiv.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <span>Registration is taking longer than expected. Please check your internet connection and try again.</span>
                </div>
            `;
            form.insertAdjacentElement('afterend', errorDiv);

            // Remove error after 8 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, 8000);
        }, 15000);

        // Clear timeout if page unloads (successful submission)
        window.addEventListener('beforeunload', () => {
            clearTimeout(timeoutId);
        });
    });
});
</script>
{% endblock %}
