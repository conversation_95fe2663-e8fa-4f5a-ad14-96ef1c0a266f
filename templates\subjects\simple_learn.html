{% extends 'base.html' %}

{% block title %}Learn - Grade {{ user_class_level_name }}{% endblock %}

{% block extra_css %}
<style>
    /* Modern hero gradient */
    .hero-gradient {
        background: linear-gradient(135deg, #059669 0%, #0d9488 50%, #0891b2 100%) !important;
        min-height: 400px;
        position: relative;
    }

    .hero-pattern {
        background-image:
            radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%) !important;
    }

    /* Enhanced subject cards */
    .subject-card {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        min-height: 220px;
        background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
        border: 1px solid #e2e8f0;
        position: relative;
        overflow: hidden;
    }

    .subject-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(5, 150, 105, 0.1), transparent);
        transition: left 0.6s ease;
    }

    .subject-card:hover::before {
        left: 100%;
    }

    .subject-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 40px rgba(5, 150, 105, 0.15);
        border-color: #059669;
    }

    /* Modern glass morphism effect */
    .glass-card {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    }

    /* Ensure glass card text is dark and visible */
    .glass-card h3,
    .glass-card p,
    .glass-card div {
        color: #1f2937 !important;
    }

    /* Text clamp for descriptions */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* Cute animations */
    @keyframes bounce-in {
        0% { transform: scale(0.8); opacity: 0; }
        50% { transform: scale(1.02); }
        100% { transform: scale(1); opacity: 1; }
    }

    .subject-card {
        animation: bounce-in 0.5s ease-out;
    }

    .subject-card:nth-child(1) { animation-delay: 0.1s; }
    .subject-card:nth-child(2) { animation-delay: 0.2s; }
    .subject-card:nth-child(3) { animation-delay: 0.3s; }
    .subject-card:nth-child(4) { animation-delay: 0.4s; }
    .subject-card:nth-child(5) { animation-delay: 0.5s; }
    .subject-card:nth-child(6) { animation-delay: 0.6s; }
    .subject-card:nth-child(7) { animation-delay: 0.7s; }
    .subject-card:nth-child(8) { animation-delay: 0.8s; }
    .subject-card:nth-child(9) { animation-delay: 0.9s; }
    .subject-card:nth-child(10) { animation-delay: 1.0s; }

    /* Ensure excellent text contrast for accessibility */
    .text-white {
        color: #ffffff !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        font-weight: 700;
    }

    .text-gray-700 {
        color: #374151 !important;
        font-weight: 600;
    }

    .text-gray-600 {
        color: #4b5563 !important;
        font-weight: 500;
    }

    /* Mobile optimizations for better readability */
    @media (max-width: 640px) {
        .subject-card {
            margin-bottom: 1rem;
            min-height: 180px;
        }

        .container {
            padding-left: 1rem !important;
            padding-right: 1rem !important;
        }

        /* Larger text on mobile for better readability */
        .subject-card h3 {
            font-size: 1.125rem !important;
            line-height: 1.4 !important;
        }

        .subject-card p {
            font-size: 0.875rem !important;
            line-height: 1.5 !important;
        }

        .subject-card .text-sm {
            font-size: 0.875rem !important;
        }

        /* Better button sizing on mobile */
        .subject-card a, .subject-card button {
            padding: 0.875rem 1rem !important;
            font-size: 0.875rem !important;
            font-weight: 700 !important;
        }
    }

    /* Ensure cards don't get too squeezed */
    @media (max-width: 480px) {
        .grid {
            grid-template-columns: 1fr !important;
            gap: 1rem !important;
        }

        .subject-card {
            max-width: 100%;
            margin: 0 auto 1rem auto;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Modern Hero Section -->
<div class="hero-gradient hero-pattern relative overflow-hidden" style="background: linear-gradient(135deg, #059669 0%, #0d9488 50%, #0891b2 100%) !important; color: white !important; min-height: 400px;">
    <!-- Animated background elements -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute top-10 left-10 w-20 h-20 bg-white rounded-full animate-pulse"></div>
        <div class="absolute top-32 right-20 w-16 h-16 bg-white rounded-full animate-pulse delay-1000"></div>
        <div class="absolute bottom-20 left-1/4 w-12 h-12 bg-white rounded-full animate-pulse delay-2000"></div>
        <div class="absolute bottom-32 right-1/3 w-8 h-8 bg-white rounded-full animate-pulse delay-3000"></div>
    </div>

    <div class="relative container mx-auto px-6 py-16 lg:py-20">
        <div class="text-center max-w-4xl mx-auto">
            <!-- Modern heading with icons -->
            <div class="flex items-center justify-center mb-6">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mr-4 backdrop-blur-sm">
                    <i class="fas fa-book-open text-white text-2xl"></i>
                </div>
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight" style="color: white !important;">
                    {{ user_class_level_name }} Learning
                </h1>
            </div>

            <!-- Enhanced description -->
            <p class="text-xl lg:text-2xl leading-relaxed mb-8 max-w-3xl mx-auto" style="color: rgba(255,255,255,0.9) !important;">
                Explore comprehensive study materials designed to help you master every subject with confidence.
            </p>

            <!-- Modern stats section -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12 max-w-2xl mx-auto">
                <div class="text-center">
                    <div class="text-2xl lg:text-3xl font-bold mb-1" style="color: white !important;">{{ subjects|length|default:"12" }}</div>
                    <div class="text-sm" style="color: rgba(255,255,255,0.9) !important;">Subjects</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl lg:text-3xl font-bold mb-1" style="color: white !important;">500+</div>
                    <div class="text-sm" style="color: rgba(255,255,255,0.9) !important;">Lessons</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl lg:text-3xl font-bold mb-1" style="color: white !important;">1000+</div>
                    <div class="text-sm" style="color: rgba(255,255,255,0.9) !important;">Topics</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl lg:text-3xl font-bold mb-1" style="color: white !important;">Free</div>
                    <div class="text-sm" style="color: rgba(255,255,255,0.9) !important;">Access</div>
                </div>
            </div>

            {% if user.is_authenticated %}
                <!-- Enhanced user progress display -->
                <div class="glass-card rounded-2xl p-6 max-w-md mx-auto" style="background: rgba(255, 255, 255, 0.95) !important;">
                    <div class="flex items-center justify-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-chart-line text-white text-xl"></i>
                        </div>
                        <div class="text-left">
                            <h3 class="font-bold text-lg" style="color: #1f2937 !important;">Your Progress</h3>
                            <p class="text-sm" style="color: #4b5563 !important;">Keep up the great work!</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4 text-center">
                        <div class="bg-green-50 rounded-lg p-3">
                            <div class="text-2xl font-bold" style="color: #1f2937 !important;">{{ completion_rate|floatformat:0 }}%</div>
                            <div class="text-xs" style="color: #4b5563 !important;">Complete</div>
                        </div>
                        <div class="bg-blue-50 rounded-lg p-3">
                            <div class="text-2xl font-bold" style="color: #1f2937 !important;">{{ completed_subjects }}/{{ total_subjects }}</div>
                            <div class="text-xs" style="color: #4b5563 !important;">Subjects</div>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Bottom wave -->
    <div class="absolute bottom-0 left-0 right-0">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="w-full h-12 lg:h-20">
            <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="#f8fafc"></path>
            <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="#f8fafc"></path>
            <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="#f8fafc"></path>
        </svg>
    </div>
</div>

<!-- Main Content Section -->
<div class="min-h-screen bg-gray-50 py-12">
    <div class="container mx-auto px-6">

    <!-- Main Content -->
    <div class="container mx-auto px-4 sm:px-6 py-6 sm:py-8">
        <div class="max-w-6xl mx-auto">

            {% if not user.is_authenticated %}
            <!-- Login Prompt for Visitors -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-2xl p-6 sm:p-8 mb-8 text-center">
                <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-info-circle text-yellow-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">Sign in to Track Your Progress</h3>
                <p class="text-gray-600 mb-4">Create a free account to save your learning progress and unlock all features.</p>
                <a href="{% url 'users:login' %}" class="bg-blue-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-blue-700 transition-colors inline-flex items-center">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Sign In to Continue
                </a>
            </div>
            {% endif %}

            <!-- Compact Subjects Grid -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                {% for subject_data in subjects_with_progress %}
                {% with subject=subject_data.subject progress=subject_data.progress %}
                <div class="subject-card bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 overflow-hidden">
                    <!-- Clean Professional Header -->
                    <div class="relative">
                        <div class="p-6 bg-gray-50 border-b border-gray-100">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4 flex-1 min-w-0">
                                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <i class="{{ subject.icon|default:'fas fa-book' }} text-blue-600 text-xl"></i>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h3 class="text-gray-900 font-bold text-lg leading-tight">{{ subject.name }}</h3>
                                        {% if subject.description %}
                                        <p class="text-gray-600 text-sm mt-1 line-clamp-2">{{ subject.description|truncatewords:8 }}</p>
                                        {% endif %}
                                    </div>
                                </div>

                                {% if user.is_authenticated and progress %}
                                <!-- Clean Progress Badge -->
                                {% if progress.is_completed %}
                                <div class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs font-semibold flex-shrink-0">
                                    <i class="fas fa-check mr-1"></i>Complete
                                </div>
                                {% elif progress.is_started %}
                                <div class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs font-semibold flex-shrink-0">
                                    {{ progress.completion_percentage|floatformat:0 }}%
                                </div>
                                {% else %}
                                <div class="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-xs font-semibold flex-shrink-0">
                                    New
                                </div>
                                {% endif %}
                                {% endif %}
                            </div>

                            {% if user.is_authenticated and progress and progress.is_started %}
                            <!-- Clean Progress Bar -->
                            <div class="mt-4">
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 rounded-full h-2 transition-all duration-300" style="width: {{ progress.completion_percentage }}%"></div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Compact Subject Content with Better Contrast -->
                    <div class="p-4 sm:p-3">
                        {% if subject.description %}
                        <p class="text-gray-700 text-sm mb-3 line-clamp-2 font-medium">
                            {{ subject.description|truncatewords:10 }}
                        </p>
                        {% endif %}

                        <!-- Compact Subject Stats -->
                        {% if user.is_authenticated and progress %}
                        <div class="flex items-center justify-between mb-3 text-sm text-gray-700">
                            <span class="font-medium">
                                <i class="fas fa-tasks mr-1 text-blue-600"></i>
                                {{ progress.topics_completed }}/{{ progress.total_topics }} topics
                            </span>
                            {% if progress.is_started %}
                            <span class="font-bold text-green-600">{{ progress.completion_percentage|floatformat:0 }}% done</span>
                            {% endif %}
                        </div>
                        {% endif %}

                        <!-- Clean Action Button -->
                        {% if user.is_authenticated %}
                        <a href="{% url 'subjects:subject_redirect' subject.id %}"
                           class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-semibold transition-all flex items-center justify-center text-sm">
                            {% if progress and progress.is_started %}
                                <i class="fas fa-play mr-2"></i>
                                Continue Learning
                            {% else %}
                                <i class="fas fa-book-open mr-2"></i>
                                Start Learning
                            {% endif %}
                        </a>
                        {% else %}
                        <button onclick="showLoginPrompt('{{ subject.name }}')"
                                class="w-full border-2 border-gray-300 text-gray-700 py-3 px-4 rounded-lg font-semibold hover:bg-gray-50 hover:border-gray-400 transition-all flex items-center justify-center text-sm">
                            <i class="fas fa-lock mr-2"></i>
                            Sign in to Learn
                        </button>
                        {% endif %}
                    </div>
                </div>
                {% endwith %}
                {% empty %}
                <div class="col-span-full text-center py-16">
                    <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-book-open text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">No Subjects Available</h3>
                    <p class="text-gray-600">
                        No subjects are available for {{ user_class_level_name }} yet. Please check back later.
                    </p>
                </div>
                {% endfor %}
            </div>

            {% if user.is_authenticated %}
            <!-- Clean Quick Actions -->
            <div class="mt-8 bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4 text-center">
                    <i class="fas fa-bolt text-gray-600 mr-2"></i>
                    Quick Actions
                </h3>
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <a href="{% url 'subjects:quiz_list' %}"
                       class="bg-gray-50 hover:bg-gray-100 border border-gray-200 text-gray-800 p-4 rounded-lg text-center transition-all">
                        <i class="fas fa-brain text-xl mb-2 text-blue-600"></i>
                        <div class="font-semibold text-sm">Take Quiz</div>
                        <div class="text-xs text-gray-600">Test knowledge</div>
                    </a>
                    <a href="{% url 'core:dashboard' %}"
                       class="bg-gray-50 hover:bg-gray-100 border border-gray-200 text-gray-800 p-4 rounded-lg text-center transition-all">
                        <i class="fas fa-chart-line text-xl mb-2 text-green-600"></i>
                        <div class="font-semibold text-sm">View Progress</div>
                        <div class="text-xs text-gray-600">Check stats</div>
                    </a>
                    <a href="{% url 'core:help' %}"
                       class="bg-gray-50 hover:bg-gray-100 border border-gray-200 text-gray-800 p-4 rounded-lg text-center transition-all">
                        <i class="fas fa-question-circle text-xl mb-2 text-orange-600"></i>
                        <div class="font-semibold text-sm">Get Help</div>
                        <div class="text-xs text-gray-600">Need help?</div>
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

{% if not user.is_authenticated %}
<!-- Login Modal -->
<div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl p-6 sm:p-8 max-w-md w-full">
        <div class="text-center">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-graduation-cap text-blue-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-2">Start Learning Today!</h3>
            <p class="text-gray-600 mb-6" id="loginModalText">Sign in to access all learning materials and track your progress.</p>
            <div class="space-y-3">
                <a href="{% url 'users:login' %}" class="w-full bg-blue-600 text-white py-3 px-4 rounded-xl font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Sign In
                </a>
                <a href="{% url 'users:register' %}" class="w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-xl font-semibold hover:bg-gray-50 transition-colors flex items-center justify-center">
                    <i class="fas fa-user-plus mr-2"></i>
                    Create Account
                </a>
                <button onclick="closeLoginPrompt()" class="w-full text-gray-500 py-2 hover:text-gray-700 transition-colors">
                    Maybe Later
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function showLoginPrompt(subjectName) {
    document.getElementById('loginModalText').textContent = `Sign in to start learning ${subjectName} and track your progress.`;
    document.getElementById('loginModal').classList.remove('hidden');
}

function closeLoginPrompt() {
    document.getElementById('loginModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('loginModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeLoginPrompt();
    }
});
</script>
{% endif %}

<script>
// Default icons for subjects
const defaultIcons = {
    'english': 'fas fa-book-open',
    'language': 'fas fa-book-open',
    'literature': 'fas fa-book-open',
    'math': 'fas fa-calculator',
    'algebra': 'fas fa-calculator',
    'geometry': 'fas fa-calculator',
    'science': 'fas fa-flask',
    'physics': 'fas fa-flask',
    'chemistry': 'fas fa-flask',
    'biology': 'fas fa-flask',
    'social': 'fas fa-globe-africa',
    'history': 'fas fa-globe-africa',
    'geography': 'fas fa-globe-africa',
    'life': 'fas fa-heart',
    'skill': 'fas fa-heart',
    'health': 'fas fa-heart',
    'computer': 'fas fa-laptop-code',
    'technology': 'fas fa-laptop-code',
    'coding': 'fas fa-laptop-code',
    'art': 'fas fa-palette',
    'music': 'fas fa-music',
    'physical': 'fas fa-running',
    'sport': 'fas fa-running'
};

// Apply appropriate icons to subjects
document.addEventListener('DOMContentLoaded', function() {
    const subjectCards = document.querySelectorAll('.subject-card');

    subjectCards.forEach((card, index) => {
        const icon = card.querySelector('i');
        const subjectName = card.querySelector('h3').textContent.toLowerCase();

        // If icon is default book icon, try to find a better match
        if (icon && icon.className.includes('fa-book')) {
            for (const [keyword, iconClass] of Object.entries(defaultIcons)) {
                if (subjectName.includes(keyword)) {
                    icon.className = icon.className.replace('fas fa-book', iconClass);
                    break;
                }
            }
        }
    });
});
</script>

{% endblock %}
