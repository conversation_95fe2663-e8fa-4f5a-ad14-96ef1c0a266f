{% extends 'base.html' %}

{% block title %}Dashboard - Pentora{% endblock %}

{% block extra_css %}
<style>
    /* Modern Clean Dashboard Styles */
    .dashboard-container {
        background: #f8fafc;
        min-height: 100vh;
        padding: 1rem 0;
    }

    .dashboard-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        overflow: hidden;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
    }

    .dashboard-card:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }

    .hero-section {
        background: white;
        border-bottom: 1px solid #e5e7eb;
        color: #1f2937;
        padding: 2rem;
        position: relative;
    }



    .hero-content {
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .hero-main {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .hero-text {
        flex: 1;
        min-width: 250px;
        text-align: left;
    }

    .hero-stats {
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .stat-card {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 1rem 1.25rem;
        text-align: center;
        min-width: 70px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .stat-icon {
        margin-bottom: 0.5rem;
        font-size: 1.25rem;
    }

    .stat-number {
        font-size: 1.5rem;
        font-weight: 800;
        margin-bottom: 0.25rem;
        line-height: 1;
        color: #1f2937;
    }

    .stat-label {
        font-size: 0.75rem;
        color: #6b7280;
        line-height: 1;
    }

    .continue-learning-btn {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        border: 1px solid #3b82f6;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        font-size: 0.875rem;
        white-space: nowrap;
        min-width: 160px;
        box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
    }

    .border-3 {
        border-width: 3px;
    }

    /* Enhanced hero section spacing */
    .hero-content {
        padding: 2rem;
    }

    .continue-learning-btn:hover {
        background: #2563eb;
        border-color: #2563eb;
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
    }

    .subjects-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1rem;
        padding: 1rem;
    }

    @media (min-width: 640px) {
        .subjects-grid {
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 1.5rem;
            padding: 1.5rem;
        }
    }

    .subject-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        border-left: 4px solid #e2e8f0;
        transition: all 0.3s ease;
        position: relative;
    }

    .subject-card.completed {
        border-left-color: #10b981;
        background: linear-gradient(to right, #f0fdf4, #ffffff);
    }

    .subject-card.in-progress {
        border-left-color: #3b82f6;
        background: linear-gradient(to right, #eff6ff, #ffffff);
    }

    .subject-card.not-started {
        border-left-color: #6b7280;
    }

    .subject-header {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .subject-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-right: 1rem;
        flex-shrink: 0;
    }

    .subject-info {
        flex: 1;
    }

    .subject-name {
        font-size: 1.25rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 0.25rem;
    }

    .subject-progress-text {
        font-size: 0.875rem;
        color: #6b7280;
    }

    .progress-bar {
        width: 100%;
        height: 8px;
        background: #e5e7eb;
        border-radius: 4px;
        overflow: hidden;
        margin: 1rem 0;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #3b82f6, #1d4ed8);
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    .progress-fill.completed {
        background: linear-gradient(90deg, #10b981, #059669);
    }

    .subject-actions {
        display: flex;
        gap: 0.75rem;
        margin-top: 1rem;
    }

    .action-btn {
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        border: none;
        cursor: pointer;
        flex: 1;
        justify-content: center;
    }

    .action-btn.primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
    }

    .action-btn.primary:hover {
        background: linear-gradient(135deg, #2563eb, #1e40af);
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
    }

    .action-btn.secondary {
        background: #f3f4f6;
        color: #374151;
        border: 1px solid #d1d5db;
    }

    .action-btn.secondary:hover {
        background: #e5e7eb;
        color: #374151;
        text-decoration: none;
    }

    .action-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .recent-activity {
        padding: 1.5rem;
    }

    .activity-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 0.75rem;
        background: #f9fafb;
        transition: all 0.3s ease;
    }

    .activity-item:hover {
        background: #f3f4f6;
    }

    .activity-icon {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        flex-shrink: 0;
    }

    .activity-content {
        flex: 1;
    }

    .activity-title {
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.25rem;
    }

    .activity-subtitle {
        font-size: 0.875rem;
        color: #6b7280;
    }

    /* Mobile optimizations */
    @media (max-width: 640px) {
        .dashboard-container {
            padding: 0.5rem 0;
        }

        .hero-section {
            padding: 1rem;
        }

        .hero-main {
            flex-direction: column;
            align-items: center;
            gap: 1rem;
        }

        .hero-text {
            text-align: center;
            min-width: auto;
        }

        .hero-text h1 {
            font-size: 1.75rem !important;
            margin-bottom: 0.5rem;
        }

        .hero-text p {
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }

        .hero-stats {
            justify-content: center;
            gap: 0.5rem;
            width: 100%;
        }

        .stat-card {
            padding: 0.5rem 0.75rem;
            min-width: 65px;
            flex: 1;
            max-width: 80px;
        }

        .stat-number {
            font-size: 1.25rem;
        }

        .stat-label {
            font-size: 0.65rem;
        }

        .continue-learning-btn {
            padding: 0.75rem 1.25rem;
            font-size: 0.8rem;
            min-width: 140px;
            margin-top: 0.5rem;
        }

        .subjects-grid {
            grid-template-columns: 1fr;
            padding: 1rem;
            gap: 1rem;
        }

        .subject-card {
            padding: 1rem;
        }

        .subject-header {
            flex-direction: column;
            align-items: center;
            text-align: center;
            gap: 1rem;
        }

        .subject-icon {
            margin-bottom: 0.5rem;
        }

        .subject-actions {
            flex-direction: column;
            gap: 0.5rem;
        }

        .action-btn {
            justify-content: center;
            width: 100%;
        }
    }

    /* Level Selection Styles */
    .professional-bg {
        background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #581c87 100%);
        min-height: 100vh;
    }

    .level-selection-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border: 1px solid #e5e7eb;
    }

    .level-option {
        background: #f8fafc;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        padding: 1.25rem;
        transition: all 0.2s ease;
        cursor: pointer;
        position: relative;
        text-align: center;
    }

    .level-option:hover {
        border-color: #3b82f6;
        background: #eff6ff;
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .level-option.selected {
        border-color: #2563eb;
        background: #dbeafe;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .level-option.selected::after {
        content: '✓';
        position: absolute;
        top: 8px;
        right: 8px;
        background: #2563eb;
        color: white;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
    }

    .level-number {
        font-size: 1.875rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.25rem;
    }

    .level-name {
        font-size: 0.875rem;
        font-weight: 500;
        color: #6b7280;
    }

    .selected .level-number {
        color: #2563eb;
    }

    .selected .level-name {
        color: #1d4ed8;
    }

    .welcome-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .welcome-title {
        font-size: 2rem;
        font-weight: 600;
        color: white;
        margin-bottom: 0.5rem;
    }

    .welcome-subtitle {
        font-size: 1rem;
        color: rgba(255, 255, 255, 0.8);
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
    }

    .submit-button {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.875rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.2s ease;
        width: 100%;
        margin-top: 1.5rem;
    }

    .submit-button:hover {
        background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .submit-button:disabled {
        background: #9ca3af;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .info-note {
        background: #f0f9ff;
        border: 1px solid #bae6fd;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
        text-align: center;
    }

    .info-note p {
        margin: 0;
        font-size: 0.875rem;
        color: #0369a1;
    }
</style>
{% endblock %}

{% block content %}
{% if needs_level_selection %}
<!-- Level Selection (keep existing) -->
<div class="professional-bg">
    <div class="container mx-auto px-2 sm:px-4 py-8 sm:py-12">
        <div class="max-w-4xl mx-auto">
            <!-- Welcome Header -->
            <div class="welcome-header">
                <h1 class="welcome-title">
                    Welcome to Pentora, {{ user.first_name }}!
                </h1>
                <p class="welcome-subtitle">
                    To provide you with the most appropriate learning materials and track your progress effectively, please select your current class level.
                </p>
            </div>

            <!-- Level Selection Form -->
            <div class="level-selection-card p-6 sm:p-8">
                <form method="post" id="levelForm">
                    {% csrf_token %}
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3 sm:gap-4">
                        {% for level in class_levels %}
                        <div class="level-option" data-level="{{ level.0 }}" onclick="selectLevel({{ level.0 }})">
                            <div class="level-number">{{ level.0 }}</div>
                            <div class="level-name">{{ level.1 }}</div>
                        </div>
                        {% endfor %}
                    </div>

                    <input type="hidden" name="class_level" id="selectedLevel" value="">
                    <button type="submit" class="submit-button" id="submitBtn" data-custom-loading disabled>
                        Continue to Dashboard
                    </button>

                    <div class="info-note">
                        <p>
                            <i class="fas fa-info-circle mr-2"></i>
                            You can change your class level later in your profile settings.
                        </p>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function selectLevel(level) {
    console.log('Level selected:', level);

    // Remove selected class from all options
    document.querySelectorAll('.level-option').forEach(option => {
        option.classList.remove('selected');
    });

    // Add selected class to clicked option
    event.target.closest('.level-option').classList.add('selected');

    // Set hidden input value
    document.getElementById('selectedLevel').value = level;
    console.log('Hidden input value set to:', document.getElementById('selectedLevel').value);

    // Enable submit button
    const submitBtn = document.getElementById('submitBtn');
    submitBtn.disabled = false;
    submitBtn.classList.remove('opacity-50');
    console.log('Submit button enabled');
}

function showError(message) {
    // Remove any existing error messages
    const existingError = document.querySelector('.level-selection-error');
    if (existingError) {
        existingError.remove();
    }

    const errorDiv = document.createElement('div');
    errorDiv.className = 'level-selection-error alert alert-error mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg';
    errorDiv.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    const form = document.getElementById('levelForm');
    form.insertAdjacentElement('afterend', errorDiv);

    // Remove error after 8 seconds
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.remove();
        }
    }, 8000);
}

function showSuccess(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'level-selection-success alert alert-success mt-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg';
    successDiv.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-check-circle mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    const form = document.getElementById('levelForm');
    form.insertAdjacentElement('afterend', successDiv);
}

// Handle form submission with proper loading state and error handling
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('levelForm');
    const submitBtn = document.getElementById('submitBtn');

    if (form && submitBtn) {
        form.addEventListener('submit', function(e) {
            console.log('Form submission started');

            // Validate selection
            const selectedLevel = document.getElementById('selectedLevel').value;
            console.log('Selected level:', selectedLevel);

            if (!selectedLevel) {
                console.log('No level selected - showing error');
                e.preventDefault();
                showError('Please select a class level first.');
                return;
            }

            // Validate CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
            if (!csrfToken || !csrfToken.value) {
                console.log('CSRF token missing');
                e.preventDefault();
                showError('Security token missing. Please refresh the page and try again.');
                return;
            }

            console.log('Form validation passed, submitting...');

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading...';
            submitBtn.classList.add('opacity-75');

            // Don't prevent default - let the form submit naturally
            console.log('Allowing natural form submission...');

            // Add a fallback timeout in case something goes wrong
            setTimeout(() => {
                if (submitBtn.disabled) {
                    console.log('Form submission taking longer than expected');
                    showError('This is taking longer than expected. Please wait or try refreshing the page if nothing happens.');
                }
            }, 8000);

            // Add progress indicator
            let dots = 0;
            const progressInterval = setInterval(() => {
                dots = (dots + 1) % 4;
                const dotString = '.'.repeat(dots);
                submitBtn.innerHTML = `<i class="fas fa-spinner fa-spin mr-2"></i>Loading${dotString}`;
            }, 500);

            // Simplified timeout handling
            const timeoutId = setTimeout(() => {
                console.log('Form submission timed out');
                clearInterval(progressInterval);
                if (submitBtn.disabled) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = 'Continue to Dashboard';
                    submitBtn.classList.remove('opacity-75');

                    showError('Request timed out. Please check your internet connection and try again.');
                }
            }, 20000); // 20 second timeout

            // Clear timeout if page unloads (form submitted successfully)
            window.addEventListener('beforeunload', () => {
                clearTimeout(timeoutId);
                clearInterval(progressInterval);
            });
        });
    }
});
</script>

{% else %}
<!-- Modern Dashboard -->
<div class="dashboard-container">
    <div class="container mx-auto px-4 max-w-7xl">

        <!-- Enhanced Hero Section -->
        <div class="dashboard-card">
            <div class="hero-section">
                <div class="hero-content">
                    <div class="hero-main">
                        <div class="hero-text">
                            <!-- Mobile-First Hero Layout -->
                            <div class="flex flex-col sm:flex-row sm:items-center mb-6 space-y-4 sm:space-y-0 sm:space-x-4">
                                <div class="relative flex-shrink-0 self-center sm:self-auto">
                                    {% if user.profile_picture %}
                                        <img class="w-16 h-16 rounded-full border-3 border-white shadow-lg" src="{{ user.profile_picture.url }}" alt="{{ user.full_name }}">
                                    {% else %}
                                        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center border-3 border-white shadow-lg">
                                            <span class="text-xl font-bold text-white">{{ user.first_name|first|upper }}{{ user.last_name|first|upper }}</span>
                                        </div>
                                    {% endif %}
                                    <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                                        <i class="fas fa-check text-white text-xs"></i>
                                    </div>
                                </div>
                                <div class="text-center sm:text-left flex-1">
                                    <h1 class="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-2">
                                        Welcome back, {{ user.first_name }}! 👋
                                    </h1>
                                    <div class="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-2">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 self-center sm:self-auto">
                                            {{ user_class_level_name }}
                                        </span>
                                        <p class="text-gray-600 text-sm sm:text-base">
                                            Ready to continue your learning journey?
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Mobile-Friendly Stats -->
                        <div class="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 mb-6">
                            <div class="bg-white rounded-xl p-3 sm:p-4 shadow-sm hover:shadow-md transition-all duration-200 hover:-translate-y-1 border border-gray-100">
                                <div class="flex flex-col items-center text-center">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mb-2 text-lg">
                                        📚
                                    </div>
                                    <div class="text-xl sm:text-2xl font-bold text-gray-900">{{ total_subjects }}</div>
                                    <div class="text-xs text-gray-500">Subjects</div>
                                </div>
                            </div>
                            <div class="bg-white rounded-xl p-3 sm:p-4 shadow-sm hover:shadow-md transition-all duration-200 hover:-translate-y-1 border border-gray-100">
                                <div class="flex flex-col items-center text-center">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mb-2 text-lg">
                                        ✅
                                    </div>
                                    <div class="text-xl sm:text-2xl font-bold text-gray-900">{{ completed_subjects }}</div>
                                    <div class="text-xs text-gray-500">Completed</div>
                                </div>
                            </div>
                            <div class="bg-white rounded-xl p-3 sm:p-4 shadow-sm hover:shadow-md transition-all duration-200 hover:-translate-y-1 border border-gray-100">
                                <div class="flex flex-col items-center text-center">
                                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mb-2 text-lg">
                                        📋
                                    </div>
                                    <div class="text-xl sm:text-2xl font-bold text-gray-900">{{ total_topics }}</div>
                                    <div class="text-xs text-gray-500">Topics</div>
                                </div>
                            </div>
                            <div class="bg-white rounded-xl p-3 sm:p-4 shadow-sm hover:shadow-md transition-all duration-200 hover:-translate-y-1 border border-gray-100">
                                <div class="flex flex-col items-center text-center">
                                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mb-2 text-lg">
                                        🏆
                                    </div>
                                    <div class="text-xl sm:text-2xl font-bold text-gray-900">{{ completed_topics }}</div>
                                    <div class="text-xs text-gray-500">Finished</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile-Friendly Continue Learning Section -->
                    {% if next_learning_path %}
                    <div class="mt-6 sm:mt-8">
                        <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 sm:p-6 border border-blue-100">
                            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-6 sm:space-y-0 sm:space-x-6">
                                <div class="text-center sm:text-left flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Continue Learning</h3>
                                    <p class="text-sm text-gray-600">{{ next_learning_path.title }}</p>
                                </div>
                                <div class="flex justify-center sm:justify-end flex-shrink-0">
                                    <a href="{{ next_learning_path.url }}"
                                       class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-sm font-medium rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-sm hover:shadow-md">
                                        <i class="fas fa-play mr-2"></i>
                                        Continue
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Subscription Promotion (only show if billing is enabled and user doesn't have active subscription) -->
        {% if billing_enabled and not user_subscription.is_active %}
        <div class="dashboard-card mb-6">
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg p-6">
                <div class="flex flex-col md:flex-row items-center justify-between">
                    <div class="mb-4 md:mb-0">
                        <h3 class="text-xl font-bold mb-2">
                            <i class="fas fa-star mr-2"></i>
                            Unlock Premium Learning
                        </h3>
                        <p class="text-blue-100">
                            Get access to all subjects, unlimited quizzes, and personalized learning paths
                        </p>
                        <div class="flex items-center mt-2 text-sm text-blue-100">
                            <i class="fas fa-check mr-2"></i>
                            <span class="mr-4">All 12 Subjects</span>
                            <i class="fas fa-check mr-2"></i>
                            <span class="mr-4">Unlimited Practice</span>
                            <i class="fas fa-check mr-2"></i>
                            <span>Progress Tracking</span>
                        </div>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-3">
                        <a href="{% url 'billing:plans' %}" class="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors">
                            <i class="fas fa-crown mr-2"></i>
                            View Plans
                        </a>
                        <a href="{% url 'billing:dashboard' %}" class="border border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                            <i class="fas fa-cog mr-2"></i>
                            Manage Billing
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% elif billing_enabled and user_subscription.is_active %}
        <!-- Active Subscription Status -->
        <div class="dashboard-card mb-6">
            <div class="bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-lg p-6">
                <div class="flex flex-col md:flex-row items-center justify-between">
                    <div class="mb-4 md:mb-0">
                        <h3 class="text-xl font-bold mb-2">
                            <i class="fas fa-check-circle mr-2"></i>
                            {{ user_subscription.plan.name }} Plan Active
                        </h3>
                        <p class="text-green-100">
                            Your subscription is active until {{ user_subscription.current_period_end|date:"F d, Y" }}
                        </p>
                        <div class="flex items-center mt-2 text-sm text-green-100">
                            <i class="fas fa-calendar mr-2"></i>
                            <span class="mr-4">{{ user_subscription.billing_cycle|title }} Billing</span>
                            <i class="fas fa-unlock mr-2"></i>
                            <span>Full Access Enabled</span>
                        </div>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-3">
                        <a href="{% url 'billing:dashboard' %}" class="bg-white text-green-600 px-6 py-3 rounded-lg font-semibold hover:bg-green-50 transition-colors">
                            <i class="fas fa-cog mr-2"></i>
                            Manage Subscription
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Email Verification Alert -->
        {% if not user.is_email_verified %}
        <div class="dashboard-card mb-6">
            <div class="bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-lg p-6">
                <div class="flex flex-col md:flex-row items-center justify-between">
                    <div class="mb-4 md:mb-0">
                        <h3 class="text-xl font-bold mb-2">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            Email Verification Required
                        </h3>
                        <p class="text-yellow-100">
                            Please verify your email address to secure your account and receive important updates.
                        </p>
                        <div class="flex items-center mt-2 text-sm text-yellow-100">
                            <i class="fas fa-envelope mr-2"></i>
                            <span class="mr-4">{{ user.email }}</span>
                            <i class="fas fa-shield-alt mr-2"></i>
                            <span>Account Security</span>
                        </div>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-3">
                        <a href="{% url 'users:resend_verification' %}" class="bg-white text-yellow-600 px-6 py-3 rounded-lg font-semibold hover:bg-yellow-50 transition-colors">
                            <i class="fas fa-paper-plane mr-2"></i>
                            Verify Email
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Quick Actions & Account Management -->
        <div class="dashboard-card mb-6">
            <div class="p-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">
                    <i class="fas fa-cog mr-3 text-purple-600"></i>
                    Account & Settings
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- Edit Profile -->
                    <a href="{% url 'users:profile' %}" class="bg-gradient-to-r from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 border border-blue-200 rounded-lg p-4 transition-all duration-200 hover:shadow-md group">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                <i class="fas fa-user-edit text-white text-lg"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-1">Edit Profile</h3>
                                <p class="text-sm text-gray-600">Update your personal information</p>
                            </div>
                        </div>
                    </a>

                    <!-- Change Class Level -->
                    <button onclick="showClassChangeModal()" class="bg-gradient-to-r from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 border border-green-200 rounded-lg p-4 transition-all duration-200 hover:shadow-md group text-left w-full">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                <i class="fas fa-graduation-cap text-white text-lg"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-1">Change Class</h3>
                                <p class="text-sm text-gray-600">Currently: {{ user_class_level_name }}</p>
                            </div>
                        </div>
                    </button>

                    <!-- Email Verification -->
                    {% if not user.is_email_verified %}
                    <a href="{% url 'users:resend_verification' %}" class="bg-gradient-to-r from-yellow-50 to-yellow-100 hover:from-yellow-100 hover:to-yellow-200 border border-yellow-200 rounded-lg p-4 transition-all duration-200 hover:shadow-md group">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                <i class="fas fa-envelope-open text-white text-lg"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-1">Verify Email</h3>
                                <p class="text-sm text-gray-600">Secure your account</p>
                            </div>
                        </div>
                    </a>
                    {% else %}
                    <div class="bg-gradient-to-r from-emerald-50 to-emerald-100 border border-emerald-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-emerald-500 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-check-circle text-white text-lg"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-1">Email Verified</h3>
                                <p class="text-sm text-gray-600">Your account is secure</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Progress Overview -->
                    <a href="{% url 'progress:overview' %}" class="bg-gradient-to-r from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200 border border-purple-200 rounded-lg p-4 transition-all duration-200 hover:shadow-md group">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                <i class="fas fa-chart-line text-white text-lg"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-1">View Progress</h3>
                                <p class="text-sm text-gray-600">Track your learning journey</p>
                            </div>
                        </div>
                    </a>

                    <!-- Achievements -->
                    <a href="{% url 'progress:achievements' %}" class="bg-gradient-to-r from-orange-50 to-orange-100 hover:from-orange-100 hover:to-orange-200 border border-orange-200 rounded-lg p-4 transition-all duration-200 hover:shadow-md group">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                <i class="fas fa-trophy text-white text-lg"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-1">Achievements</h3>
                                <p class="text-sm text-gray-600">View your badges & awards</p>
                            </div>
                        </div>
                    </a>

                    <!-- Help & Support -->
                    <a href="{% url 'core:help' %}" class="bg-gradient-to-r from-indigo-50 to-indigo-100 hover:from-indigo-100 hover:to-indigo-200 border border-indigo-200 rounded-lg p-4 transition-all duration-200 hover:shadow-md group">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-indigo-500 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                <i class="fas fa-life-ring text-white text-lg"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-1">Help & Support</h3>
                                <p class="text-sm text-gray-600">Get assistance when needed</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>

        <!-- Subjects Grid -->
        <div class="dashboard-card">
            <div class="p-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-graduation-cap mr-3 text-blue-600"></i>
                    Your {{ user_class_level_name }} Subjects
                </h2>
            </div>

            <div class="subjects-grid">
                {% for subject_data in current_grade_subjects %}
                <div class="subject-card {% if subject_data.is_completed %}completed{% elif subject_data.completed_topics > 0 %}in-progress{% else %}not-started{% endif %}">
                    <div class="subject-header">
                        <div class="subject-icon" style="background: {{ subject_data.subject.color }}20; color: {{ subject_data.subject.color }};">
                            {% if subject_data.subject.icon %}
                                {% if 'fa-' in subject_data.subject.icon %}
                                    <i class="{{ subject_data.subject.icon }}"></i>
                                {% else %}
                                    {{ subject_data.subject.icon }}
                                {% endif %}
                            {% else %}
                                {% if 'math' in subject_data.subject.name|lower or 'calculator' in subject_data.subject.name|lower %}
                                    🧮
                                {% elif 'english' in subject_data.subject.name|lower or 'language' in subject_data.subject.name|lower %}
                                    📚
                                {% elif 'science' in subject_data.subject.name|lower %}
                                    🔬
                                {% elif 'social' in subject_data.subject.name|lower or 'studies' in subject_data.subject.name|lower %}
                                    🌍
                                {% elif 'french' in subject_data.subject.name|lower %}
                                    🇫🇷
                                {% elif 'technology' in subject_data.subject.name|lower or 'ict' in subject_data.subject.name|lower %}
                                    💻
                                {% else %}
                                    📖
                                {% endif %}
                            {% endif %}
                        </div>
                        <div class="subject-info">
                            <h3 class="subject-name">{{ subject_data.subject.name }}</h3>
                            <p class="subject-progress-text">
                                {{ subject_data.completed_topics }}/{{ subject_data.topics_count }} topics completed
                            </p>
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div class="progress-bar">
                        <div class="progress-fill {% if subject_data.is_completed %}completed{% endif %}"
                             style="width: {{ subject_data.completion_percentage }}%"></div>
                    </div>

                    <!-- Subject Actions -->
                    <div class="subject-actions">
                        {% if subject_data.next_topic %}
                            <a href="{{ subject_data.next_topic_url }}" class="action-btn primary">
                                <i class="fas fa-play mr-2"></i>
                                {% if subject_data.completed_topics == 0 %}Start Learning{% else %}Continue{% endif %}
                            </a>
                        {% else %}
                            <span class="action-btn primary" style="opacity: 0.5;">
                                <i class="fas fa-check mr-2"></i>
                                Completed
                            </span>
                        {% endif %}

                        <a href="{% url 'subjects:level_detail' subject_data.subject.id subject_data.class_level.id %}" class="action-btn secondary">
                            <i class="fas fa-list mr-2"></i>
                            View Topics
                        </a>
                    </div>
                </div>
                {% empty %}
                <div class="col-span-full text-center py-8">
                    <i class="fas fa-book text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-600">No subjects available for your current grade.</p>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Recent Activity -->
        {% if recent_activity %}
        <div class="dashboard-card">
            <div class="recent-activity">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-clock mr-3 text-green-600"></i>
                    Recent Activity
                </h2>

                {% for activity in recent_activity %}
                <div class="activity-item">
                    <div class="activity-icon" style="background: {{ activity.topic.class_level.subject.color }}20; color: {{ activity.topic.class_level.subject.color }};">
                        {{ activity.topic.class_level.subject.icon }}
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">{{ activity.topic.title }}</div>
                        <div class="activity-subtitle">
                            {{ activity.topic.class_level.subject.name }} •
                            {% if activity.is_completed %}
                                <span class="text-green-600">Completed</span>
                            {% else %}
                                <span class="text-blue-600">In Progress</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

    </div>
</div>

<!-- Class Change Modal -->
<div id="classChangeModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg max-w-md w-full p-6">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-bold text-gray-900">Change Class Level</h3>
            <button onclick="hideClassChangeModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form method="post" id="classChangeForm">
            {% csrf_token %}
            <input type="hidden" name="action" value="change_class">

            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-3">Select your new class level:</label>
                <div class="grid grid-cols-3 gap-3">
                    {% for level in class_levels %}
                    <div class="level-option-modal" data-level="{{ level.0 }}" onclick="selectNewLevel({{ level.0 }})">
                        <div class="border-2 border-gray-200 rounded-lg p-3 text-center cursor-pointer hover:border-blue-500 hover:bg-blue-50 transition-all">
                            <div class="text-lg font-bold text-gray-900">{{ level.0 }}</div>
                            <div class="text-xs text-gray-600">{{ level.1 }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <input type="hidden" name="new_class_level" id="newSelectedLevel" value="">

            <div class="flex space-x-3">
                <button type="button" onclick="hideClassChangeModal()" class="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button type="submit" id="classChangeSubmit" class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50" disabled>
                    Change Class
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function showClassChangeModal() {
    document.getElementById('classChangeModal').classList.remove('hidden');
}

function hideClassChangeModal() {
    document.getElementById('classChangeModal').classList.add('hidden');
    // Reset form
    document.querySelectorAll('.level-option-modal .border-2').forEach(option => {
        option.classList.remove('border-blue-500', 'bg-blue-50');
        option.classList.add('border-gray-200');
    });
    document.getElementById('newSelectedLevel').value = '';
    document.getElementById('classChangeSubmit').disabled = true;
}

function selectNewLevel(level) {
    // Remove selected class from all options
    document.querySelectorAll('.level-option-modal .border-2').forEach(option => {
        option.classList.remove('border-blue-500', 'bg-blue-50');
        option.classList.add('border-gray-200');
    });

    // Add selected class to clicked option
    event.target.closest('.level-option-modal').querySelector('.border-2').classList.remove('border-gray-200');
    event.target.closest('.level-option-modal').querySelector('.border-2').classList.add('border-blue-500', 'bg-blue-50');

    // Set hidden input value
    document.getElementById('newSelectedLevel').value = level;

    // Enable submit button
    document.getElementById('classChangeSubmit').disabled = false;
}

// Handle form submission
document.getElementById('classChangeForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('classChangeSubmit');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Changing...';
});

// Close modal when clicking outside
document.getElementById('classChangeModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideClassChangeModal();
    }
});
</script>

{% endif %}
{% endblock %}
