{% extends 'admin_panel/base.html' %}

{% block title %}{% if object %}Edit Question{% else %}Create Question{% endif %}{% endblock %}
{% block page_title %}{% if object %}Edit Question{% else %}Create New Question{% endif %}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>
                    Create New Question
                </h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}

                    <!-- Question Details -->
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="{{ form.topic.id_for_label }}" class="form-label">
                                Topic <span class="text-danger">*</span>
                            </label>
                            {{ form.topic }}
                            {% if form.topic.errors %}
                                <div class="text-danger small">{{ form.topic.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="{{ form.question_type.id_for_label }}" class="form-label">
                                Question Type <span class="text-danger">*</span>
                            </label>
                            {{ form.question_type }}
                            {% if form.question_type.errors %}
                                <div class="text-danger small">{{ form.question_type.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.question_text.id_for_label }}" class="form-label">
                            Question Text <span class="text-danger">*</span>
                        </label>
                        {{ form.question_text }}
                        {% if form.question_text.errors %}
                            <div class="text-danger small">{{ form.question_text.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.difficulty.id_for_label }}" class="form-label">
                                Difficulty
                            </label>
                            {{ form.difficulty }}
                            {% if form.difficulty.errors %}
                                <div class="text-danger small">{{ form.difficulty.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="{{ form.points.id_for_label }}" class="form-label">
                                Points
                            </label>
                            {{ form.points }}
                            {% if form.points.errors %}
                                <div class="text-danger small">{{ form.points.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="{{ form.image.id_for_label }}" class="form-label">
                                Question Image (Optional)
                            </label>
                            {{ form.image }}
                            {% if form.image.errors %}
                                <div class="text-danger small">{{ form.image.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Correct Answer (for fill-in-the-blank, true/false, short answer) -->
                    <div id="correct-answer-section" class="mb-3">
                        <label for="{{ form.correct_answer.id_for_label }}" class="form-label">
                            Correct Answer <span class="text-danger">*</span>
                        </label>
                        {{ form.correct_answer }}
                        <div class="form-text">
                            <strong>For Fill-in-the-Blank/Short Answer:</strong> Enter the correct answer(s). For multiple acceptable answers, separate with commas (e.g., "smart, clever, intelligent").<br>
                            <strong>For True/False:</strong> Enter "True" or "False".<br>
                            <strong>For Multiple Choice:</strong> This field is ignored - use the answer choices below.
                        </div>
                        {% if form.correct_answer.errors %}
                            <div class="text-danger small">{{ form.correct_answer.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.explanation.id_for_label }}" class="form-label">
                            Explanation
                        </label>
                        {{ form.explanation }}
                        <div class="form-text">
                            Explain why the correct answer is right
                        </div>
                        {% if form.explanation.errors %}
                            <div class="text-danger small">{{ form.explanation.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <!-- Answer Choices (for multiple choice) -->
                    <div id="answer-choices-section" style="display: none;">
                        <h6 class="border-bottom pb-2 mb-3">Answer Choices</h6>
                        {{ formset.management_form }}
                        <div id="answer-choices-container">
                            {% for form in formset %}
                                <div class="answer-choice-form mb-3">
                                    <div class="row">
                                        <div class="col-md-10">
                                            <label class="form-label">Choice {{ forloop.counter }}</label>
                                            {{ form.choice_text }}
                                            {% if form.choice_text.errors %}
                                                <div class="text-danger small">{{ form.choice_text.errors.0 }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-2 d-flex align-items-end">
                                            <div class="form-check">
                                                {{ form.is_correct }}
                                                <label class="form-check-label">Correct</label>
                                            </div>
                                        </div>
                                    </div>
                                    {{ form.id }}
                                    {% if form.DELETE %}
                                        {{ form.DELETE }}
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    Active
                                </label>
                            </div>
                            <div class="form-text">
                                Only active questions will be used in quizzes
                            </div>
                            {% if form.is_active.errors %}
                                <div class="text-danger small">{{ form.is_active.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Preview Section -->
                    <div class="mb-4">
                        <h6>Preview</h6>
                        <div class="border rounded p-3 bg-light">
                            <div class="d-flex align-items-start">
                                <div class="me-3">
                                    <span id="preview-points" class="badge bg-primary">1</span>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 id="preview-question" class="mb-2">Question text will appear here</h6>
                                    <div id="preview-choices" class="mb-2">
                                        <!-- Choices will be populated by JavaScript -->
                                    </div>
                                    <small class="text-muted">
                                        <span id="preview-topic">Topic</span> •
                                        <span id="preview-difficulty">Difficulty</span> •
                                        <span id="preview-type">Type</span>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'admin_panel:questions' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Questions
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Create Question
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const questionTypeSelect = document.getElementById('{{ form.question_type.id_for_label }}');
    const topicSelect = document.getElementById('{{ form.topic.id_for_label }}');
    const questionTextInput = document.getElementById('{{ form.question_text.id_for_label }}');
    const difficultySelect = document.getElementById('{{ form.difficulty.id_for_label }}');
    const pointsInput = document.getElementById('{{ form.points.id_for_label }}');

    const answerChoicesSection = document.getElementById('answer-choices-section');
    const correctAnswerSection = document.getElementById('correct-answer-section');
    const previewQuestion = document.getElementById('preview-question');
    const previewTopic = document.getElementById('preview-topic');
    const previewDifficulty = document.getElementById('preview-difficulty');
    const previewType = document.getElementById('preview-type');
    const previewPoints = document.getElementById('preview-points');
    const previewChoices = document.getElementById('preview-choices');

    function toggleAnswerSections() {
        if (questionTypeSelect.value === 'multiple_choice') {
            answerChoicesSection.style.display = 'block';
            correctAnswerSection.style.display = 'none';
        } else {
            answerChoicesSection.style.display = 'none';
            correctAnswerSection.style.display = 'block';
        }
        updatePreview();
    }

    function updatePreview() {
        // Update question text
        previewQuestion.textContent = questionTextInput.value || 'Question text will appear here';

        // Update topic
        const selectedTopic = topicSelect.options[topicSelect.selectedIndex];
        previewTopic.textContent = selectedTopic.text || 'Topic';

        // Update difficulty
        const selectedDifficulty = difficultySelect.options[difficultySelect.selectedIndex];
        previewDifficulty.textContent = selectedDifficulty.text || 'Difficulty';

        // Update type
        const selectedType = questionTypeSelect.options[questionTypeSelect.selectedIndex];
        previewType.textContent = selectedType.text || 'Type';

        // Update points
        previewPoints.textContent = pointsInput.value || '1';

        // Update choices for multiple choice
        if (questionTypeSelect.value === 'multiple_choice') {
            const choiceInputs = document.querySelectorAll('.answer-choice-form input[type="text"]');
            const correctInputs = document.querySelectorAll('.answer-choice-form input[type="checkbox"]');

            let choicesHtml = '';
            choiceInputs.forEach((input, index) => {
                if (input.value.trim()) {
                    const isCorrect = correctInputs[index] && correctInputs[index].checked;
                    const letter = String.fromCharCode(65 + index); // A, B, C, D
                    choicesHtml += `<div class="mb-1 ${isCorrect ? 'text-success fw-bold' : ''}">
                        ${letter}. ${input.value}
                        ${isCorrect ? '<i class="fas fa-check ms-2"></i>' : ''}
                    </div>`;
                }
            });
            previewChoices.innerHTML = choicesHtml || '<div class="text-muted">Answer choices will appear here</div>';
        } else {
            previewChoices.innerHTML = '<div class="text-muted">Answer input field will appear here</div>';
        }
    }

    // Event listeners
    questionTypeSelect.addEventListener('change', toggleAnswerSections);
    topicSelect.addEventListener('change', updatePreview);
    questionTextInput.addEventListener('input', updatePreview);
    difficultySelect.addEventListener('change', updatePreview);
    pointsInput.addEventListener('input', updatePreview);

    // Listen for changes in answer choices
    document.addEventListener('input', function(e) {
        if (e.target.closest('.answer-choice-form')) {
            updatePreview();
        }
    });

    document.addEventListener('change', function(e) {
        if (e.target.closest('.answer-choice-form')) {
            updatePreview();
        }
    });

    // Initial setup
    toggleAnswerSections();
    updatePreview();
});
</script>
{% endblock %}
