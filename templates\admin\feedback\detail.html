{% extends 'admin_panel/base.html' %}
{% load static %}

{% block title %}Feedback Details{% endblock %}
{% block page_title %}Feedback Details{% endblock %}

{% block extra_css %}
<style>
    .rating-stars {
        color: #fbbf24;
        font-size: 1.2em;
    }
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
    }
    .resolved { background: #dcfce7; color: #166534; }
    .unresolved { background: #fef3c7; color: #92400e; }
    .type-badge {
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
    }
    .bug-report { background: #fee2e2; color: #dc2626; }
    .feature-request { background: #dbeafe; color: #2563eb; }
    .improvement { background: #f3e8ff; color: #7c3aed; }
    .general { background: #f0f9ff; color: #0369a1; }
    .info-card {
        border-left: 4px solid #3b82f6;
        background: #f8fafc;
    }
</style>
{% endblock %}

{% block content %}
<!-- Header Actions -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'core:feedback_list' %}">Feedback</a></li>
                <li class="breadcrumb-item active">Details</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="{% url 'core:feedback_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>
</div>

    <div class="row">
        <!-- Main Feedback Details -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Feedback Information</h5>
                    <div>
                        <span class="type-badge {{ feedback.feedback_type }}">
                            {{ feedback.get_feedback_type_display }}
                        </span>
                        <span class="status-badge {% if feedback.is_resolved %}resolved{% else %}unresolved{% endif %} ms-2">
                            {% if feedback.is_resolved %}Resolved{% else %}Unresolved{% endif %}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Rating -->
                    {% if feedback.rating %}
                    <div class="mb-3">
                        <label class="form-label fw-bold">Rating:</label>
                        <div class="rating-stars">
                            {% for i in "12345" %}
                                {% if forloop.counter <= feedback.rating %}
                                    <i class="fas fa-star"></i>
                                {% else %}
                                    <i class="far fa-star"></i>
                                {% endif %}
                            {% endfor %}
                            <span class="ms-2 text-muted">({{ feedback.rating }}/5)</span>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Message -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">Message:</label>
                        <div class="p-3 bg-light rounded">
                            {{ feedback.message|linebreaks }}
                        </div>
                    </div>

                    <!-- Screenshot -->
                    {% if feedback.include_screenshot %}
                    <div class="mb-3">
                        <label class="form-label fw-bold">Screenshot:</label>
                        <div class="alert alert-info">
                            <i class="fas fa-camera"></i> User indicated they would like to include a screenshot
                        </div>
                    </div>
                    {% endif %}

                    <!-- Page URL -->
                    {% if feedback.page_url %}
                    <div class="mb-3">
                        <label class="form-label fw-bold">Page URL:</label>
                        <div>
                            <a href="{{ feedback.page_url }}" target="_blank" class="text-decoration-none">
                                {{ feedback.page_url }}
                                <i class="fas fa-external-link-alt ms-1"></i>
                            </a>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Admin Notes -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Admin Notes</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'core:update_feedback_status' feedback.id %}">
                        {% csrf_token %}
                        <div class="mb-3">
                            <textarea name="admin_notes" class="form-control" rows="4" 
                                      placeholder="Add notes about this feedback...">{{ feedback.admin_notes }}</textarea>
                        </div>
                        <div class="d-flex gap-2">
                            <button type="submit" name="action" value="update_notes" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Notes
                            </button>
                            {% if not feedback.is_resolved %}
                            <button type="submit" name="action" value="resolve" class="btn btn-success">
                                <i class="fas fa-check"></i> Mark as Resolved
                            </button>
                            {% else %}
                            <button type="submit" name="action" value="unresolve" class="btn btn-warning">
                                <i class="fas fa-undo"></i> Mark as Unresolved
                            </button>
                            {% endif %}
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- User Information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">User Information</h5>
                </div>
                <div class="card-body">
                    {% if feedback.user %}
                        <div class="mb-2">
                            <strong>Name:</strong> {{ feedback.user.get_full_name|default:"Not provided" }}
                        </div>
                        <div class="mb-2">
                            <strong>Email:</strong> {{ feedback.user.email }}
                        </div>
                        <div class="mb-2">
                            <strong>Username:</strong> {{ feedback.user.username|default:"Not set" }}
                        </div>
                        <div class="mb-2">
                            <strong>Joined:</strong> {{ feedback.user.date_joined|date:"M d, Y" }}
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'admin:users_user_change' feedback.user.id %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-user"></i> View User Profile
                            </a>
                        </div>
                    {% elif feedback.email %}
                        <div class="mb-2">
                            <strong>Type:</strong> Anonymous User
                        </div>
                        <div class="mb-2">
                            <strong>Email:</strong> {{ feedback.email }}
                        </div>
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle"></i> This feedback was submitted by an anonymous user
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i> Anonymous feedback with no contact information
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Technical Details -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Technical Details</h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Submitted:</strong> {{ feedback.created_at|date:"M d, Y H:i:s" }}
                    </div>
                    <div class="mb-2">
                        <strong>Updated:</strong> {{ feedback.updated_at|date:"M d, Y H:i:s" }}
                    </div>
                    {% if feedback.ip_address %}
                    <div class="mb-2">
                        <strong>IP Address:</strong> {{ feedback.ip_address }}
                    </div>
                    {% endif %}
                    {% if feedback.user_agent %}
                    <div class="mb-2">
                        <strong>User Agent:</strong>
                        <small class="text-muted d-block">{{ feedback.user_agent|truncatechars:100 }}</small>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    {% if feedback.user and feedback.user.email %}
                    <a href="mailto:{{ feedback.user.email }}?subject=Re: Your feedback on Pentora" class="btn btn-outline-primary btn-sm d-block mb-2">
                        <i class="fas fa-envelope"></i> Email User
                    </a>
                    {% elif feedback.email %}
                    <a href="mailto:{{ feedback.email }}?subject=Re: Your feedback on Pentora" class="btn btn-outline-primary btn-sm d-block mb-2">
                        <i class="fas fa-envelope"></i> Email User
                    </a>
                    {% endif %}
                    
                    {% if feedback.page_url %}
                    <a href="{{ feedback.page_url }}" target="_blank" class="btn btn-outline-info btn-sm d-block mb-2">
                        <i class="fas fa-external-link-alt"></i> Visit Page
                    </a>
                    {% endif %}
                    
                    <button class="btn btn-outline-danger btn-sm d-block" onclick="deleteFeedback()">
                        <i class="fas fa-trash"></i> Delete Feedback
                    </button>
                </div>
            </div>
        </div>
    </div>

<script>
function deleteFeedback() {
    if (confirm('Are you sure you want to delete this feedback? This action cannot be undone.')) {
        // Create a form to delete the feedback
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{% url "core:bulk_feedback_action" %}';
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrfmiddlewaretoken';
        csrfToken.value = '{{ csrf_token }}';
        
        form.appendChild(csrfToken);
        document.body.appendChild(form);
        
        fetch(form.action, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken.value
            },
            body: JSON.stringify({
                action: 'delete',
                feedback_ids: ['{{ feedback.id }}']
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = '{% url "core:feedback_list" %}';
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the feedback');
        });
    }
}
</script>
{% endblock %}
