<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Access Denied - Pentora Admin</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .access-denied-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 500px;
            margin: 20px;
            text-align: center;
        }

        .access-denied-header {
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
            color: white;
            padding: 2rem;
        }

        .access-denied-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem auto;
            font-size: 2rem;
        }

        .access-denied-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 700;
        }

        .access-denied-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
            font-size: 1rem;
        }

        .access-denied-body {
            padding: 2rem;
        }

        .access-denied-body h2 {
            color: #374151;
            font-size: 1.3rem;
            margin-bottom: 1rem;
        }

        .access-denied-body p {
            color: #6b7280;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .btn-action {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            transition: all 0.3s ease;
            margin: 0.25rem;
        }

        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }

        .btn-secondary {
            background: #6b7280;
        }

        .btn-secondary:hover {
            background: #4b5563;
            box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
        }

        .actions {
            margin-top: 1.5rem;
        }

        @media (max-width: 576px) {
            .access-denied-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .access-denied-header {
                padding: 1.5rem;
            }
            
            .access-denied-body {
                padding: 1.5rem;
            }

            .btn-action {
                width: 100%;
                margin: 0.25rem 0;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="access-denied-container">
        <div class="access-denied-header">
            <div class="access-denied-icon">
                <i class="fas fa-ban"></i>
            </div>
            <h1>Access Denied</h1>
            <p>Admin Privileges Required</p>
        </div>
        
        <div class="access-denied-body">
            <h2>Sorry, you don't have permission</h2>
            <p>
                The admin panel is restricted to authorized administrators only. 
                If you believe you should have access, please contact your system administrator.
            </p>

            {% if user.is_authenticated %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    You are currently logged in as <strong>{{ user.first_name }} {{ user.last_name }}</strong>, 
                    but your account does not have admin privileges.
                </div>
            {% endif %}

            <div class="actions">
                <a href="{% url 'core:home' %}" class="btn-action">
                    <i class="fas fa-home me-2"></i>
                    Go to Main Site
                </a>
                
                {% if user.is_authenticated %}
                    <a href="{% url 'core:dashboard' %}" class="btn-action">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        My Dashboard
                    </a>
                    
                    <a href="{% url 'users:logout' %}" class="btn-action btn-secondary">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        Sign Out
                    </a>
                {% else %}
                    <a href="{% url 'users:login' %}" class="btn-action btn-secondary">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        Sign In
                    </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
