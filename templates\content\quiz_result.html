{% extends 'base.html' %}

{% block title %}Quiz Results - Pentora{% endblock %}

{% block extra_css %}
<style>
    .results-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 1rem;
    }

    .results-card {
        background: white;
        border-radius: 16px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 1rem;
    }

    .score-circle {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        font-weight: bold;
        margin: 0 auto;
    }

    .score-excellent { background: linear-gradient(135deg, #10B981, #059669); color: white; }
    .score-good { background: linear-gradient(135deg, #3B82F6, #1D4ED8); color: white; }
    .score-fair { background: linear-gradient(135deg, #F59E0B, #D97706); color: white; }
    .score-poor { background: linear-gradient(135deg, #EF4444, #DC2626); color: white; }

    .stat-card {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        text-align: center;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .question-card {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1rem;
        border-left: 4px solid #e5e7eb;
    }

    .question-card.correct {
        border-left-color: #10B981;
        background: linear-gradient(to right, #f0fdf4, #ffffff);
    }

    .question-card.incorrect {
        border-left-color: #EF4444;
        background: linear-gradient(to right, #fef2f2, #ffffff);
    }

    .answer-option {
        padding: 0.75rem;
        border-radius: 8px;
        margin-bottom: 0.5rem;
        border: 2px solid #e5e7eb;
    }

    .answer-option.correct {
        background-color: #d1fae5;
        border-color: #10B981;
        color: #065f46;
    }

    .answer-option.incorrect {
        background-color: #fee2e2;
        border-color: #EF4444;
        color: #991b1b;
    }

    .action-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .action-btn:hover {
        color: white;
        text-decoration: none;
        opacity: 0.9;
    }

    .action-btn.secondary {
        background: white;
        color: #374151;
        border: 2px solid #e5e7eb;
    }

    .action-btn.secondary:hover {
        background: #f9fafb;
        color: #374151;
    }
</style>
{% endblock %}

{% block content %}

<div class="results-container">
    <div class="max-w-4xl mx-auto">

        {% if error %}
        <!-- Error Message -->
        <div class="text-center text-white mb-6">
            <h1 class="text-2xl sm:text-3xl font-bold mb-2">Oops!</h1>
            <p class="text-white/90 text-sm sm:text-base">{{ error }}</p>
        </div>

        <div class="results-card p-4 sm:p-6 text-center">
            <div class="mb-4">
                <i class="fas fa-exclamation-triangle text-yellow-500 text-4xl mb-4"></i>
                <h2 class="text-lg font-bold text-gray-800 mb-2">Unable to Load Quiz Results</h2>
                <p class="text-gray-600 mb-4">{{ error }}</p>
            </div>
            <a href="/dashboard/" class="action-btn">
                <i class="fas fa-home mr-2"></i>
                Back to Dashboard
            </a>
        </div>

        {% else %}
        <!-- Header -->
        <div class="text-center text-white mb-6">
            <h1 class="text-2xl sm:text-3xl font-bold mb-2">Quiz Complete!</h1>
            <p class="text-white/90 text-sm sm:text-base">
                {% if topic and topic.title %}
                    Here are your results for "{{ topic.title }}"
                {% else %}
                    Here are your quiz results
                {% endif %}
            </p>
        </div>

        <!-- Main Results Card -->
        <div class="results-card p-4 sm:p-6 text-center mb-4">
            <!-- Score Circle -->
            <div class="score-circle {% if percentage >= 90 %}score-excellent{% elif percentage >= 80 %}score-good{% elif percentage >= 60 %}score-fair{% else %}score-poor{% endif %} mb-4">
                <span>{{ percentage|floatformat:0 }}%</span>
            </div>

            <!-- Performance Badge -->
            <div class="mb-4">
                <span class="inline-block px-4 py-2 rounded-full text-sm font-semibold
                    {% if percentage >= 90 %}bg-green-100 text-green-800
                    {% elif percentage >= 80 %}bg-blue-100 text-blue-800
                    {% elif percentage >= 60 %}bg-yellow-100 text-yellow-800
                    {% else %}bg-red-100 text-red-800{% endif %}">
                    {% if percentage >= 90 %}Excellent!{% elif percentage >= 80 %}Good Job!{% elif percentage >= 60 %}Well Done!{% else %}Keep Trying!{% endif %}
                </span>
            </div>

            <!-- Stats Grid -->
            <div class="grid grid-cols-3 gap-3 sm:gap-4">
                <div class="stat-card">
                    <div class="text-xl sm:text-2xl font-bold text-green-600">{{ score }}</div>
                    <div class="text-xs sm:text-sm text-gray-600">Correct</div>
                </div>
                <div class="stat-card">
                    <div class="text-xl sm:text-2xl font-bold text-blue-600">{{ total }}</div>
                    <div class="text-xs sm:text-sm text-gray-600">Total</div>
                </div>
                <div class="stat-card">
                    <div class="text-xl sm:text-2xl font-bold text-purple-600">{{ time_display }}</div>
                    <div class="text-xs sm:text-sm text-gray-600">Time</div>
                </div>
            </div>
        </div>

        <!-- Actions Card -->
        <div class="results-card p-4 sm:p-6 mb-4">
            <h2 class="text-lg font-bold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-rocket text-purple-600 mr-2"></i>
                What's Next?
            </h2>

            <!-- Pass/Fail Status -->
            <div class="mb-4 p-3 rounded-lg {% if passed %}bg-green-50 border border-green-200{% else %}bg-red-50 border border-red-200{% endif %}">
                <div class="flex items-center justify-center">
                    <span class="text-sm font-semibold {% if passed %}text-green-800{% else %}text-red-800{% endif %}">
                        {% if passed %}
                            <i class="fas fa-check mr-1"></i>Passed! You scored {{ percentage|floatformat:0 }}% (required: {{ pass_percentage }}%)
                        {% else %}
                            <i class="fas fa-times mr-1"></i>Not passed. You scored {{ percentage|floatformat:0 }}% (required: {{ pass_percentage }}%)
                        {% endif %}
                    </span>
                </div>
            </div>

            <!-- What's Next Section -->
            {% if next_step %}
            <div class="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
                <h3 class="text-lg font-bold text-gray-800 mb-2 flex items-center">
                    <i class="{{ next_step.icon }} text-blue-600 mr-2"></i>
                    What's Next?
                </h3>

                <!-- Enhanced description with topic and subject info -->
                <div class="mb-3">
                    <p class="text-gray-700 mb-2">{{ next_step.description }}</p>

                    {% if next_step.topic_name %}
                    <div class="text-sm text-gray-600 bg-white p-2 rounded border-l-4 border-blue-400">
                        <strong>Topic:</strong> {{ next_step.topic_name }}
                        {% if next_step.subject_name %}
                        <br><strong>Subject:</strong> {{ next_step.subject_name }}
                        {% endif %}
                        {% if next_step.grade %}
                        <br><strong>Grade:</strong> {{ next_step.grade }}
                        {% endif %}
                    </div>
                    {% endif %}
                </div>

                <!-- Action buttons -->
                <div class="flex flex-wrap gap-2">
                    <!-- Primary action button (Quiz/Exam) -->
                    {% if next_step.quiz_url %}
                    <a href="{{ next_step.quiz_url }}" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200">
                        <i class="{{ next_step.icon }} mr-2"></i>
                        {{ next_step.button_text }}
                    </a>
                    {% endif %}

                    <!-- Learn button -->
                    {% if next_step.learn_url and next_step.learn_button_text %}
                    <a href="{{ next_step.learn_url }}" class="inline-flex items-center px-4 py-2 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 transition-colors duration-200">
                        <i class="fas fa-book-open mr-2"></i>
                        {{ next_step.learn_button_text }}
                    </a>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                {% if topic and topic.id %}
                <a href="{% url 'content:quiz' topic.id %}" class="action-btn secondary">
                    <i class="fas fa-redo mr-2"></i>
                    Retake Quiz
                </a>
                <a href="{% url 'content:study_notes' topic.id %}" class="action-btn secondary">
                    <i class="fas fa-book mr-2"></i>
                    Review Study Materials
                </a>
                {% else %}
                <a href="/dashboard/" class="action-btn secondary">
                    <i class="fas fa-redo mr-2"></i>
                    Take Another Quiz
                </a>
                <a href="/dashboard/" class="action-btn secondary">
                    <i class="fas fa-book mr-2"></i>
                    Browse Topics
                </a>
                {% endif %}
                <a href="/dashboard/" class="action-btn secondary">
                    <i class="fas fa-home mr-2"></i>
                    Back to Dashboard
                </a>
                {% if subject and subject.id and level and level.id %}
                <a href="{% url 'subjects:topics' subject.id level.id %}" class="action-btn secondary">
                    <i class="fas fa-list mr-2"></i>
                    View All Topics
                </a>
                {% else %}
                <a href="/dashboard/" class="action-btn secondary">
                    <i class="fas fa-graduation-cap mr-2"></i>
                    Browse Subjects
                </a>
                {% endif %}
            </div>
        </div>

        <!-- Question Review -->
        <div class="results-card p-4 sm:p-6">
            <h2 class="text-lg font-bold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-search text-blue-600 mr-2"></i>
                Question Review
            </h2>

            {% if question_reviews %}
                {% for q in question_reviews %}
                    <div class="question-card {% if q.is_correct %}correct{% else %}incorrect{% endif %} mb-4">
                        <div class="flex items-start justify-between mb-3">
                            <h3 class="font-bold text-base">Question {{ forloop.counter }}</h3>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold {% if q.is_correct %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                <i class="fas {% if q.is_correct %}fa-check{% else %}fa-times{% endif %} mr-1"></i>
                                {% if q.is_correct %}Correct{% else %}Incorrect{% endif %}
                            </span>
                        </div>
                        <p class="text-gray-800 mb-3 text-sm">{{ q.question_text }}</p>
                        <div class="space-y-2 mb-3">
                            {% if q.user_answer %}
                                <div class="answer-option {% if q.is_correct %}correct{% else %}incorrect{% endif %}">
                                    <i class="fas {% if q.is_correct %}fa-check{% else %}fa-times{% endif %} mr-2"></i>
                                    Your answer: {{ q.user_answer }}
                                </div>
                            {% else %}
                                <div class="answer-option incorrect">
                                    <i class="fas fa-clock mr-2"></i>
                                    No answer selected (Time expired)
                                </div>
                            {% endif %}
                            {% if not q.is_correct and q.correct_answers %}
                                <div class="answer-option correct">
                                    <i class="fas fa-check mr-2"></i>
                                    Correct answer: {{ q.correct_answers|join:', ' }}
                                </div>
                            {% endif %}
                        </div>
                        {% if q.explanation %}
                            <div class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                <p class="text-sm text-blue-800">
                                    <strong>Explanation:</strong> {{ q.explanation }}
                                </p>
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}
            {% else %}
                <div class="text-center py-8">
                    <i class="fas fa-info-circle text-blue-500 text-3xl mb-4"></i>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Question Review Not Available</h3>
                    <p class="text-gray-600">Detailed question review is not available for this quiz session.</p>
                </div>
            {% endif %}
        </div>

        {% endif %} <!-- End error handling -->
    </div>
</div>



<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Quiz results page loaded');

    // Clear any stored quiz results since we're now showing the results
    sessionStorage.removeItem('quizResults');
});
</script>
{% endblock %}
