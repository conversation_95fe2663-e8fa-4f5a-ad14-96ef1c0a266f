# Generated by Django 5.2.1 on 2025-07-05 14:54

import django.core.validators
import uuid
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BillingEvent',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('event_type', models.CharField(choices=[('subscription_created', 'Subscription Created'), ('subscription_updated', 'Subscription Updated'), ('subscription_canceled', 'Subscription Canceled'), ('payment_succeeded', 'Payment Succeeded'), ('payment_failed', 'Payment Failed'), ('trial_started', 'Trial Started'), ('trial_ending', 'Trial Ending'), ('invoice_created', 'Invoice Created'), ('refund_created', 'Refund Created')], max_length=30)),
                ('description', models.TextField()),
                ('metadata', models.J<PERSON><PERSON>ield(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Billing Event',
                'verbose_name_plural': 'Billing Events',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BillingSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('billing_enabled', models.BooleanField(default=False, help_text='Enable billing system globally')),
                ('free_trial_days', models.PositiveIntegerField(default=30, help_text='Number of free trial days for new users')),
                ('grace_period_days', models.PositiveIntegerField(default=7, help_text='Grace period after subscription expires')),
                ('stripe_enabled', models.BooleanField(default=False)),
                ('stripe_public_key', models.CharField(blank=True, max_length=200)),
                ('stripe_secret_key', models.CharField(blank=True, max_length=200)),
                ('stripe_webhook_secret', models.CharField(blank=True, max_length=200)),
                ('paypal_enabled', models.BooleanField(default=False)),
                ('paypal_client_id', models.CharField(blank=True, max_length=200)),
                ('paypal_client_secret', models.CharField(blank=True, max_length=200)),
                ('paypal_sandbox', models.BooleanField(default=True)),
                ('paystack_enabled', models.BooleanField(default=True, help_text='Enable Paystack payments')),
                ('paystack_public_key', models.CharField(blank=True, help_text='Paystack public key', max_length=200)),
                ('paystack_secret_key', models.CharField(blank=True, help_text='Paystack secret key', max_length=200)),
                ('paystack_test_mode', models.BooleanField(default=True, help_text='Use Paystack test mode')),
                ('base_currency', models.CharField(choices=[('GHS', 'Ghana Cedi'), ('USD', 'US Dollar'), ('EUR', 'Euro'), ('GBP', 'British Pound')], default='GHS', help_text='Base currency for pricing', max_length=3)),
                ('usd_to_ghs_rate', models.DecimalField(decimal_places=4, default=Decimal('12.0000'), help_text='Exchange rate from USD to GHS (manual override)', max_digits=10)),
                ('auto_currency_conversion', models.BooleanField(default=False, help_text='Automatically fetch exchange rates (requires API)')),
                ('last_rate_update', models.DateTimeField(blank=True, help_text='Last exchange rate update', null=True)),
                ('announcement_sent', models.BooleanField(default=False, help_text='Whether billing activation announcement has been sent')),
                ('send_billing_emails', models.BooleanField(default=True, help_text='Send billing-related emails to users')),
                ('billing_email_from', models.EmailField(default='<EMAIL>', help_text='From email for billing notifications', max_length=254)),
                ('show_billing_announcement', models.BooleanField(default=False, help_text='Show billing announcement banner')),
                ('announcement_title', models.CharField(blank=True, max_length=200)),
                ('announcement_message', models.TextField(blank=True)),
                ('announcement_type', models.CharField(choices=[('info', 'Info'), ('warning', 'Warning'), ('success', 'Success'), ('error', 'Error')], default='info', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Billing Settings',
                'verbose_name_plural': 'Billing Settings',
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='USD', max_length=3)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('succeeded', 'Succeeded'), ('failed', 'Failed'), ('canceled', 'Canceled'), ('refunded', 'Refunded'), ('partially_refunded', 'Partially Refunded')], default='pending', max_length=20)),
                ('payment_type', models.CharField(choices=[('subscription', 'Subscription'), ('one_time', 'One-time Payment'), ('upgrade', 'Plan Upgrade'), ('refund', 'Refund')], default='subscription', max_length=20)),
                ('provider', models.CharField(blank=True, max_length=20)),
                ('provider_payment_id', models.CharField(blank=True, max_length=100)),
                ('provider_fee', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('description', models.TextField(blank=True)),
                ('failure_reason', models.TextField(blank=True)),
                ('receipt_url', models.URLField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Payment',
                'verbose_name_plural': 'Payments',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PaymentMethod',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('payment_type', models.CharField(choices=[('card', 'Credit/Debit Card'), ('paypal', 'PayPal'), ('bank', 'Bank Transfer')], max_length=20)),
                ('is_default', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('card_last_four', models.CharField(blank=True, max_length=4)),
                ('card_brand', models.CharField(blank=True, max_length=20)),
                ('card_exp_month', models.PositiveIntegerField(blank=True, null=True)),
                ('card_exp_year', models.PositiveIntegerField(blank=True, null=True)),
                ('stripe_payment_method_id', models.CharField(blank=True, max_length=100)),
                ('paypal_payment_method_id', models.CharField(blank=True, max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Payment Method',
                'verbose_name_plural': 'Payment Methods',
            },
        ),
        migrations.CreateModel(
            name='SubscriptionPlan',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('monthly_price', models.DecimalField(decimal_places=2, help_text='Monthly subscription price in USD', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('yearly_price', models.DecimalField(blank=True, decimal_places=2, help_text='Yearly subscription price in USD (leave blank to disable yearly option)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('monthly_price_ghs', models.DecimalField(blank=True, decimal_places=2, help_text='Monthly subscription price in GHS (auto-calculated if not set)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('yearly_price_ghs', models.DecimalField(blank=True, decimal_places=2, help_text='Yearly subscription price in GHS (auto-calculated if not set)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('price', models.DecimalField(decimal_places=2, help_text='Legacy price field - will be deprecated', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('billing_cycle', models.CharField(choices=[('monthly', 'Monthly'), ('quarterly', 'Quarterly'), ('yearly', 'Yearly'), ('lifetime', 'Lifetime')], default='monthly', max_length=20)),
                ('max_subjects', models.PositiveIntegerField(blank=True, help_text='Maximum subjects user can access (null = unlimited, overridden by allowed_subjects if set)', null=True)),
                ('max_quizzes_per_day', models.PositiveIntegerField(blank=True, help_text='Maximum quizzes per day (null = unlimited)', null=True)),
                ('priority_support', models.BooleanField(default=False)),
                ('advanced_analytics', models.BooleanField(default=False)),
                ('offline_access', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('is_featured', models.BooleanField(default=False)),
                ('sort_order', models.PositiveIntegerField(default=0)),
                ('stripe_price_id', models.CharField(blank=True, max_length=100)),
                ('paypal_plan_id', models.CharField(blank=True, max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Subscription Plan',
                'verbose_name_plural': 'Subscription Plans',
                'ordering': ['sort_order', 'price'],
            },
        ),
        migrations.CreateModel(
            name='UserSubscription',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('active', 'Active'), ('trialing', 'Trial'), ('past_due', 'Past Due'), ('canceled', 'Canceled'), ('unpaid', 'Unpaid'), ('incomplete', 'Incomplete'), ('incomplete_expired', 'Incomplete Expired'), ('paused', 'Paused')], default='trialing', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('trial_start', models.DateTimeField(blank=True, null=True)),
                ('trial_end', models.DateTimeField(blank=True, null=True)),
                ('current_period_start', models.DateTimeField(blank=True, null=True)),
                ('current_period_end', models.DateTimeField(blank=True, null=True)),
                ('canceled_at', models.DateTimeField(blank=True, null=True)),
                ('ended_at', models.DateTimeField(blank=True, null=True)),
                ('stripe_subscription_id', models.CharField(blank=True, max_length=100)),
                ('paypal_subscription_id', models.CharField(blank=True, max_length=100)),
                ('auto_renew', models.BooleanField(default=True)),
                ('cancel_at_period_end', models.BooleanField(default=False)),
                ('notes', models.TextField(blank=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'User Subscription',
                'verbose_name_plural': 'User Subscriptions',
            },
        ),
    ]
