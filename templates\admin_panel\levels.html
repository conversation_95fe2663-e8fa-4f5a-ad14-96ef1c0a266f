{% extends 'admin_panel/base.html' %}

{% block title %}Manage Class Levels{% endblock %}
{% block page_title %}Manage Class Levels{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">
        <i class="fas fa-layer-group me-2"></i>
        Class Levels Management
    </h4>
    <a href="{% url 'admin_panel:create_level' %}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        Add New Level
    </a>
</div>

<!-- Levels Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">All Class Levels</h5>
    </div>
    <div class="card-body">
        {% if levels %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Subject</th>
                            <th>Level Name</th>
                            <th>Level Number</th>
                            <th>Description</th>
                            <th>Topics</th>
                            <th>Questions</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for level in levels %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="d-flex align-items-center justify-content-center me-2"
                                             style="width: 30px; height: 30px; background-color: {{ level.subject.color }}; border-radius: 6px; color: white;">
                                            <i class="fas {{ level.subject.icon }} fa-sm"></i>
                                        </div>
                                        <span>{{ level.subject.name }}</span>
                                    </div>
                                </td>
                                <td>
                                    <strong>{{ level.name }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ level.level_number }}</span>
                                </td>
                                <td>
                                    <span class="text-muted">{{ level.description|truncatechars:40 }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ level.topics_count }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-success">{{ level.questions_count }}</span>
                                </td>
                                <td>
                                    {% if level.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'admin_panel:edit_level' level.id %}"
                                           class="btn btn-sm btn-outline-primary"
                                           title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-sm btn-outline-danger"
                                                title="Delete"
                                                onclick="confirmDelete('{{ level.name }}', '{% url 'admin_panel:delete_level' level.id %}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if levels.has_other_pages %}
                <nav aria-label="Levels pagination">
                    <ul class="pagination justify-content-center">
                        {% if levels.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ levels.previous_page_number }}">Previous</a>
                            </li>
                        {% endif %}

                        {% for num in levels.paginator.page_range %}
                            {% if levels.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > levels.number|add:'-3' and num < levels.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if levels.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ levels.next_page_number }}">Next</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-layer-group fs-1 text-muted mb-3"></i>
                <h5 class="text-muted">No class levels found</h5>
                <p class="text-muted">Start by creating your first class level.</p>
                <a href="{% url 'admin_panel:create_level' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Create First Level
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Summary Cards -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-primary">{{ levels|length }}</h4>
                <p class="mb-0">Total Levels</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-success">
                    {% for level in levels %}
                        {% if level.is_active %}{{ forloop.counter0|add:1 }}{% endif %}
                    {% empty %}0{% endfor %}
                </h4>
                <p class="mb-0">Active Levels</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-info">
                    {% for level in levels %}{{ level.topics_count|add:0 }}{% empty %}0{% endfor %}
                </h4>
                <p class="mb-0">Total Topics</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-warning">
                    {% for level in levels %}{{ level.questions_count|add:0 }}{% empty %}0{% endfor %}
                </h4>
                <p class="mb-0">Total Questions</p>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the level "<span id="deleteItemName"></span>"?</p>
                <p class="text-danger"><strong>Warning:</strong> This will also delete all associated topics and questions.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(itemName, deleteUrl) {
    document.getElementById('deleteItemName').textContent = itemName;
    document.getElementById('deleteForm').action = deleteUrl;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
