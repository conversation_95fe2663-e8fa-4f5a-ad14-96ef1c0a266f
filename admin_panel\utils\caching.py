"""
Caching utilities for improved performance
"""

from django.core.cache import cache, caches
from django.db.models import QuerySet
import hashlib
import json
from functools import wraps


def cache_query_result(cache_key_prefix, timeout=300, cache_alias='queries'):
    """
    Decorator to cache query results
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            key_data = {
                'args': str(args),
                'kwargs': str(kwargs),
                'func': func.__name__
            }
            key_hash = hashlib.md5(json.dumps(key_data, sort_keys=True).encode()).hexdigest()
            cache_key = f"{cache_key_prefix}_{key_hash}"
            
            # Try to get from cache
            query_cache = caches[cache_alias]
            result = query_cache.get(cache_key)
            
            if result is not None:
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            query_cache.set(cache_key, result, timeout)
            
            return result
        return wrapper
    return decorator


class QueryCache:
    """
    Utility class for caching database queries
    """
    
    def __init__(self, cache_alias='queries', default_timeout=600):
        self.cache = caches[cache_alias]
        self.default_timeout = default_timeout
    
    def get_or_set_queryset(self, cache_key, queryset_func, timeout=None):
        """
        Get queryset from cache or execute and cache it
        """
        timeout = timeout or self.default_timeout
        
        # Try to get from cache
        result = self.cache.get(cache_key)
        if result is not None:
            return result
        
        # Execute queryset and convert to list for caching
        if callable(queryset_func):
            queryset = queryset_func()
        else:
            queryset = queryset_func
            
        if isinstance(queryset, QuerySet):
            result = list(queryset)
        else:
            result = queryset
        
        # Cache the result
        self.cache.set(cache_key, result, timeout)
        return result
    
    def invalidate_pattern(self, pattern):
        """
        Invalidate cache keys matching a pattern
        """
        # Note: This is a simplified implementation
        # In production, you might want to use Redis with pattern matching
        pass


class FilterOptionsCache:
    """
    Cache for filter options (subjects, class levels, topics)
    """
    
    def __init__(self):
        self.cache = caches['queries']
        self.timeout = 1800  # 30 minutes
    
    def get_subjects(self, force_refresh=False):
        """Get cached subjects list"""
        cache_key = 'filter_subjects'
        
        if force_refresh:
            self.cache.delete(cache_key)
        
        return self.cache.get_or_set(
            cache_key,
            self._fetch_subjects,
            self.timeout
        )
    
    def get_class_levels(self, force_refresh=False):
        """Get cached class levels list"""
        cache_key = 'filter_class_levels'
        
        if force_refresh:
            self.cache.delete(cache_key)
        
        return self.cache.get_or_set(
            cache_key,
            self._fetch_class_levels,
            self.timeout
        )
    
    def get_topics(self, class_level=None, subject=None, force_refresh=False):
        """Get cached topics list with optional filtering"""
        cache_key = f'filter_topics_{class_level}_{subject}'
        
        if force_refresh:
            self.cache.delete(cache_key)
        
        return self.cache.get_or_set(
            cache_key,
            lambda: self._fetch_topics(class_level, subject),
            self.timeout
        )
    
    def _fetch_subjects(self):
        """Fetch subjects from database"""
        from subjects.models import Subject
        return list(Subject.objects.filter(is_active=True).order_by('name').values('id', 'name'))
    
    def _fetch_class_levels(self):
        """Fetch class levels from database"""
        from subjects.models import ClassLevel
        return list(ClassLevel.objects.filter(is_active=True).order_by('level_number').values('id', 'level_number', 'name'))
    
    def _fetch_topics(self, class_level=None, subject=None):
        """Fetch topics from database with optional filtering"""
        from subjects.models import Topic
        
        queryset = Topic.objects.filter(is_active=True).select_related('class_level__subject')
        
        if class_level:
            queryset = queryset.filter(class_level__level_number=class_level)
        if subject:
            queryset = queryset.filter(class_level__subject_id=subject)
        
        return list(queryset.order_by('class_level__level_number', 'title').values(
            'id', 'title', 'class_level__level_number', 'class_level__subject__name'
        ))
    
    def invalidate_all(self):
        """Invalidate all filter caches"""
        self.cache.delete_many([
            'filter_subjects',
            'filter_class_levels'
        ])
        
        # Delete topic caches (simplified - in production you'd track these better)
        # This is a basic implementation


class DuplicateDetectionCache:
    """
    Specialized cache for duplicate detection results
    """
    
    def __init__(self):
        self.cache = caches['default']
        self.timeout = 1800  # 30 minutes
    
    def get_cache_key(self, class_level, subject, similarity_threshold):
        """Generate cache key for duplicate detection"""
        key_data = f"duplicates_{class_level}_{subject}_{similarity_threshold}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get_duplicates(self, class_level, subject, similarity_threshold):
        """Get cached duplicate detection results"""
        cache_key = self.get_cache_key(class_level, subject, similarity_threshold)
        return self.cache.get(cache_key)
    
    def set_duplicates(self, class_level, subject, similarity_threshold, results):
        """Cache duplicate detection results"""
        cache_key = self.get_cache_key(class_level, subject, similarity_threshold)
        self.cache.set(cache_key, results, self.timeout)
    
    def invalidate_duplicates(self, class_level=None, subject=None):
        """Invalidate duplicate detection cache"""
        # In a real implementation, you'd track and delete specific keys
        # For now, we'll use a simple approach
        if class_level or subject:
            # Delete specific cache entries
            pass
        else:
            # Clear all duplicate detection caches
            pass


# Global cache instances
filter_cache = FilterOptionsCache()
duplicate_cache = DuplicateDetectionCache()
query_cache = QueryCache()


def warm_up_caches():
    """
    Warm up frequently used caches
    """
    try:
        # Warm up filter caches
        filter_cache.get_subjects()
        filter_cache.get_class_levels()
        
        print("✅ Cache warm-up completed")
    except Exception as e:
        print(f"⚠️ Cache warm-up failed: {e}")


def clear_all_caches():
    """
    Clear all application caches
    """
    try:
        for cache_alias in ['default', 'queries', 'pagination']:
            caches[cache_alias].clear()
        
        print("✅ All caches cleared")
    except Exception as e:
        print(f"⚠️ Cache clearing failed: {e}")
