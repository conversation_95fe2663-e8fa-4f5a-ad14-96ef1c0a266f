#!/usr/bin/env python3
"""
Step-by-step analysis of Grade_9_questions.csv to identify formatting issues
"""
import csv

def analyze_csv_structure():
    """Analyze the CSV structure to identify problematic rows"""
    
    expected_header = [
        'subject_name', 'class_level_name', 'topic_title', 'question_text', 
        'question_type', 'correct_answer', 'explanation', 'difficulty', 
        'points', 'time_limit', 'choice_a', 'choice_b', 'choice_c', 'choice_d'
    ]
    
    valid_question_types = ['multiple_choice', 'fill_blank', 'true_false', 'short_answer']
    valid_difficulties = ['easy', 'medium', 'hard']
    valid_answers = ['a', 'b', 'c', 'd', 'true', 'false']
    
    good_rows = []
    problematic_rows = []
    
    print("🔍 ANALYZING CSV STRUCTURE...")
    print("=" * 60)
    
    with open('Grade_9_questions.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        
        for row_num, row in enumerate(reader, 1):
            # Remove empty trailing fields
            while row and row[-1] == '':
                row.pop()
            
            if row_num == 1:
                print(f"📋 HEADER ROW:")
                print(f"   Expected: {len(expected_header)} fields")
                print(f"   Found: {len(row)} fields")
                print(f"   Header: {row[:14]}")
                print()
                continue
            
            # Analyze data rows
            if len(row) >= 14:
                # Check key indicators to see if row is properly formatted
                question_type = row[4].strip().lower() if len(row) > 4 else ""
                correct_answer = row[5].strip().lower() if len(row) > 5 else ""
                difficulty = row[7].strip().lower() if len(row) > 7 else ""
                
                # Check if points field is numeric
                points_field = row[8] if len(row) > 8 else ""
                is_points_numeric = points_field.isdigit()
                
                # Check if time_limit field is numeric  
                time_field = row[9] if len(row) > 9 else ""
                is_time_numeric = time_field.isdigit()
                
                # Determine if row is properly formatted
                is_good_row = (
                    question_type in valid_question_types and
                    difficulty in valid_difficulties and
                    is_points_numeric and
                    is_time_numeric
                )
                
                if is_good_row:
                    good_rows.append(row_num)
                else:
                    problematic_rows.append({
                        'row_num': row_num,
                        'question_type': question_type,
                        'correct_answer': correct_answer,
                        'difficulty': difficulty,
                        'points': points_field,
                        'time_limit': time_field,
                        'issues': []
                    })
                    
                    # Identify specific issues
                    if question_type not in valid_question_types:
                        problematic_rows[-1]['issues'].append(f"Invalid question_type: '{question_type}'")
                    if difficulty not in valid_difficulties:
                        problematic_rows[-1]['issues'].append(f"Invalid difficulty: '{difficulty}'")
                    if not is_points_numeric:
                        problematic_rows[-1]['issues'].append(f"Non-numeric points: '{points_field}'")
                    if not is_time_numeric:
                        problematic_rows[-1]['issues'].append(f"Non-numeric time_limit: '{time_field}'")
    
    print(f"📊 ANALYSIS RESULTS:")
    print(f"   ✅ Good rows: {len(good_rows)}")
    print(f"   ❌ Problematic rows: {len(problematic_rows)}")
    print()
    
    if problematic_rows:
        print("🔍 SAMPLE PROBLEMATIC ROWS:")
        print("-" * 60)
        for i, prob_row in enumerate(problematic_rows[:10]):  # Show first 10
            print(f"Row {prob_row['row_num']}:")
            print(f"   Question Type: '{prob_row['question_type']}'")
            print(f"   Difficulty: '{prob_row['difficulty']}'")
            print(f"   Points: '{prob_row['points']}'")
            print(f"   Time Limit: '{prob_row['time_limit']}'")
            print(f"   Issues: {'; '.join(prob_row['issues'])}")
            print()
        
        if len(problematic_rows) > 10:
            print(f"   ... and {len(problematic_rows) - 10} more problematic rows")
    
    return good_rows, problematic_rows

if __name__ == "__main__":
    good_rows, problematic_rows = analyze_csv_structure()
    
    print("\n🎯 NEXT STEPS:")
    print("1. Fix header row (add class_level_name)")
    print("2. Identify patterns in problematic rows")
    print("3. Create targeted fixes for each pattern")
    print("4. Validate fixed data")
