{% extends 'base.html' %}

{% block title %}{{ level.name }} {{ subject.name }} - Pentora{% endblock %}

{% block extra_css %}
<style>
    /* Modern glassmorphism and animation styles */
    .backdrop-blur-sm {
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
    }

    /* Mobile-first design with proper contrast */
    .topic-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        min-height: 200px;
    }

    .topic-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    /* Text clamp for descriptions */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* Smooth gradient animations */
    @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .bg-gradient-to-r {
        background-size: 200% 200%;
        animation: gradientShift 6s ease infinite;
    }

    /* Cute animations */
    @keyframes bounce-in {
        0% { transform: scale(0.95); opacity: 0; }
        50% { transform: scale(1.02); }
        100% { transform: scale(1); opacity: 1; }
    }

    .topic-card {
        animation: bounce-in 0.6s ease-out;
    }

    .topic-card:nth-child(1) { animation-delay: 0.1s; }
    .topic-card:nth-child(2) { animation-delay: 0.2s; }
    .topic-card:nth-child(3) { animation-delay: 0.3s; }
    .topic-card:nth-child(4) { animation-delay: 0.4s; }
    .topic-card:nth-child(5) { animation-delay: 0.5s; }
    .topic-card:nth-child(6) { animation-delay: 0.6s; }

    /* Mobile optimizations for better readability */
    @media (max-width: 768px) {
        .topic-card {
            margin-bottom: 1rem;
            min-height: 180px;
            border-radius: 0;
            border-left: none;
            border-right: none;
            border-top: 1px solid #e5e7eb;
        }

        .topic-card:first-child {
            border-top: none;
        }

        /* Full width on mobile */
        .grid {
            grid-template-columns: 1fr !important;
            gap: 0 !important;
        }

        /* Larger text on mobile for better readability */
        .topic-card h3 {
            font-size: 1.25rem !important;
            line-height: 1.4 !important;
        }

        .topic-card p {
            font-size: 1rem !important;
            line-height: 1.6 !important;
        }

        /* Better button sizing on mobile */
        .topic-card a, .topic-card button {
            padding: 1rem !important;
            font-size: 1rem !important;
            font-weight: 700 !important;
        }

        /* Remove hover effects on mobile */
        .topic-card:hover {
            transform: none;
            box-shadow: none;
        }
    }

    /* Enhanced shadow effects */
    .shadow-xl {
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .hover\:shadow-2xl:hover {
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
    <!-- Modern Compact Header -->
    <div class="relative bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-800 overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 bg-black/10"></div>
        <div class="absolute inset-0 bg-gradient-to-r from-blue-600/90 to-indigo-800/90"></div>

        <!-- Decorative Elements -->
        <div class="absolute top-0 left-0 w-full h-full overflow-hidden">
            <div class="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
            <div class="absolute top-20 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
            <div class="absolute bottom-10 right-20 w-16 h-16 bg-white/10 rounded-full blur-lg"></div>
        </div>

        <div class="relative container mx-auto px-4 py-4 sm:py-6">
            <div class="max-w-6xl mx-auto">
                <!-- Compact Breadcrumb -->
                <nav class="flex items-center space-x-2 text-sm text-white/80 mb-4">
                    <a href="{% url 'subjects:simple_learn' %}" class="hover:text-white transition-colors duration-200 flex items-center">
                        <i class="fas fa-home mr-1"></i>
                        Learn
                    </a>
                    <i class="fas fa-chevron-right text-xs text-white/60"></i>
                    <a href="{% url 'subjects:levels' subject.id %}" class="hover:text-white transition-colors duration-200">{{ subject.name }}</a>
                    <i class="fas fa-chevron-right text-xs text-white/60"></i>
                    <span class="text-white font-medium">{{ level.name }}</span>
                </nav>

                <!-- Compact Hero Content -->
                <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
                    <div class="flex-1">
                        <!-- Subject Badge -->
                        <div class="inline-flex items-center px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-white/90 text-sm font-medium mb-3">
                            <i class="fas fa-graduation-cap mr-2"></i>
                            {{ subject.name }}
                        </div>

                        <h1 class="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-3 leading-tight">
                            {{ level.name }}
                        </h1>

                        {% if level.description %}
                            <p class="text-base sm:text-lg text-white/90 leading-relaxed mb-4 max-w-3xl">
                                {{ level.description }}
                            </p>
                        {% endif %}

                        <!-- Compact Stats -->
                        <div class="flex flex-wrap items-center gap-3 text-white/80">
                            <div class="flex items-center bg-white/10 backdrop-blur-sm rounded-lg px-2 py-1">
                                <i class="fas fa-book mr-1 text-blue-200 text-sm"></i>
                                <span class="text-xs font-medium">{{ topics|length }} Topic{{ topics|length|pluralize }}</span>
                            </div>
                            <div class="flex items-center bg-white/10 backdrop-blur-sm rounded-lg px-2 py-1">
                                <i class="fas fa-layer-group mr-1 text-green-200 text-sm"></i>
                                <span class="text-xs font-medium">Grade {{ level.level_number }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Smaller Hero Icon -->
                    <div class="flex-shrink-0 lg:block hidden">
                        <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-xl">
                            <i class="{{ subject.icon|default:'fas fa-book' }} text-white text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="lg:container lg:mx-auto lg:px-4 py-6">
        <div class="max-w-6xl mx-auto">
            <!-- Topics Grid - Mobile-first full width -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 px-4 lg:px-0">
                {% for topic_data in topics_with_progress %}
                    {% with topic=topic_data.topic %}
                    <div class="topic-card bg-white lg:rounded-xl lg:shadow-md hover:shadow-lg transition-all duration-300 lg:border lg:border-gray-200 overflow-hidden">
                        <!-- Modern Topic Header -->
                        <div class="lg:bg-gradient-to-r lg:from-blue-50 lg:to-indigo-50 lg:border-b lg:border-blue-100 p-4 sm:p-5">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center space-x-3 flex-1 min-w-0">
                                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-book-open text-lg text-blue-600"></i>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h3 class="text-gray-900 font-bold text-lg sm:text-xl leading-tight">{{ topic.title }}</h3>
                                    </div>
                                </div>

                                <!-- Progress Ring -->
                                {% if user.is_authenticated %}
                                <div class="flex-shrink-0 ml-2">
                                    <div class="relative w-12 h-12">
                                        <svg class="w-12 h-12 progress-ring" viewBox="0 0 36 36">
                                            <!-- Background circle -->
                                            <path class="progress-ring-circle"
                                                  stroke="#e5e7eb"
                                                  stroke-width="3"
                                                  fill="transparent"
                                                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                                            <!-- Progress circle -->
                                            <path class="progress-ring-circle"
                                                  stroke="{% if topic_data.is_completed %}#10b981{% else %}#3b82f6{% endif %}"
                                                  stroke-width="3"
                                                  fill="transparent"
                                                  stroke-dasharray="{{ topic_data.progress_percentage }}, 100"
                                                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                                        </svg>
                                        <!-- Progress percentage text -->
                                        <div class="absolute inset-0 flex items-center justify-center">
                                            <span class="text-xs font-bold text-gray-700">{{ topic_data.progress_percentage }}%</span>
                                        </div>
                                    </div>
                                </div>
                                {% else %}
                                <div class="flex-shrink-0 ml-2">
                                    <span class="bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded-full font-medium">
                                        #{{ topic.order }}
                                    </span>
                                </div>
                                {% endif %}
                            </div>

                            {% if topic.description %}
                                <p class="text-gray-600 text-sm leading-relaxed line-clamp-2">
                                    {{ topic.description|truncatewords:20 }}
                                </p>
                            {% endif %}
                        </div>

                        <!-- Topic Content -->
                        <div class="p-4 sm:p-5">
                            <!-- Topic Stats -->
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                <div class="flex items-center">
                                    <i class="fas fa-book mr-2 text-blue-500"></i>
                                    <span>Study Materials</span>
                                </div>
                                {% if user.is_authenticated %}
                                <div class="flex items-center">
                                    {% if topic_data.is_completed %}
                                        <i class="fas fa-check-circle mr-2 text-green-500"></i>
                                        <span>Completed</span>
                                    {% elif topic_data.progress_percentage > 0 %}
                                        <i class="fas fa-play-circle mr-2 text-blue-500"></i>
                                        <span>{{ topic_data.progress_percentage }}% Done</span>
                                    {% else %}
                                        <i class="fas fa-circle mr-2 text-gray-400"></i>
                                        <span>Not Started</span>
                                    {% endif %}
                                </div>
                                {% else %}
                                <div class="flex items-center">
                                    <i class="fas fa-user mr-2 text-purple-500"></i>
                                    <span>Sign in to track</span>
                                </div>
                                {% endif %}
                            </div>

                            <!-- Action Buttons - Side by Side -->
                            <div class="space-y-3">
                                {% if user.is_authenticated %}
                                    <!-- Both buttons side by side -->
                                    <div class="grid grid-cols-2 gap-3">
                                        <!-- Start Learning Button -->
                                        <a href="{% url 'subjects:topic_detail' subject.id level.id topic.id %}"
                                           class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white py-3 px-4 rounded-xl font-semibold transition-all flex items-center justify-center text-sm shadow-lg hover:shadow-xl">
                                            <i class="fas fa-book-open mr-2"></i>
                                            {% if topic_data.progress_percentage > 0 %}Continue{% else %}Start{% endif %}
                                        </a>

                                        <!-- Take Quiz Button -->
                                        {% if topic.questions.count > 0 %}
                                            <a href="{% url 'content:quiz' topic.id %}"
                                               class="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white py-3 px-4 rounded-xl font-semibold transition-all flex items-center justify-center text-sm shadow-lg hover:shadow-xl">
                                                <i class="fas fa-brain mr-2"></i>
                                                Take Quiz
                                            </a>
                                        {% else %}
                                            <button disabled
                                                    class="bg-gray-200 text-gray-500 py-3 px-4 rounded-xl font-semibold text-sm cursor-not-allowed flex items-center justify-center">
                                                <i class="fas fa-clock mr-2"></i>
                                                Coming Soon
                                            </button>
                                        {% endif %}
                                    </div>
                                {% else %}
                                    <button onclick="showLoginPrompt('{{ topic.title }}')"
                                            class="w-full bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white py-3 px-4 rounded-xl font-semibold transition-all flex items-center justify-center text-sm shadow-lg hover:shadow-xl">
                                        <i class="fas fa-lock mr-2"></i>
                                        Sign in to Learn
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endwith %}
                {% empty %}
                    <!-- Empty State -->
                    <div class="col-span-full text-center py-16">
                        <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-book-open text-gray-600 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">No Topics Available Yet</h3>
                        <p class="text-gray-600 mb-6">
                            Topics for {{ level.name }} will be available soon!
                        </p>
                        <a href="{% url 'subjects:simple_learn' %}" class="bg-gray-900 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-800 transition-colors inline-flex items-center">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to Learn
                        </a>
                    </div>
                {% endfor %}
            </div>

            <!-- Modern Navigation -->
            <div class="mt-8 px-4 lg:px-0">
                <div class="flex flex-col sm:flex-row items-center justify-between gap-4 bg-white/60 backdrop-blur-sm lg:rounded-2xl p-6 lg:border lg:border-white/20">
                    <a href="{% url 'subjects:levels' subject.id %}"
                       class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-xl font-medium hover:from-gray-700 hover:to-gray-800 transition-all duration-300 shadow-lg hover:shadow-xl group">
                        <i class="fas fa-arrow-left mr-3 group-hover:-translate-x-1 transition-transform duration-200"></i>
                        Back to {{ subject.name }}
                    </a>

                    <!-- Progress Indicator -->
                    <div class="flex items-center space-x-2 text-sm text-gray-600">
                        <i class="fas fa-layer-group text-blue-500"></i>
                        <span>{{ topics|length }} Topic{{ topics|length|pluralize }} in {{ level.name }}</span>
                    </div>

                    <!-- Quick Actions -->
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'subjects:quiz_list' %}"
                           class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg font-medium hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 text-sm">
                            <i class="fas fa-brain mr-2"></i>
                            Quiz Mode
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Login Prompt Modal -->
{% if not user.is_authenticated %}
<div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg max-w-md w-full p-6">
        <div class="text-center">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-graduation-cap text-blue-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-2">Ready to learn?</h3>
            <p class="text-gray-600 mb-6" id="topicName">Sign in to access this topic and track your progress!</p>

            <div class="flex gap-3 justify-center">
                <a href="{% url 'users:register' %}" class="px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors">
                    <i class="fas fa-user-plus mr-2"></i>
                    Create Free Account
                </a>
                <a href="{% url 'users:login' %}" class="px-4 py-2 border border-gray-300 text-gray-600 rounded-lg font-medium hover:bg-gray-50 transition-colors">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Sign In
                </a>
            </div>

            <button onclick="closeLoginPrompt()" class="mt-4 text-gray-500 hover:text-gray-700 text-sm">
                Maybe later
            </button>
        </div>
    </div>
</div>

<script>
function showLoginPrompt(topicName) {
    document.getElementById('topicName').textContent = `Sign in to access "${topicName}" and track your progress!`;
    document.getElementById('loginModal').classList.remove('hidden');
}

function closeLoginPrompt() {
    document.getElementById('loginModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('loginModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeLoginPrompt();
    }
});
</script>
{% endif %}



{% endblock %}
