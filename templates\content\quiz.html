{% extends 'base.html' %}
{% load static %}
{% csrf_token %}

{% block title %}{{ topic.title }} - Quiz{% endblock %}

{% block extra_head %}
<meta name="csrf-token" content="{{ csrf_token }}">
{% endblock %}

{% block extra_css %}
<style>
    /* Quiz-focused design with minimal distractions */
    .quiz-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .quiz-card {
        background: white;
        border-radius: 16px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .option-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .option-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border-color: #3B82F6 !important;
        background-color: #EFF6FF !important;
    }

    .option-card.selected {
        border-color: #10B981 !important;
        background-color: #D1FAE5 !important;
        transform: translateY(-2px);
    }

    .option-card.correct {
        border-color: #10B981 !important;
        background-color: #D1FAE5 !important;
        animation: correctPulse 0.6s ease;
    }

    .option-card.incorrect {
        border-color: #EF4444 !important;
        background-color: #FEE2E2 !important;
        animation: incorrectShake 0.6s ease;
    }

    @keyframes correctPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.02); }
        100% { transform: scale(1); }
    }

    @keyframes incorrectShake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }

    .progress-bar {
        transition: width 0.5s ease;
        background: linear-gradient(90deg, #10B981, #059669);
    }

    .quiz-timer {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }

    /* Laptop optimizations - Compact layout for 12-15 inch screens */
    @media (min-width: 641px) and (max-width: 1440px) and (min-height: 600px) and (max-height: 900px) {
        .quiz-container {
            padding: 0.5rem !important;
        }

        .quiz-card {
            max-height: 85vh;
            overflow-y: auto;
        }

        /* Compact header */
        .quiz-card .p-6:first-child {
            padding: 1rem !important;
        }

        /* Compact quiz content */
        .quiz-card .p-6:not(:first-child) {
            padding: 1rem !important;
        }

        /* Smaller question text */
        .quiz-card h3 {
            font-size: 1.125rem !important;
            margin-bottom: 1rem !important;
        }

        /* Compact option cards */
        .option-card .flex {
            padding: 0.75rem !important;
        }

        /* Compact navigation */
        .quiz-card .border-t {
            padding: 1rem !important;
        }

        /* Smaller buttons */
        .quiz-card button {
            padding: 0.5rem 1rem !important;
            font-size: 0.875rem !important;
        }

        /* Compact progress bar area */
        .bg-blue-50.rounded-lg {
            padding: 0.5rem !important;
        }

        /* Reduce spacing between elements */
        .space-y-3 > * + * {
            margin-top: 0.5rem !important;
        }

        /* Compact timer display */
        #questionTimeDisplay {
            font-size: 1rem !important;
        }

        /* Smaller icons */
        .quiz-card i {
            font-size: 0.875rem !important;
        }

        /* Compact start screen */
        #quizStartScreen {
            padding: 1.5rem !important;
        }

        #quizStartScreen .w-16.h-16,
        #quizStartScreen .w-24.h-24 {
            width: 3rem !important;
            height: 3rem !important;
            margin-bottom: 1rem !important;
        }

        #quizStartScreen h2 {
            font-size: 1.5rem !important;
            margin-bottom: 0.75rem !important;
        }

        #quizStartScreen p {
            font-size: 0.875rem !important;
            margin-bottom: 1rem !important;
        }

        /* Compact quiz info grid */
        #quizStartScreen .grid {
            gap: 0.5rem !important;
            margin-bottom: 1rem !important;
        }

        #quizStartScreen .grid > div {
            padding: 0.5rem !important;
        }

        /* Compact instructions */
        #quizStartScreen .bg-yellow-50 {
            padding: 0.75rem !important;
            margin-bottom: 1rem !important;
        }

        #quizStartScreen .bg-yellow-50 ul {
            font-size: 0.75rem !important;
        }

        /* Compact comprehension passage */
        .quiz-card .bg-blue-50 {
            padding: 1rem !important;
        }

        .quiz-card .bg-blue-50 h3 {
            font-size: 1rem !important;
            margin-bottom: 0.75rem !important;
        }

        .quiz-card .bg-blue-50 .bg-white {
            padding: 0.75rem !important;
            max-height: 200px;
            overflow-y: auto;
        }

        .quiz-card .bg-blue-50 .prose {
            font-size: 0.875rem !important;
            line-height: 1.4 !important;
        }

        /* Compact feedback modal for laptops */
        #feedbackModal .bg-white {
            max-height: 80vh !important;
        }

        /* Compact question counter */
        .quiz-card .text-xl.font-bold {
            font-size: 1.125rem !important;
        }

        /* Compact progress bar */
        .quiz-card .h-2 {
            height: 0.375rem !important;
        }
    }

    /* Mobile optimizations - Full width and better spacing */
    @media (max-width: 640px) {
        .quiz-container {
            padding: 0 !important;
        }

        .quiz-card {
            margin: 0 !important;
            border-radius: 0 !important;
            min-height: 100vh;
            width: 100% !important;
            max-width: 100% !important;
        }

        .container {
            padding-left: 0 !important;
            padding-right: 0 !important;
            max-width: 100% !important;
        }

        .option-card {
            margin-bottom: 0.75rem;
        }

        /* Ensure full width on mobile */
        .max-w-3xl {
            max-width: 100% !important;
            margin: 0 !important;
        }

        /* Better button sizing */
        .quiz-card button, .quiz-card a {
            padding: 1rem !important;
            font-size: 1rem !important;
            width: 100% !important;
        }

        /* Remove excessive padding on mobile */
        .quiz-card .p-8 {
            padding: 1rem !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="quiz-container">
    <!-- Minimal Header - Quiz Focused -->
    <div class="bg-white/10 backdrop-blur-sm border-b border-white/20">
        <div class="container mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <a href="{% url 'subjects:topic_detail' subject.id level.id topic.id %}"
                       class="text-white/80 hover:text-white transition-colors">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div>
                        <h1 class="text-white font-bold text-lg">{{ topic.title }} Quiz</h1>
                        <p class="text-white/80 text-sm">Test Your Knowledge</p>
                    </div>
                </div>
                <div class="text-white/80 text-sm">
                    Grade {{ level.level_number }} • {{ subject.name }}
                </div>
            </div>
        </div>
    </div>

    <!-- Main Quiz Container - Mobile-First Full Width -->
    <div class="w-full">
        <div class="w-full max-w-3xl mx-auto px-4 sm:px-6 py-4 sm:py-6">
            {% if not user.is_authenticated %}
            <!-- Login Prompt for Visitors -->
            <div class="quiz-card p-8 text-center mb-6">
                <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-lock text-yellow-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">Sign in to Take Quiz</h3>
                <p class="text-gray-600 mb-4">Create a free account to take quizzes and track your progress.</p>
                <a href="{% url 'users:login' %}" class="bg-purple-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-purple-700 transition-colors inline-flex items-center">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Sign In to Continue
                </a>
            </div>
            {% endif %}

            {% if quiz_info.has_questions %}
                <!-- Quiz Interface -->
                <div id="quizInterface" class="quiz-card" style="display: none;">
                    <!-- Quiz Header -->
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                <h2 class="text-xl font-bold text-gray-800">{{ quiz_info.title }}</h2>
                                <p class="text-gray-600">{{ quiz_info.description }}</p>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-gray-500">Question</div>
                                <div class="text-xl font-bold text-green-600" id="questionCounter">1 of {{ quiz_info.questions_count }}</div>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                            <div class="bg-green-600 h-2 rounded-full transition-all duration-300" id="progressBar" style="width: 0%"></div>
                        </div>

                        <!-- Question Timer -->
                        <div class="flex items-center justify-center bg-blue-50 rounded-lg p-3">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-clock text-blue-600"></i>
                                <span class="text-sm font-medium text-blue-800">Time Remaining:</span>
                                <div class="flex items-center space-x-2">
                                    <span id="questionTimeDisplay" class="text-lg font-bold text-blue-600">45s</span>
                                    <div class="w-16 bg-blue-200 rounded-full h-2">
                                        <div id="questionTimeBar" class="bg-blue-600 h-2 rounded-full transition-all duration-1000" style="width: 100%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Comprehension Passage (if applicable) -->
                    {% if quiz_info.is_comprehension and quiz_info.passage %}
                    <div class="p-6 border-b border-gray-200 bg-blue-50">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">
                                <i class="fas fa-book-open text-blue-600 mr-2"></i>
                                Reading Passage: {{ quiz_info.passage.title }}
                            </h3>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-clock mr-1"></i>
                                {{ quiz_info.passage.estimated_reading_time }} min read
                            </div>
                        </div>

                        {% if quiz_info.passage.author %}
                        <div class="text-sm text-gray-600 mb-3">
                            <i class="fas fa-user mr-1"></i>
                            By {{ quiz_info.passage.author }}
                        </div>
                        {% endif %}

                        <div class="bg-white rounded-lg p-4 border border-blue-200">
                            <div class="prose prose-sm max-w-none text-gray-800 leading-relaxed">
                                {{ quiz_info.passage.content|linebreaks }}
                            </div>
                        </div>

                        <div class="mt-4 text-center">
                            <button onclick="togglePassage()" id="togglePassageBtn"
                                    class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                <i class="fas fa-eye-slash mr-1"></i>
                                Hide Passage
                            </button>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Quiz Content -->
                    <div class="p-6">
                        {% if quiz_info.is_comprehension %}
                        <div class="mb-4 text-center">
                            <button onclick="showPassage()" id="showPassageBtn"
                                    class="text-blue-600 hover:text-blue-800 text-sm font-medium hidden">
                                <i class="fas fa-eye mr-1"></i>
                                Show Passage Again
                            </button>
                        </div>
                        {% endif %}

                        <div id="quizContent">
                            <!-- Questions will be loaded here -->
                        </div>
                    </div>

                    <!-- Quiz Navigation -->
                    <div class="p-6 border-t border-gray-200">
                        <div class="flex justify-between items-center">
                            <button id="prevBtn" onclick="previousQuestion()" class="px-6 py-3 bg-gray-200 text-gray-700 rounded-lg font-medium hover:bg-gray-300 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                <i class="fas fa-arrow-left mr-2"></i>
                                Previous
                            </button>

                            <div class="flex space-x-3">
                                <button id="nextBtn" onclick="nextQuestion()" class="px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors">
                                    Next
                                    <i class="fas fa-arrow-right ml-2"></i>
                                </button>
                                <button id="submitBtn" onclick="showDetailedResults()" class="px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors hidden">
                                    <i class="fas fa-check mr-2"></i>
                                    Submit Quiz
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quiz Start Screen -->
                <div id="quizStartScreen" class="quiz-card p-4 sm:p-8">
                    <div class="text-center">
                        <!-- Mobile-First: Smaller icon and compact layout -->
                        <div class="w-16 h-16 sm:w-24 sm:h-24 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 shadow-lg">
                            <i class="fas fa-brain text-white text-xl sm:text-3xl"></i>
                        </div>
                        <h2 class="text-xl sm:text-3xl font-bold text-gray-800 mb-3 sm:mb-4">{{ topic.title }} Quiz</h2>
                        <p class="text-base sm:text-lg text-gray-600 mb-4 sm:mb-6">Test your knowledge with {{ quiz_info.questions_count }} questions</p>

                        <!-- Mobile-First: Start Button Moved Higher -->
                        <div class="mb-4 sm:mb-6">
                            {% if user.is_authenticated %}
                                <button onclick="startQuiz()" class="w-full bg-gradient-to-r from-green-600 to-green-700 text-white px-6 sm:px-8 py-4 sm:py-4 rounded-xl font-bold text-lg sm:text-xl hover:from-green-700 hover:to-green-800 transition-all transform hover:scale-105 shadow-lg flex items-center justify-center">
                                    <i class="fas fa-play mr-2 sm:mr-3 text-lg sm:text-xl"></i>
                                    Start Quiz Now
                                </button>
                            {% else %}
                                <button onclick="showLoginPrompt()" class="w-full bg-gray-400 text-white px-6 sm:px-8 py-4 rounded-xl font-bold text-lg sm:text-xl cursor-not-allowed flex items-center justify-center">
                                    <i class="fas fa-lock mr-2 sm:mr-3 text-lg sm:text-xl"></i>
                                    Sign in to take quiz
                                </button>
                            {% endif %}
                        </div>

                        {% if quiz_info.is_shuffled %}
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-6 max-w-lg mx-auto">
                            <div class="flex items-center justify-center">
                                <i class="fas fa-random text-blue-600 mr-2"></i>
                                <span class="text-blue-800 text-sm font-medium">
                                    Questions and answers are shuffled for this attempt
                                </span>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Compact Quiz Info - After Start Button -->
                        <div class="grid grid-cols-2 gap-2 sm:gap-3 mb-4 max-w-sm mx-auto">
                            <div class="bg-green-50 rounded-lg p-2 sm:p-3 text-center">
                                <i class="fas fa-question text-green-600 text-sm sm:text-lg mb-1"></i>
                                <div class="text-xs text-gray-600">Questions</div>
                                <div class="text-sm font-semibold">{{ quiz_info.questions_count }}</div>
                            </div>
                            <div class="bg-blue-50 rounded-lg p-2 sm:p-3 text-center">
                                <i class="fas fa-clock text-blue-600 text-sm sm:text-lg mb-1"></i>
                                <div class="text-xs text-gray-600">Time Limit</div>
                                <div class="text-sm font-semibold">{{ quiz_info.time_limit }}s</div>
                            </div>
                        </div>

                        <!-- Compact Instructions -->
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4 text-left max-w-lg mx-auto">
                            <h3 class="font-semibold text-yellow-800 mb-2 text-sm">
                                <i class="fas fa-info-circle mr-2"></i>
                                Quick Tips
                            </h3>
                            <ul class="text-yellow-700 text-xs space-y-1">
                                <li>• Read each question carefully</li>
                                <li>• Answer before time runs out</li>
                                <li>• Review explanations after each answer</li>
                            </ul>
                        </div>
                    </div>
                </div>
            {% else %}
                <!-- No Quizzes Available -->
                <div class="quiz-card p-8 text-center">
                    <div class="w-20 h-20 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-info-circle text-yellow-600 text-2xl"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">Quiz Coming Soon!</h2>
                    <p class="text-gray-600 mb-6 max-w-2xl mx-auto">
                        We're preparing an interactive quiz for <strong>{{ topic.title }}</strong>
                        to help you test your knowledge and track your progress.
                    </p>
                    <a href="{% url 'subjects:topic_detail' subject.id level.id topic.id %}"
                       class="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors inline-flex items-center">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Study Material
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Feedback Modal -->
<div id="feedbackModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div id="feedbackContent">
            <!-- Feedback content will be inserted here -->
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-60 hidden z-60 flex items-center justify-center">
    <div class="bg-white rounded-xl p-8 flex flex-col items-center shadow-2xl">
        <div class="relative">
            <!-- Spinning circle -->
            <div class="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
            <!-- Inner pulsing dot -->
            <div class="absolute inset-0 flex items-center justify-center">
                <div class="w-4 h-4 bg-blue-600 rounded-full animate-pulse"></div>
            </div>
        </div>
        <p class="mt-4 text-gray-700 font-medium">Checking your answer...</p>
        <p class="text-sm text-gray-500">Please wait</p>
    </div>
</div>

<!-- Login Prompt Modal -->
{% if not user.is_authenticated %}
<div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg max-w-md w-full p-6">
        <div class="text-center">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-graduation-cap text-blue-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-2">Ready to test your knowledge?</h3>
            <p class="text-gray-600 mb-6">Sign in to take the quiz and track your progress!</p>

            <div class="flex gap-3 justify-center">
                <a href="{% url 'users:register' %}" class="btn bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 rounded-lg">
                    <i class="fas fa-user-plus mr-2"></i>
                    Create Free Account
                </a>
                <a href="{% url 'users:login' %}" class="btn btn-outline border-gray-300 text-gray-600 hover:bg-gray-50 px-4 py-2 rounded-lg">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Sign In
                </a>
            </div>

            <button onclick="closeLoginPrompt()" class="mt-4 text-gray-500 hover:text-gray-700 text-sm">
                Maybe later
            </button>
        </div>
    </div>
</div>
{% endif %}

<script>
// Quiz data and state
let currentQuestion = 0;
let answers = {};
let questions = [];
let questionTimer = null;
let explanationTimer = null;
let questionStartTime = null;
let isAnswered = false;
let quizResults = [];
const totalQuestions = parseInt('{{ quiz_info.questions_count }}');

// Quiz questions data (from Django) - Already shuffled
const quizQuestions = JSON.parse('{{ questions_json|escapejs }}');

// Quiz metadata
const quizMetadata = JSON.parse('{{ quiz_metadata_json|escapejs }}');

// User authentication status
const isUserAuthenticated = quizMetadata.isUserAuthenticated;

// Initialize questions array
questions = quizQuestions;

console.log('Quiz initialized with', questions.length, 'questions');
console.log('Questions:', questions);
console.log('Quiz metadata:', quizMetadata);
console.log('User authenticated:', isUserAuthenticated);

// Function to get CSRF token
function getCsrfToken() {
    const csrfMeta = document.querySelector('meta[name="csrf-token"]');
    if (csrfMeta) {
        return csrfMeta.getAttribute('content');
    }

    // Fallback: try to get from cookie
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrftoken') {
            return value;
        }
    }

    // Last fallback: try to get from Django's template variable
    return '{{ csrf_token }}';
}

function startQuiz() {
    document.getElementById('quizStartScreen').style.display = 'none';
    document.getElementById('quizInterface').style.display = 'block';
    questions = quizQuestions;

    if (questions.length > 0) {
        showQuestion(0);
    } else {
        alert('No questions available for this quiz.');
    }
}

// Function to generate question HTML based on question type
function generateQuestionHTML(question) {
    const questionTextHTML = `<h3 class="text-xl font-semibold text-gray-800 mb-6">${question.text}</h3>`;

    let answerHTML = '';

    switch (question.type) {
        case 'multiple_choice':
            answerHTML = `
                <div class="space-y-3" id="choicesContainer">
                    ${question.choices.map((choice, i) => `
                        <div class="option-card"
                             data-choice-id="${choice.id}"
                             data-is-correct="${choice.isCorrect}"
                             onclick="selectOption('${question.id}', '${choice.id}')">
                            <div class="flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-green-500 hover:bg-green-50 transition-all">
                                <div class="w-6 h-6 border-2 border-gray-300 rounded-full mr-4 flex items-center justify-center">
                                    <div class="w-3 h-3 bg-green-600 rounded-full hidden"></div>
                                </div>
                                <span class="text-gray-800 flex-1">${choice.text}</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
            break;

        case 'true_false':
            answerHTML = `
                <div class="space-y-3" id="choicesContainer">
                    <div class="option-card"
                         data-choice-id="true"
                         data-is-correct="${question.correctAnswer.toLowerCase() === 'true'}"
                         onclick="selectTrueFalse('${question.id}', 'true')">
                        <div class="flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-green-500 hover:bg-green-50 transition-all">
                            <div class="w-6 h-6 border-2 border-gray-300 rounded-full mr-4 flex items-center justify-center">
                                <div class="w-3 h-3 bg-green-600 rounded-full hidden"></div>
                            </div>
                            <span class="text-gray-800 flex-1">True</span>
                        </div>
                    </div>
                    <div class="option-card"
                         data-choice-id="false"
                         data-is-correct="${question.correctAnswer.toLowerCase() === 'false'}"
                         onclick="selectTrueFalse('${question.id}', 'false')">
                        <div class="flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-green-500 hover:bg-green-50 transition-all">
                            <div class="w-6 h-6 border-2 border-gray-300 rounded-full mr-4 flex items-center justify-center">
                                <div class="w-3 h-3 bg-green-600 rounded-full hidden"></div>
                            </div>
                            <span class="text-gray-800 flex-1">False</span>
                        </div>
                    </div>
                </div>
            `;
            break;

        case 'fill_blank':
        case 'short_answer':
            answerHTML = `
                <div class="space-y-4" id="answerContainer">
                    <div class="relative">
                        <input type="text"
                               id="textAnswer"
                               class="w-full p-4 border-2 border-gray-200 rounded-lg focus:border-green-500 focus:outline-none text-lg"
                               placeholder="Type your answer here..."
                               onkeypress="handleTextInputKeypress(event, '${question.id}')">
                    </div>
                    <button onclick="submitTextAnswer('${question.id}')"
                            class="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors">
                        Submit Answer
                    </button>
                </div>
            `;
            break;

        default:
            answerHTML = '<p class="text-red-600">Unsupported question type</p>';
    }

    return `
        <div class="mb-6">
            ${questionTextHTML}
            ${answerHTML}
        </div>
    `;
}

// Generate read-only HTML for already answered questions
function generateReadOnlyQuestionHTML(question, questionIndex) {
    // Find the result for this question
    const result = quizResults.find(r => r.question.id === question.id);
    if (!result) return generateQuestionHTML(question); // Fallback to regular if no result found

    const isCorrect = result.isCorrect;
    const selectedChoice = result.selectedChoice;
    const correctChoice = result.correctChoice;

    let html = `
        <div class="question-container bg-blue-50 border border-blue-200 rounded-xl p-4 md:p-6 mb-4">
            <div class="flex items-center mb-3">
                <div class="w-8 h-8 rounded-full flex items-center justify-center mr-3 ${isCorrect ? 'bg-green-100' : 'bg-red-100'}">
                    <i class="fas ${isCorrect ? 'fa-check text-green-600' : 'fa-times text-red-600'}"></i>
                </div>
                <span class="text-sm font-medium text-blue-800">Previously Answered - Review Mode</span>
            </div>

            <h2 class="text-lg md:text-xl font-bold text-gray-800 mb-4">${question.text}</h2>
    `;

    if (question.type === 'multiple_choice') {
        html += '<div class="space-y-3">';
        question.choices.forEach(choice => {
            const isSelected = selectedChoice && choice.id === selectedChoice.id;
            const isCorrectChoice = choice.isCorrect;
            let cardClass = 'border-2 rounded-lg p-3 md:p-4 cursor-default ';

            if (isCorrectChoice) {
                cardClass += 'border-green-500 bg-green-50';
            } else if (isSelected && !isCorrect) {
                cardClass += 'border-red-500 bg-red-50';
            } else {
                cardClass += 'border-gray-200 bg-gray-50';
            }

            html += `
                <div class="${cardClass}">
                    <div class="flex items-center">
                        <div class="w-5 h-5 border-2 rounded-full mr-3 flex items-center justify-center ${isCorrectChoice ? 'border-green-500' : isSelected ? 'border-red-500' : 'border-gray-400'}">
                            ${(isCorrectChoice || isSelected) ? `<div class="w-3 h-3 rounded-full ${isCorrectChoice ? 'bg-green-500' : 'bg-red-500'}"></div>` : ''}
                        </div>
                        <span class="text-gray-800">${choice.text}</span>
                        ${isSelected ? '<span class="ml-auto text-sm font-medium text-blue-600">Your Answer</span>' : ''}
                        ${isCorrectChoice ? '<span class="ml-auto text-sm font-medium text-green-600">Correct Answer</span>' : ''}
                    </div>
                </div>
            `;
        });
        html += '</div>';
    } else if (question.type === 'true_false') {
        const choices = [
            { id: 'true', text: 'True', isCorrect: question.correctAnswer.toLowerCase() === 'true' },
            { id: 'false', text: 'False', isCorrect: question.correctAnswer.toLowerCase() === 'false' }
        ];

        html += '<div class="space-y-3">';
        choices.forEach(choice => {
            const isSelected = selectedChoice && choice.id === selectedChoice.id;
            const isCorrectChoice = choice.isCorrect;
            let cardClass = 'border-2 rounded-lg p-3 md:p-4 cursor-default ';

            if (isCorrectChoice) {
                cardClass += 'border-green-500 bg-green-50';
            } else if (isSelected && !isCorrect) {
                cardClass += 'border-red-500 bg-red-50';
            } else {
                cardClass += 'border-gray-200 bg-gray-50';
            }

            html += `
                <div class="${cardClass}">
                    <div class="flex items-center">
                        <div class="w-5 h-5 border-2 rounded-full mr-3 flex items-center justify-center ${isCorrectChoice ? 'border-green-500' : isSelected ? 'border-red-500' : 'border-gray-400'}">
                            ${(isCorrectChoice || isSelected) ? `<div class="w-3 h-3 rounded-full ${isCorrectChoice ? 'bg-green-500' : 'bg-red-500'}"></div>` : ''}
                        </div>
                        <span class="text-gray-800">${choice.text}</span>
                        ${isSelected ? '<span class="ml-auto text-sm font-medium text-blue-600">Your Answer</span>' : ''}
                        ${isCorrectChoice ? '<span class="ml-auto text-sm font-medium text-green-600">Correct Answer</span>' : ''}
                    </div>
                </div>
            `;
        });
        html += '</div>';
    } else {
        // Text input questions
        html += `
            <div class="space-y-3">
                <div class="border-2 rounded-lg p-3 md:p-4 ${isCorrect ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'}">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Your Answer:</label>
                    <div class="text-gray-800 font-medium">${selectedChoice ? selectedChoice.text : 'No answer provided'}</div>
                </div>
                <div class="border-2 border-green-500 bg-green-50 rounded-lg p-3 md:p-4">
                    <label class="block text-sm font-medium text-green-700 mb-2">Correct Answer:</label>
                    <div class="text-green-800 font-medium">${correctChoice.text}</div>
                </div>
            </div>
        `;
    }

    // Add explanation if available
    if (question.explanation) {
        html += `
            <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 class="font-semibold text-blue-800 mb-2">Explanation:</h4>
                <p class="text-blue-700">${question.explanation}</p>
            </div>
        `;
    }

    html += '</div>';
    return html;
}

function showQuestion(index) {
    if (!questions || !questions[index]) {
        return;
    }

    const question = questions[index];
    const progress = ((index + 1) / totalQuestions) * 100;

    // Check if this question was already answered
    const wasAnswered = quizResults.some(result => result.question.id === question.id);

    // Reset question state only if not previously answered
    if (!wasAnswered) {
        isAnswered = false;
        questionStartTime = Date.now();
    } else {
        isAnswered = true; // Mark as answered to prevent changes
    }

    // Clear any existing timers
    if (questionTimer) clearInterval(questionTimer);
    if (explanationTimer) clearTimeout(explanationTimer);

    // Update progress bar with animation
    const progressBar = document.getElementById('progressBar');
    progressBar.style.width = progress + '%';
    progressBar.classList.add('animate');
    setTimeout(() => progressBar.classList.remove('animate'), 500);

    // Update question counter
    document.getElementById('questionCounter').textContent = `${index + 1} of ${totalQuestions}`;

    // Show question with appropriate interface based on type
    if (wasAnswered) {
        // Show read-only version with previous answer and explanation
        document.getElementById('quizContent').innerHTML = generateReadOnlyQuestionHTML(question, index);
    } else {
        // Show interactive version
        document.getElementById('quizContent').innerHTML = generateQuestionHTML(question);
        // Start question timer only for new questions
        startQuestionTimer(question);
    }

    // Update navigation buttons
    document.getElementById('prevBtn').disabled = index === 0;
    document.getElementById('nextBtn').style.display = index === totalQuestions - 1 ? 'none' : 'block';
    document.getElementById('submitBtn').style.display = index === totalQuestions - 1 ? 'block' : 'none';
}

function selectOption(questionId, choiceId) {
    if (isAnswered) {
        return; // Prevent multiple answers
    }

    // Clear previous selections
    document.querySelectorAll('.option-card').forEach(card => {
        card.classList.remove('selected');
        const border = card.querySelector('.border-2');
        const dot = card.querySelector('.w-3');
        if (border) {
            border.classList.remove('border-green-500', 'bg-green-50');
            border.classList.add('border-gray-200');
        }
        if (dot) {
            dot.classList.add('hidden');
        }
    });

    // Highlight selected option
    const selectedCard = document.querySelector(`[data-choice-id="${choiceId}"]`);

    if (selectedCard) {
        selectedCard.classList.add('selected');
        const border = selectedCard.querySelector('.border-2');
        const dot = selectedCard.querySelector('.w-3');

        if (border) {
            border.classList.remove('border-gray-200');
            border.classList.add('border-green-500', 'bg-green-50');
        }
        if (dot) {
            dot.classList.remove('hidden');
        }
    }

    // Mark as answered and process
    isAnswered = true;
    const timeTaken = Math.round((Date.now() - questionStartTime) / 1000);

    // Clear question timer
    if (questionTimer) {
        clearInterval(questionTimer);
    }

    // Store answer
    answers[questionId] = choiceId;

    // Get current question and selected choice
    const question = questions[currentQuestion];
    const selectedChoice = question.choices.find(c => c.id === choiceId);
    const correctChoice = question.choices.find(c => c.isCorrect);
    const isCorrect = selectedChoice && selectedChoice.isCorrect;

    // Store result for review
    quizResults.push({
        question: question,
        selectedChoice: selectedChoice,
        correctChoice: correctChoice,
        isCorrect: isCorrect,
        timeTaken: timeTaken
    });

    // Show loading animation and then feedback
    showLoadingAnimation();
    setTimeout(() => {
        hideLoadingAnimation();
        showAnswerFeedback(question, selectedChoice, correctChoice, isCorrect);
    }, 1200);
}

// Handler for True/False questions
function selectTrueFalse(questionId, answer) {
    if (isAnswered) return;

    // Clear previous selections
    document.querySelectorAll('.option-card').forEach(card => {
        card.classList.remove('selected');
        const border = card.querySelector('.border-2');
        const dot = card.querySelector('.w-3');
        if (border) {
            border.classList.remove('border-green-500', 'bg-green-50');
            border.classList.add('border-gray-200');
        }
        if (dot) {
            dot.classList.add('hidden');
        }
    });

    // Highlight selected option
    const selectedCard = document.querySelector(`[data-choice-id="${answer}"]`);
    if (selectedCard) {
        selectedCard.classList.add('selected');
        const border = selectedCard.querySelector('.border-2');
        const dot = selectedCard.querySelector('.w-3');

        if (border) {
            border.classList.remove('border-gray-200');
            border.classList.add('border-green-500', 'bg-green-50');
        }
        if (dot) {
            dot.classList.remove('hidden');
        }
    }

    // Mark as answered and process
    isAnswered = true;
    const timeTaken = Math.round((Date.now() - questionStartTime) / 1000);

    // Clear question timer
    if (questionTimer) {
        clearInterval(questionTimer);
    }

    // Store answer
    answers[questionId] = answer;

    // Get current question
    const question = questions[currentQuestion];
    const isCorrect = question.correctAnswer.toLowerCase() === answer.toLowerCase();

    // Create mock choice objects for consistency with multiple choice
    const selectedChoice = { id: answer, text: answer.charAt(0).toUpperCase() + answer.slice(1) };
    const correctChoice = {
        id: question.correctAnswer.toLowerCase(),
        text: question.correctAnswer.charAt(0).toUpperCase() + question.correctAnswer.slice(1)
    };

    // Store result for review
    quizResults.push({
        question: question,
        selectedChoice: selectedChoice,
        correctChoice: correctChoice,
        isCorrect: isCorrect,
        timeTaken: timeTaken
    });

    // Show loading animation and then feedback
    showLoadingAnimation();
    setTimeout(() => {
        hideLoadingAnimation();
        showAnswerFeedback(question, selectedChoice, correctChoice, isCorrect);
    }, 1200);
}

// Handler for text input questions (fill_blank, short_answer)
async function submitTextAnswer(questionId) {
    if (isAnswered) return;

    const textInput = document.getElementById('textAnswer');
    const userAnswer = textInput.value.trim();

    if (!userAnswer) {
        alert('Please enter an answer before submitting.');
        return;
    }

    // Show loading state
    const submitButton = document.querySelector('button[onclick*="submitTextAnswer"]');
    const originalButtonText = submitButton.textContent;
    submitButton.textContent = 'Checking...';
    submitButton.disabled = true;

    try {
        // Validate answer using server-side intelligent matching
        const response = await fetch('/content/api/validate-answer/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                question_id: questionId,
                user_answer: userAnswer
            })
        });

        const validationResult = await response.json();

        if (!validationResult.success) {
            throw new Error(validationResult.error || 'Validation failed');
        }

        // Mark as answered and process
        isAnswered = true;
        const timeTaken = Math.round((Date.now() - questionStartTime) / 1000);

        // Clear question timer
        if (questionTimer) {
            clearInterval(questionTimer);
        }

        // Store answer
        answers[questionId] = userAnswer;

        // Get current question
        const question = questions[currentQuestion];

        // Use validation results
        const isCorrect = validationResult.is_correct;
        const matchType = validationResult.match_type;
        const matchedAnswer = validationResult.matched_answer;

        // Create choice objects with enhanced information
        const selectedChoice = {
            id: 'user_input',
            text: userAnswer,
            matchType: matchType,
            feedback: validationResult.feedback
        };

        const correctChoice = {
            id: 'correct_answer',
            text: matchedAnswer || question.correctAnswer,
            acceptableAnswers: validationResult.acceptable_answers
        };

        // Store result for review
        quizResults.push({
            question: question,
            selectedChoice: selectedChoice,
            correctChoice: correctChoice,
            isCorrect: isCorrect,
            timeTaken: timeTaken,
            validationDetails: validationResult
        });

        // Disable input and button
        textInput.disabled = true;
        textInput.classList.add('bg-gray-100');

        // Show loading animation and then feedback
        showLoadingAnimation();
        setTimeout(() => {
            hideLoadingAnimation();
            showAnswerFeedback(question, selectedChoice, correctChoice, isCorrect, validationResult);
        }, 1200);

    } catch (error) {
        console.error('Error validating answer:', error);

        // Fallback to simple validation
        isAnswered = true;
        const timeTaken = Math.round((Date.now() - questionStartTime) / 1000);

        if (questionTimer) {
            clearInterval(questionTimer);
        }

        answers[questionId] = userAnswer;
        const question = questions[currentQuestion];

        // Simple case-insensitive comparison as fallback
        const isCorrect = question.correctAnswer.toLowerCase().trim() === userAnswer.toLowerCase();

        const selectedChoice = { id: 'user_input', text: userAnswer };
        const correctChoice = { id: 'correct_answer', text: question.correctAnswer };

        quizResults.push({
            question: question,
            selectedChoice: selectedChoice,
            correctChoice: correctChoice,
            isCorrect: isCorrect,
            timeTaken: timeTaken
        });

        textInput.disabled = true;
        textInput.classList.add('bg-gray-100');

        showLoadingAnimation();
        setTimeout(() => {
            hideLoadingAnimation();
            showAnswerFeedback(question, selectedChoice, correctChoice, isCorrect);
        }, 1200);
    } finally {
        // Reset button state
        submitButton.textContent = originalButtonText;
    }
}

// Handler for Enter key in text input
function handleTextInputKeypress(event, questionId) {
    if (event.key === 'Enter') {
        submitTextAnswer(questionId);
    }
}

function startQuestionTimer(question) {
    let timeLeft = question.timeLimit;
    const timeDisplay = document.getElementById('questionTimeDisplay');
    const timeBar = document.getElementById('questionTimeBar');

    // Initialize timer display
    if (timeDisplay) {
        timeDisplay.textContent = timeLeft + 's';
        timeDisplay.classList.remove('text-red-600');
        timeDisplay.classList.add('text-blue-600');
    }

    if (timeBar) {
        timeBar.style.width = '100%';
        timeBar.classList.remove('bg-red-600');
        timeBar.classList.add('bg-blue-600');
    }

    questionTimer = setInterval(() => {
        timeLeft--;

        if (timeDisplay) {
            timeDisplay.textContent = timeLeft + 's';
        }

        // Update progress bar
        if (timeBar) {
            const percentage = (timeLeft / question.timeLimit) * 100;
            timeBar.style.width = percentage + '%';
        }

        // Change color as time runs out
        if (timeLeft <= 10) {
            if (timeBar) {
                timeBar.classList.remove('bg-blue-600');
                timeBar.classList.add('bg-red-600');
            }
            if (timeDisplay) {
                timeDisplay.classList.remove('text-blue-600');
                timeDisplay.classList.add('text-red-600');
            }
        }

        // Time's up
        if (timeLeft <= 0) {
            clearInterval(questionTimer);
            if (!isAnswered) {
                // Auto-submit with no answer
                isAnswered = true;
                const currentQ = questions[currentQuestion];
                let correctChoice = null;

                // Find correct choice based on question type
                if (currentQ.type === 'multiple_choice') {
                    correctChoice = currentQ.choices.find(c => c.isCorrect);
                } else if (currentQ.type === 'true_false') {
                    correctChoice = {
                        id: currentQ.correctAnswer.toLowerCase(),
                        text: currentQ.correctAnswer
                    };
                } else {
                    correctChoice = {
                        id: 'correct_answer',
                        text: currentQ.correctAnswer
                    };
                }

                quizResults.push({
                    question: currentQ,
                    selectedChoice: null,
                    correctChoice: correctChoice,
                    isCorrect: false,
                    timeTaken: currentQ.timeLimit
                });

                showAnswerFeedback(currentQ, null, correctChoice, false);
            }
        }
    }, 1000);
}

function showAnswerFeedback(question, selectedChoice, correctChoice, isCorrect, validationResult = null) {
    // Disable all interactive elements
    document.querySelectorAll('.option-card').forEach(card => {
        card.style.pointerEvents = 'none';
    });

    // Disable text input if present
    const textInput = document.getElementById('textAnswer');
    if (textInput) {
        textInput.disabled = true;
        textInput.classList.add('bg-gray-100');
    }

    // Highlight correct and incorrect answers for choice-based questions
    if (question.type === 'multiple_choice' || question.type === 'true_false') {
        document.querySelectorAll('.option-card').forEach(card => {
            const choiceId = card.getAttribute('data-choice-id');
            const choiceIsCorrect = card.getAttribute('data-is-correct') === 'true';
            const border = card.querySelector('.border-2');
            const dot = card.querySelector('.w-3');

            if (choiceIsCorrect) {
                // Highlight correct answer in green
                border.classList.remove('border-gray-200', 'border-green-500');
                border.classList.add('border-green-600', 'bg-green-100');
                dot.classList.remove('hidden');
                dot.classList.remove('bg-green-600');
                dot.classList.add('bg-green-600');
            } else if (selectedChoice && choiceId === selectedChoice.id && !isCorrect) {
                // Highlight wrong selected answer in red
                border.classList.remove('border-gray-200', 'border-green-500');
                border.classList.add('border-red-600', 'bg-red-100');
                dot.classList.remove('hidden');
                dot.classList.remove('bg-green-600');
                dot.classList.add('bg-red-600');
            }
        });
    } else if (question.type === 'fill_blank' || question.type === 'short_answer') {
        // For text input questions, show visual feedback on the input field
        if (textInput) {
            if (isCorrect) {
                textInput.classList.add('border-green-600', 'bg-green-50');
                textInput.classList.remove('border-gray-200');
            } else {
                textInput.classList.add('border-red-600', 'bg-red-50');
                textInput.classList.remove('border-gray-200');
            }
        }
    }

    // Show feedback modal instead of inline explanation
    showFeedbackModal(question, selectedChoice, correctChoice, isCorrect, validationResult);
}

// New modal-based feedback function
function showFeedbackModal(question, selectedChoice, correctChoice, isCorrect, validationResult = null) {
    // Prepare feedback message for text answers
    let feedbackMessage = '';
    let acceptableAnswersDisplay = '';

    if (validationResult && (question.type === 'fill_blank' || question.type === 'short_answer')) {
        feedbackMessage = validationResult.feedback;
        if (validationResult.acceptable_answers && validationResult.acceptable_answers.length > 1) {
            acceptableAnswersDisplay = `
                <div class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p class="text-sm font-medium text-blue-800 mb-1">All acceptable answers:</p>
                    <p class="text-blue-700">${validationResult.acceptable_answers.join(', ')}</p>
                </div>
            `;
        }
    }

    // Create mobile-optimized modal content
    const modalContent = `
        <div class="p-4 md:p-6 max-h-[90vh] overflow-y-auto">
            <!-- Header -->
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 md:w-12 md:h-12 rounded-full flex items-center justify-center mr-3 ${isCorrect ? 'bg-green-100' : 'bg-red-100'}">
                    <i class="fas ${isCorrect ? 'fa-check-circle text-green-600' : 'fa-times-circle text-red-600'} text-lg md:text-2xl"></i>
                </div>
                <div class="flex-1">
                    <h3 class="text-lg md:text-xl font-bold ${isCorrect ? 'text-green-800' : 'text-red-800'}">
                        ${isCorrect ? 'Correct!' : 'Incorrect'}
                    </h3>
                    <p class="text-sm text-gray-600">Question ${currentQuestion + 1} of ${totalQuestions}</p>
                </div>
            </div>

            <!-- Question -->
            <div class="mb-3 p-3 md:p-4 bg-gray-50 rounded-lg">
                <p class="text-sm md:text-base font-medium text-gray-800">${question.text}</p>
            </div>

            <!-- Answer Details -->
            <div class="space-y-2 md:space-y-3 mb-4">
                ${selectedChoice ? `
                    <div class="p-2 md:p-3 rounded-lg ${isCorrect ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}">
                        <p class="text-xs md:text-sm font-medium ${isCorrect ? 'text-green-800' : 'text-red-800'}">Your answer:</p>
                        <p class="text-sm md:text-base text-gray-700">${selectedChoice.text}</p>
                    </div>
                ` : `
                    <div class="p-2 md:p-3 rounded-lg bg-orange-50 border border-orange-200">
                        <p class="text-xs md:text-sm font-medium text-orange-800">No answer selected</p>
                        <p class="text-sm text-orange-700">Time expired or no selection made</p>
                    </div>
                `}

                <div class="p-2 md:p-3 rounded-lg bg-green-50 border border-green-200">
                    <p class="text-xs md:text-sm font-medium text-green-800">Correct answer:</p>
                    <p class="text-sm md:text-base text-gray-700">${correctChoice.text}</p>
                </div>
            </div>

            <!-- Feedback Message -->
            ${feedbackMessage ? `
                <div class="mb-3 p-2 md:p-3 ${isCorrect ? 'bg-green-100' : 'bg-red-100'} rounded-lg">
                    <p class="text-xs md:text-sm ${isCorrect ? 'text-green-800' : 'text-red-800'}">${feedbackMessage}</p>
                </div>
            ` : ''}

            <!-- Acceptable Answers -->
            ${acceptableAnswersDisplay}

            <!-- Explanation -->
            ${question.explanation ? `
                <div class="mb-4 p-3 md:p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 class="text-sm md:text-base font-semibold text-blue-800 mb-2 flex items-center">
                        <i class="fas fa-lightbulb mr-2"></i>
                        Explanation
                    </h4>
                    <p class="text-sm md:text-base text-blue-700 leading-relaxed">${question.explanation}</p>
                </div>
            ` : ''}

            <!-- Action Button -->
            <div class="flex justify-center pt-2">
                <button onclick="closeFeedbackModal()" class="w-full md:w-auto bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 md:px-8 py-3 rounded-xl font-semibold hover:from-blue-700 hover:to-blue-800 transition-all transform hover:scale-105 shadow-lg flex items-center justify-center text-sm md:text-base">
                    ${currentQuestion < totalQuestions - 1 ?
                        '<i class="fas fa-arrow-right mr-2"></i>Continue to Next Question' :
                        '<i class="fas fa-flag-checkered mr-2"></i>View Results'
                    }
                </button>
            </div>
        </div>
    `;

    // Show modal
    document.getElementById('feedbackContent').innerHTML = modalContent;
    document.getElementById('feedbackModal').classList.remove('hidden');
}

// Loading animation functions
function showLoadingAnimation() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.classList.remove('hidden');
    }
}

function hideLoadingAnimation() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.classList.add('hidden');
    }
}

// Navigation functions
function nextQuestion() {
    if (currentQuestion < totalQuestions - 1) {
        currentQuestion++;
        showQuestion(currentQuestion);
    }
}

function previousQuestion() {
    if (currentQuestion > 0) {
        currentQuestion--;
        showQuestion(currentQuestion);
    }
}

// Close feedback modal and proceed to next question or results
function closeFeedbackModal() {
    document.getElementById('feedbackModal').classList.add('hidden');

    // Move to next question or show results
    if (currentQuestion < totalQuestions - 1) {
        currentQuestion++;
        showQuestion(currentQuestion);
    } else {
        showDetailedResults();
    }
}

// Navigation functions
document.getElementById('prevBtn').addEventListener('click', () => {
    if (currentQuestion > 0) {
        // Allow navigation back but show read-only view if question was already answered
        currentQuestion--;
        showQuestion(currentQuestion);
    }
});

document.getElementById('nextBtn').addEventListener('click', () => {
    if (currentQuestion < totalQuestions - 1) {
        // Allow navigation forward but show read-only view if question was already answered
        currentQuestion++;
        showQuestion(currentQuestion);
    }
});

document.getElementById('submitBtn').addEventListener('click', () => {
    if (Object.keys(answers).length < totalQuestions) {
        alert('Please answer all questions before submitting.');
        return;
    }

    showDetailedResults();
});

function showDetailedResults() {
    console.log('showDetailedResults called');
    console.log('quizResults length:', quizResults.length);
    console.log('quizResults:', quizResults);
    console.log('answers:', answers);
    console.log('totalQuestions:', totalQuestions);
    console.log('questions array:', questions);

    // If quizResults is empty but we have answers, try to reconstruct the results
    if (quizResults.length === 0 && Object.keys(answers).length > 0) {
        console.log('Reconstructing quiz results from answers...');

        Object.keys(answers).forEach((questionId, index) => {
            const question = questions.find(q => q.id === questionId);
            if (question) {
                const selectedChoiceId = answers[questionId];
                const selectedChoice = question.choices ? question.choices.find(c => c.id === selectedChoiceId) : null;
                const correctChoice = question.choices ? question.choices.find(c => c.isCorrect) : null;
                const isCorrect = selectedChoice ? selectedChoice.isCorrect : false;

                quizResults.push({
                    question: question,
                    selectedChoice: selectedChoice || { text: selectedChoiceId, id: selectedChoiceId },
                    correctChoice: correctChoice || { text: question.correctAnswer, id: 'correct' },
                    isCorrect: isCorrect,
                    timeTaken: 30 // Default time
                });
            }
        });

        console.log('Reconstructed quiz results:', quizResults);
    }

    // Ensure we have results for all questions
    if (quizResults.length === 0) {
        console.error('No quiz results found and could not reconstruct!');
        alert('Error: No quiz results found. Please try again.');
        return;
    }

    // Hide the quiz interface immediately to prevent showing last question
    document.getElementById('quizInterface').style.display = 'none';

    // Show a loading screen while processing results
    showLoadingAnimation();

    // Calculate comprehensive results
    const score = quizResults.filter(r => r.isCorrect).length;
    const percentage = Math.round((score / totalQuestions) * 100);
    const passed = percentage >= 70;
    const totalTime = quizResults.reduce((sum, r) => sum + r.timeTaken, 0);
    const avgTimePerQuestion = Math.round(totalTime / totalQuestions);
    const timeExpiredQuestions = quizResults.filter(r => !r.selectedChoice).length;

    // Prepare results data for the results page
    const resultsData = {
        score: score,
        total: totalQuestions,
        percentage: percentage,
        passed: passed,
        totalTime: totalTime,
        avgTimePerQuestion: avgTimePerQuestion,
        timeExpiredQuestions: timeExpiredQuestions,
        quizResults: quizResults,
        quizMetadata: quizMetadata,
        topicTitle: '{{ topic.title }}',
        subjectName: '{{ subject.name }}',
        levelNumber: '{{ level.level_number }}'
    };

    // Store results in sessionStorage for the results page
    console.log('Storing quiz results:', resultsData);
    sessionStorage.setItem('quizResults', JSON.stringify(resultsData));

    // Submit quiz results to backend for progress tracking
    setTimeout(() => {
        if (isUserAuthenticated) {
            submitQuizToBackend(score, percentage, totalTime, resultsData);
        } else {
            // For non-authenticated users, redirect immediately
            hideLoadingAnimation();
            redirectToResults();
        }
    }, 1000); // Small delay to show loading
}

// Function to redirect to results page (optimized for free tier)
function redirectToResults(quizId = null) {
    // Calculate current results if not already done
    const score = quizResults.filter(r => r.isCorrect).length;
    const percentage = Math.round((score / totalQuestions) * 100);
    const totalTime = quizResults.reduce((sum, r) => sum + (r.timeTaken || 0), 0);

    // Store results in sessionStorage using consistent format
    const resultsData = {
        score: score,
        total: totalQuestions,
        percentage: percentage,
        totalTime: totalTime,
        quizResults: quizResults,
        topicTitle: '{{ topic.title }}',
        subjectName: '{{ subject.name }}',
        levelNumber: '{{ level.level_number }}',
        topicId: '{{ topic.id }}',
        timestamp: Date.now()
    };

    console.log('Storing quiz results (redirect):', resultsData);
    sessionStorage.setItem('quizResults', JSON.stringify(resultsData));

    // Use the quiz ID if provided, otherwise fallback to topic ID (for non-authenticated users)
    if (quizId) {
        console.log('Redirecting to quiz result page with quiz ID:', quizId);
        window.location.href = `/content/quiz/${quizId}/result/`;
    } else {
        console.log('No quiz ID provided, redirecting to dashboard');
        window.location.href = '/dashboard/';
    }
}






function retakeQuiz() {
    // Add a timestamp to force new shuffling
    const currentUrl = window.location.href;
    const separator = currentUrl.includes('?') ? '&' : '?';
    const newUrl = currentUrl + separator + 'retake=' + Date.now();
    window.location.href = newUrl;
}

// Login prompt functions (only for non-authenticated users)
function showLoginPrompt() {
    if (!isUserAuthenticated) {
        document.getElementById('loginModal').classList.remove('hidden');
    }
}

function closeLoginPrompt() {
    if (!isUserAuthenticated) {
        document.getElementById('loginModal').classList.add('hidden');
    }
}

// Close modal when clicking outside (only for non-authenticated users)
if (!isUserAuthenticated) {
    document.getElementById('loginModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeLoginPrompt();
        }
    });
}

// Passage toggle functions for comprehension quizzes
function togglePassage() {
    const passageSection = document.querySelector('.bg-blue-50');
    const toggleBtn = document.getElementById('togglePassageBtn');
    const showBtn = document.getElementById('showPassageBtn');

    if (passageSection.style.display === 'none') {
        // Show passage
        passageSection.style.display = 'block';
        toggleBtn.innerHTML = '<i class="fas fa-eye-slash mr-1"></i>Hide Passage';
        showBtn.classList.add('hidden');
    } else {
        // Hide passage
        passageSection.style.display = 'none';
        showBtn.classList.remove('hidden');
    }
}

function showPassage() {
    const passageSection = document.querySelector('.bg-blue-50');
    const toggleBtn = document.getElementById('togglePassageBtn');
    const showBtn = document.getElementById('showPassageBtn');

    passageSection.style.display = 'block';
    toggleBtn.innerHTML = '<i class="fas fa-eye-slash mr-1"></i>Hide Passage';
    showBtn.classList.add('hidden');
}

// Initialize quiz interface as hidden
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('quizInterface').style.display = 'none';
});

// Submit quiz results to backend for progress tracking (only for authenticated users)
function submitQuizToBackend(score, percentage, totalTime, resultsData) {
    if (!isUserAuthenticated) {
        redirectToResults();
        return;
    }

    // Prepare answers data
    const answersData = {};
    quizResults.forEach((result, index) => {
        if (result.question && result.question.id) {
            answersData[result.question.id] = result.selectedChoice ?
                (result.selectedChoice.text || result.selectedChoice.id) : '';
        }
    });

    const submissionData = {
        topic_id: '{{ topic.id }}',
        answers: answersData,
        time_taken: totalTime
    };

    fetch('/content/api/submit-quiz/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify(submissionData)
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingAnimation();
        if (data.success) {
            // Show success message if needed
            if (data.topic_completed) {
                showTopicCompletionMessage();
            }
            // Redirect to results page after successful submission using quiz_id
            redirectToResults(data.quiz_id);
        } else {
            console.error('Quiz submission failed:', data.error);
            // Still redirect to results page even if submission failed
            redirectToResults();
        }
    })
    .catch(error => {
        hideLoadingAnimation();
        console.error('Error submitting quiz:', error);
        // Still redirect to results page even if there was an error
        redirectToResults();
    });
}

function showTopicCompletionMessage() {
    if (!isUserAuthenticated) return;

    // Add a success notification for topic completion
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-trophy mr-2"></i>
            <span>Topic completed! Progress updated.</span>
        </div>
    `;
    document.body.appendChild(notification);

    // Remove notification after 5 seconds
    setTimeout(() => {
        notification.remove();
    }, 5000);
}
</script>

<style>
.option-card.selected .border-2 {
    border-color: #10b981 !important;
    background-color: #ecfdf5 !important;
}

/* Quiz Results Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.5s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.4s ease-out;
}

.animate-pulse-gentle {
    animation: pulse 2s infinite;
}

/* Gradient text effect */
.gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Custom scrollbar for results */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Progress circle animation */
.progress-circle {
    transition: stroke-dashoffset 2s ease-in-out;
}

/* Hover effects for cards */
.result-card:hover {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
}

/* Achievement badge animation */
.achievement-badge {
    animation: scaleIn 0.5s ease-out;
    animation-fill-mode: both;
}

.achievement-badge:nth-child(1) { animation-delay: 0.1s; }
.achievement-badge:nth-child(2) { animation-delay: 0.2s; }
.achievement-badge:nth-child(3) { animation-delay: 0.3s; }
.achievement-badge:nth-child(4) { animation-delay: 0.4s; }

/* Question review hover effects */
.question-review-card:hover {
    transform: translateX(5px);
    transition: transform 0.3s ease;
}

/* Button hover effects */
.action-button {
    transition: all 0.3s ease;
}

.action-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Progress bar improvements */
.progress-bar {
    transition: width 0.3s ease-in-out;
}

.progress-bar.animate {
    animation: progressPulse 0.5s ease-in-out;
}

@keyframes progressPulse {
    0% { transform: scaleY(1); }
    50% { transform: scaleY(1.1); }
    100% { transform: scaleY(1); }
}

/* Loading animation improvements */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Modal improvements for mobile */
@media (max-width: 640px) {
    #feedbackModal .bg-white {
        margin: 1rem;
        max-height: calc(100vh - 2rem);
    }

    #loadingOverlay .bg-white {
        margin: 1rem;
        padding: 1.5rem;
    }
}

/* Mobile responsiveness improvements */
@media (max-width: 768px) {
    .quiz-results-container {
        padding: 1rem;
    }

    .score-circle {
        width: 120px;
        height: 120px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}
</style>

{% endblock %}
