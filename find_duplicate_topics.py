#!/usr/bin/env python3
"""
Find and fix duplicate topics in the CSV file
"""
import csv
from collections import defaultdict

def find_duplicate_topics():
    """Find duplicate topics that would cause database constraint violations"""
    
    # Track topics by subject and class level
    topics_by_subject = defaultdict(lambda: defaultdict(list))
    all_rows = []
    
    print("🔍 ANALYZING TOPICS FOR DUPLICATES...")
    print("=" * 60)
    
    # Read all data
    with open('Grade_9_questions_CLEAN.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        header = next(reader)
        all_rows.append(header)
        
        for row_num, row in enumerate(reader, 2):  # Start from 2 (after header)
            if len(row) >= 3:
                subject = row[0].strip()
                class_level = row[1].strip()
                topic = row[2].strip()
                
                # Track topics
                key = f"{subject}_{class_level}"
                topics_by_subject[key][topic].append((row_num, row))
                all_rows.append(row)
    
    # Find duplicates and similar topics
    duplicates_found = []
    
    for subject_class, topics in topics_by_subject.items():
        subject, class_level = subject_class.split('_', 1)
        
        print(f"\n📚 {subject} - {class_level}:")
        
        # Check for exact duplicates
        exact_duplicates = []
        similar_topics = []
        
        topic_names = list(topics.keys())
        
        for topic_name in topic_names:
            rows = topics[topic_name]
            if len(rows) > 1:
                exact_duplicates.append((topic_name, len(rows)))
            
            # Check for similar topics (case-insensitive, minor variations)
            for other_topic in topic_names:
                if (topic_name != other_topic and 
                    topic_name.lower().strip() == other_topic.lower().strip()):
                    similar_topics.append((topic_name, other_topic))
        
        # Report findings
        if exact_duplicates:
            for topic, count in exact_duplicates:
                print(f"   ❌ EXACT DUPLICATE: '{topic}' ({count} entries)")
                duplicates_found.append((subject_class, topic, count))
        
        if similar_topics:
            for topic1, topic2 in similar_topics:
                print(f"   ⚠️  SIMILAR: '{topic1}' vs '{topic2}'")
        
        # Show all unique topics
        unique_topics = sorted(topics.keys())
        print(f"   📋 Topics ({len(unique_topics)}):")
        for i, topic in enumerate(unique_topics, 1):
            question_count = len(topics[topic])
            print(f"      {i:2d}. {topic} ({question_count} questions)")
    
    return duplicates_found, topics_by_subject

def fix_duplicate_topics():
    """Fix duplicate topics by merging them"""
    
    duplicates, topics_data = find_duplicate_topics()
    
    if not duplicates:
        print(f"\n✅ No exact duplicate topics found!")
        return
    
    print(f"\n🔧 FIXING DUPLICATE TOPICS...")
    print("=" * 60)
    
    # Read the original file
    fixed_rows = []
    
    with open('Grade_9_questions_CLEAN.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        header = next(reader)
        fixed_rows.append(header)
        
        for row in reader:
            if len(row) >= 3:
                subject = row[0].strip()
                class_level = row[1].strip()
                topic = row[2].strip()
                
                # Check for known problematic cases and standardize
                fixed_topic = standardize_topic_name(topic)
                row[2] = fixed_topic
                
            fixed_rows.append(row)
    
    # Write fixed file
    with open('Grade_9_questions_NO_DUPLICATES.csv', 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        for row in fixed_rows:
            writer.writerow(row)
    
    print(f"✅ Fixed file saved as: Grade_9_questions_NO_DUPLICATES.csv")
    
    # Verify the fix
    verify_fix()

def standardize_topic_name(topic):
    """Standardize topic names to avoid duplicates"""
    
    topic = topic.strip()
    
    # Known duplicates to fix
    standardizations = {
        'Life cycle of a mosquito': 'Life Cycle of a Mosquito',
        'Life Cycle of a Mosquito': 'Life Cycle of a Mosquito',
        'Simultaneous Equation': 'Simultaneous Equations',
        'Simultaneous Equations': 'Simultaneous Equations',
        'Spreadseite Application': 'Spreadsheet Application',
        'Spreadsheet Application': 'Spreadsheet Application',
    }
    
    return standardizations.get(topic, topic)

def verify_fix():
    """Verify that duplicates are fixed"""
    
    topics_count = defaultdict(lambda: defaultdict(int))
    
    with open('Grade_9_questions_NO_DUPLICATES.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # Skip header
        
        for row in reader:
            if len(row) >= 3:
                subject = row[0].strip()
                class_level = row[1].strip()
                topic = row[2].strip()
                
                key = f"{subject}_{class_level}"
                topics_count[key][topic] += 1
    
    print(f"\n🔍 VERIFICATION:")
    duplicates_remaining = 0
    
    for subject_class, topics in topics_count.items():
        subject, class_level = subject_class.split('_', 1)
        unique_topics = len(topics)
        
        print(f"   📚 {subject} - {class_level}: {unique_topics} unique topics")
        
        # Check for any remaining duplicates
        for topic, count in topics.items():
            if count > 1:
                print(f"      ❌ Still duplicate: '{topic}' ({count} entries)")
                duplicates_remaining += 1
    
    if duplicates_remaining == 0:
        print(f"   ✅ All duplicates resolved!")
    else:
        print(f"   ⚠️  {duplicates_remaining} duplicates still need attention")

if __name__ == "__main__":
    fix_duplicate_topics()
