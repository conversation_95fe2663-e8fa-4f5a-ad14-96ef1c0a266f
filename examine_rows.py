#!/usr/bin/env python3
"""
Examine specific problematic rows to understand the pattern
"""
import csv

def examine_specific_rows():
    """Look at specific rows to understand the shifting pattern"""
    
    target_rows = [8, 10, 11, 12]  # Some problematic rows from analysis
    
    print("🔍 EXAMINING SPECIFIC PROBLEMATIC ROWS:")
    print("=" * 70)
    
    with open('Grade_9_questions.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        
        for row_num, row in enumerate(reader, 1):
            if row_num in target_rows:
                print(f"\n📍 ROW {row_num} ({len(row)} fields):")
                print("-" * 50)
                
                expected_fields = [
                    'subject_name', 'class_level_name', 'topic_title', 'question_text', 
                    'question_type', 'correct_answer', 'explanation', 'difficulty', 
                    'points', 'time_limit', 'choice_a', 'choice_b', 'choice_c', 'choice_d'
                ]
                
                for i, field in enumerate(row[:20]):  # Show first 20 fields
                    expected_name = expected_fields[i] if i < len(expected_fields) else f"extra_{i+1}"
                    print(f"  {i+1:2d}. {expected_name:15s}: '{field}'")
                
                # Try to identify where the shift occurs
                print(f"\n  🔍 Analysis:")
                if len(row) > 4:
                    print(f"     Question type (field 5): '{row[4]}'")
                if len(row) > 5:
                    print(f"     Correct answer (field 6): '{row[5]}'")
                if len(row) > 6:
                    print(f"     Explanation (field 7): '{row[6]}'")
                if len(row) > 7:
                    print(f"     Expected difficulty (field 8): '{row[7]}'")
                if len(row) > 8:
                    print(f"     Expected points (field 9): '{row[8]}'")
                if len(row) > 9:
                    print(f"     Expected time_limit (field 10): '{row[9]}'")

if __name__ == "__main__":
    examine_specific_rows()
