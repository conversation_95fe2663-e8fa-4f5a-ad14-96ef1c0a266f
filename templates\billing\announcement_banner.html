{% if show_billing_announcement and billing_announcement %}
<div class="alert alert-{{ billing_announcement.type }} mb-4" id="billingAnnouncement">
    <div class="flex-1">
        {% if billing_announcement.title %}
        <h3 class="font-bold">{{ billing_announcement.title }}</h3>
        {% endif %}
        <div class="text-sm">{{ billing_announcement.message|linebreaks }}</div>
    </div>
    <div class="flex-none">
        <button class="btn btn-sm btn-ghost" onclick="dismissAnnouncement()">
            <i class="fas fa-times"></i>
        </button>
    </div>
</div>

<script>
function dismissAnnouncement() {
    const announcement = document.getElementById('billingAnnouncement');
    if (announcement) {
        announcement.style.display = 'none';
        // Store dismissal in localStorage to remember user's choice
        localStorage.setItem('billing_announcement_dismissed', 'true');
    }
}

// Check if user previously dismissed the announcement
document.addEventListener('DOMContentLoaded', function() {
    if (localStorage.getItem('billing_announcement_dismissed') === 'true') {
        const announcement = document.getElementById('billingAnnouncement');
        if (announcement) {
            announcement.style.display = 'none';
        }
    }
});
</script>
{% endif %}

<!-- Subscription Status Warning for Users -->
{% if user.is_authenticated and billing_enabled and user_subscription_expires_soon %}
<div class="alert alert-warning mb-4">
    <div class="flex-1">
        <h3 class="font-bold">Subscription Expiring Soon</h3>
        <div class="text-sm">
            Your subscription expires in {{ user_subscription.days_until_expiry }} day{{ user_subscription.days_until_expiry|pluralize }}.
            <a href="{% url 'billing:dashboard' %}" class="link link-primary">Manage your subscription</a>
        </div>
    </div>
</div>
{% endif %}

{% if user.is_authenticated and billing_enabled and not user_subscription_active %}
<div class="alert alert-error mb-4">
    <div class="flex-1">
        <h3 class="font-bold">Subscription Required</h3>
        <div class="text-sm">
            Your subscription is not active. Some features may be limited.
            <a href="{% url 'billing:plans' %}" class="link link-primary">View plans</a>
        </div>
    </div>
</div>
{% endif %}
