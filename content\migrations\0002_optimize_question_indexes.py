# Generated migration for optimizing question queries

from django.db import migrations, models


def create_indexes_if_tables_exist(apps, schema_editor):
    """Create indexes only if tables exist - PostgreSQL compatible"""
    db_alias = schema_editor.connection.alias

    with schema_editor.connection.cursor() as cursor:
        # PostgreSQL-compatible table existence check and index creation
        try:
            # Check if content_question table exists (using correct table name)
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = 'questions'
                );
            """)
            if cursor.fetchone()[0]:
                # Create optimized indexes for questions table
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_question_active_topic ON questions(is_active, topic_id);")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_question_type_difficulty ON questions(question_type, difficulty);")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_question_created_at ON questions(created_at);")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_question_topic_active_type ON questions(topic_id, is_active, question_type);")

                # NEW: Performance indexes for duplicate detection and pagination
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_question_text_search ON questions USING gin(to_tsvector('english', question_text));")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_question_pagination ON questions(topic_id, created_at DESC, id);")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_question_filtering ON questions(is_active, topic_id, question_type, created_at);")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_question_duplicate_detection ON questions(is_active, length(question_text), created_at);")

                print("✅ Created optimized question indexes")
        except Exception as e:
            print(f"⚠️ Could not create question indexes: {e}")

        try:
            # Check if topics table exists (using correct table name)
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = 'topics'
                );
            """)
            if cursor.fetchone()[0]:
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_topic_class_level ON topics(class_level_id);")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_topic_active_class ON topics(is_active, class_level_id);")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_topic_title_search ON topics USING gin(to_tsvector('english', title));")
                print("✅ Created optimized topic indexes")
        except Exception as e:
            print(f"⚠️ Could not create topic indexes: {e}")

        try:
            # Check if class_levels table exists (using correct table name)
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = 'class_levels'
                );
            """)
            if cursor.fetchone()[0]:
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_class_level_subject ON class_levels(subject_id, level_number);")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_class_level_active ON class_levels(is_active, level_number);")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_class_level_lookup ON class_levels(subject_id, is_active, level_number);")
                print("✅ Created optimized class level indexes")
        except Exception as e:
            print(f"⚠️ Could not create class level indexes: {e}")

        try:
            # Check if answer_choices table exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = 'answer_choices'
                );
            """)
            if cursor.fetchone()[0]:
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_answer_choice_question ON answer_choices(question_id);")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_answer_choice_correct ON answer_choices(question_id, is_correct);")
                print("✅ Created answer choice indexes")
        except Exception as e:
            print(f"⚠️ Could not create answer choice indexes: {e}")


def remove_indexes(apps, schema_editor):
    """Remove indexes - PostgreSQL compatible"""
    with schema_editor.connection.cursor() as cursor:
        try:
            # Remove original indexes
            cursor.execute("DROP INDEX IF EXISTS idx_question_active_topic;")
            cursor.execute("DROP INDEX IF EXISTS idx_question_type_difficulty;")
            cursor.execute("DROP INDEX IF EXISTS idx_question_created_at;")
            cursor.execute("DROP INDEX IF EXISTS idx_question_topic_active_type;")
            cursor.execute("DROP INDEX IF EXISTS idx_topic_class_level;")
            cursor.execute("DROP INDEX IF EXISTS idx_class_level_subject;")

            # Remove new performance indexes
            cursor.execute("DROP INDEX IF EXISTS idx_question_text_search;")
            cursor.execute("DROP INDEX IF EXISTS idx_question_pagination;")
            cursor.execute("DROP INDEX IF EXISTS idx_question_filtering;")
            cursor.execute("DROP INDEX IF EXISTS idx_question_duplicate_detection;")
            cursor.execute("DROP INDEX IF EXISTS idx_topic_active_class;")
            cursor.execute("DROP INDEX IF EXISTS idx_topic_title_search;")
            cursor.execute("DROP INDEX IF EXISTS idx_class_level_active;")
            cursor.execute("DROP INDEX IF EXISTS idx_class_level_lookup;")
            cursor.execute("DROP INDEX IF EXISTS idx_answer_choice_question;")
            cursor.execute("DROP INDEX IF EXISTS idx_answer_choice_correct;")

            print("✅ Removed all indexes")
        except Exception as e:
            print(f"⚠️ Could not remove indexes: {e}")


class Migration(migrations.Migration):

    dependencies = [
        ('content', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(create_indexes_if_tables_exist, remove_indexes),
    ]
