subject_name,class_level_name,topic_title,question_text,question_type,correct_answer,explanation,difficulty,points,time_limit,choice_a,choice_b,choice_c,choice_d
ICT,Basic 6,Introduction to Computers,What is a computer?,multiple_choice,b,A computer is an electronic machine that processes information and data.,easy,10,60,A toy,An electronic machine that processes information,A book,A television
ICT,Basic 6,Introduction to Computers,What can computers help us do?,multiple_choice,d,Computers help us learn work communicate and solve problems.,medium,10,120,Only play games,Only watch movies,Only listen to music,Learn work communicate and solve problems
ICT,Basic 6,Introduction to Computers,Where can we find computers?,multiple_choice,c,Computers are found in schools offices homes hospitals and many other places.,medium,10,150,Only in schools,Only in offices,Schools offices homes hospitals and many places,Only at home
ICT,Basic 6,Introduction to Computers,What makes a computer work?,multiple_choice,a,Electricity makes computers work by powering all the electronic parts.,easy,10,60,Electricity,Water,Air,Fire
ICT,Basic 6,Introduction to Computers,Are computers intelligent by themselves?,multiple_choice,b,Computers are not intelligent by themselves; they need instructions from people.,medium,10,120,Yes they are very smart,No they need instructions from people,Sometimes,Only big computers are smart
ICT,Basic 6,Introduction to Computers,What is the difference between a computer and a calculator?,multiple_choice,c,Computers can do many different tasks while calculators mainly do math.,medium,10,150,No difference,Calculators are bigger,Computers can do many tasks; calculators mainly do math,Computers are smaller
ICT,Basic 6,Introduction to Computers,Why are computers useful?,multiple_choice,d,Computers are useful because they work fast store lots of information and help with many tasks.,medium,10,180,Only because they are expensive,Only because they look nice,Only because they make noise,They work fast store information and help with tasks
ICT,Basic 6,Introduction to Computers,Can computers think like humans?,multiple_choice,b,Computers cannot think like humans; they only follow instructions.,medium,10,120,Yes exactly like humans,No they only follow instructions,Sometimes they can,Only smart computers can
ICT,Basic 6,Introduction to Computers,What do we call the instructions we give to computers?,multiple_choice,c,Instructions given to computers are called programs or software.,hard,15,120,Commands,Orders,Programs or software,Rules
ICT,Basic 6,Introduction to Computers,How have computers changed our lives?,multiple_choice,a,Computers have made communication faster learning easier and work more efficient.,hard,15,180,Made communication faster learning easier work more efficient,Made life more difficult,Made everything slower,Only changed games
ICT,Basic 6,Parts of a Computer,What are the main parts of a computer?,multiple_choice,d,The main parts are monitor keyboard mouse and system unit.,medium,10,150,Only the screen,Only the keyboard,Only the mouse,Monitor keyboard mouse and system unit
ICT,Basic 6,Parts of a Computer,What do we use to see what the computer is doing?,multiple_choice,a,We use the monitor (screen) to see what the computer is doing.,easy,10,60,Monitor or screen,Keyboard,Mouse,Speaker
ICT,Basic 6,Parts of a Computer,What do we use to type letters and numbers?,multiple_choice,b,We use the keyboard to type letters numbers and symbols.,easy,10,60,Monitor,Keyboard,Mouse,Speaker
ICT,Basic 6,Parts of a Computer,What do we use to point and click on the screen?,multiple_choice,c,We use the mouse to point and click on things on the screen.,easy,10,60,Monitor,Keyboard,Mouse,Speaker
ICT,Basic 6,Parts of a Computer,What do we call the box that contains the computer's brain?,multiple_choice,d,The system unit (CPU box) contains the computer's brain and main parts.,medium,10,120,Monitor,Keyboard,Mouse,System unit or CPU box
ICT,Basic 6,Parts of a Computer,What do we use to hear sounds from the computer?,multiple_choice,a,We use speakers or headphones to hear sounds from the computer.,easy,10,75,Speakers or headphones,Monitor,Keyboard,Mouse
ICT,Basic 6,Parts of a Computer,What do we use to print documents on paper?,multiple_choice,b,We use a printer to print documents and pictures on paper.,easy,10,75,Scanner,Printer,Monitor,Keyboard
ICT,Basic 6,Parts of a Computer,What device helps us put pictures into the computer?,multiple_choice,c,A scanner helps us put pictures and documents into the computer.,medium,10,90,Printer,Monitor,Scanner,Speaker
ICT,Basic 6,Parts of a Computer,What do we call the moving arrow on the screen?,multiple_choice,d,The moving arrow on the screen is called a cursor or pointer.,medium,10,75,Icon,Window,Button,Cursor or pointer
ICT,Basic 6,Parts of a Computer,Which part of the computer is like its brain?,multiple_choice,a,The CPU (Central Processing Unit) is like the computer's brain.,hard,15,120,CPU (Central Processing Unit),Monitor,Keyboard,Mouse
ICT,Basic 6,Using a Computer Safely,Why should we wash our hands before using a computer?,multiple_choice,c,We should wash hands to keep the computer clean and prevent spreading germs.,medium,10,120,To make hands smell good,To make hands soft,To keep computer clean and prevent germs,To make typing faster
ICT,Basic 6,Using a Computer Safely,How should we sit when using a computer?,multiple_choice,b,We should sit up straight with feet on the floor for good posture.,medium,10,120,Lying down,Sitting up straight with feet on floor,Standing up,Sitting sideways
ICT,Basic 6,Using a Computer Safely,What should we do with food and drinks near computers?,multiple_choice,d,We should keep food and drinks away from computers to prevent damage.,medium,10,120,Eat while typing,Drink while using mouse,Put drinks on keyboard,Keep food and drinks away from computer
ICT,Basic 6,Using a Computer Safely,How should we turn off a computer?,multiple_choice,c,We should use the proper shutdown procedure not just switch off power.,medium,10,150,Pull out the power cord,Press any button,Use proper shutdown procedure,Hit the computer
ICT,Basic 6,Using a Computer Safely,What should we do if we spill water on a computer?,multiple_choice,a,We should turn off the computer immediately and tell an adult.,hard,15,150,Turn off computer and tell an adult,Keep using it,Pour more water,Ignore it
ICT,Basic 6,Using a Computer Safely,How long should we use a computer without taking a break?,multiple_choice,b,We should take breaks every 20-30 minutes to rest our eyes and body.,hard,15,150,All day without stopping,Take breaks every 20-30 minutes,Only 5 minutes total,Never take breaks
ICT,Basic 6,Using a Computer Safely,What should we do to protect our eyes while using a computer?,multiple_choice,d,We should sit at proper distance blink often and take breaks.,hard,15,180,Sit very close to screen,Never blink,Stare without moving,Sit at proper distance blink often take breaks
ICT,Basic 6,Using a Computer Safely,Why shouldn't we touch the inside of a computer?,multiple_choice,c,The inside has electrical parts that can be dangerous and we might break something.,hard,15,150,It's dirty,It's too cold,Has electrical parts that are dangerous,It's too hot
ICT,Basic 6,Using a Computer Safely,What should we do before moving a computer?,multiple_choice,a,We should turn it off properly and unplug all cables safely.,hard,15,150,Turn off properly and unplug cables safely,Just pick it up,Move it while it's running,Shake it first
ICT,Basic 6,Using a Computer Safely,Who should we ask for help if something goes wrong with the computer?,multiple_choice,b,We should ask a teacher parent or adult who knows about computers.,medium,10,120,Another child,Teacher parent or knowledgeable adult,No one,Try to fix it ourselves
ICT,Basic 6,Basic Computer Operations,How do we start or turn on a computer?,multiple_choice,a,We press the power button to start the computer.,easy,10,60,Press the power button,Hit the screen,Shake the mouse,Pull a cord
ICT,Basic 6,Basic Computer Operations,What appears on the screen when we start the computer?,multiple_choice,c,The desktop appears with icons and a background picture.,medium,10,120,Nothing,Only black screen,Desktop with icons and background,Only text
ICT,Basic 6,Basic Computer Operations,What are the small pictures on the desktop called?,multiple_choice,b,The small pictures on the desktop are called icons.,medium,10,75,Buttons,Icons,Windows,Screens
ICT,Basic 6,Basic Computer Operations,How do we open a program or application?,multiple_choice,d,We double-click on an icon or use the start menu.,medium,10,120,Shake the computer,Hit the screen,Press any key,Double-click icon or use start menu
ICT,Basic 6,Basic Computer Operations,What do we call the rectangular areas that open on the screen?,multiple_choice,c,The rectangular areas that open are called windows.,medium,10,90,Doors,Boxes,Windows,Screens
ICT,Basic 6,Basic Computer Operations,How do we close a window or program?,multiple_choice,a,We click the X button in the corner or use the close option.,medium,10,120,Click the X button or use close option,Turn off computer,Hit the screen,Press any key
ICT,Basic 6,Basic Computer Operations,What is the taskbar?,multiple_choice,b,The taskbar is the bar at the bottom showing open programs and start button.,hard,15,150,Top of the screen,Bar at bottom showing programs and start button,Side of the screen,The desktop background
ICT,Basic 6,Basic Computer Operations,How do we make a window bigger or smaller?,multiple_choice,d,We can drag the corners or use maximize/minimize buttons.,hard,15,150,Cannot change size,Hit the screen,Shake the mouse,Drag corners or use maximize/minimize buttons
ICT,Basic 6,Basic Computer Operations,What is the start button used for?,multiple_choice,c,The start button is used to open programs and access computer features.,medium,10,150,Turn off computer,Change colors,Open programs and access features,Make noise
ICT,Basic 6,Basic Computer Operations,How do we save our work on a computer?,multiple_choice,a,We use the save option in the file menu or press Ctrl+S.,hard,15,120,Use save option or press Ctrl+S,Turn off computer,Close the program,Do nothing
ICT,Basic 6,Introduction to Internet,What is the Internet?,multiple_choice,b,The Internet is a worldwide network that connects computers together.,medium,10,150,A single computer,Worldwide network connecting computers,A program,A game
ICT,Basic 6,Introduction to Internet,What can we do on the Internet?,multiple_choice,d,We can search for information send messages play games and learn.,medium,10,180,Only send messages,Only play games,Only search,Search for information send messages play games learn
ICT,Basic 6,Introduction to Internet,What do we need to use the Internet?,multiple_choice,c,We need a computer and an Internet connection.,medium,10,120,Only a computer,Only a phone,Computer and Internet connection,Only a tablet
ICT,Basic 6,Introduction to Internet,What is a website?,multiple_choice,a,A website is a collection of pages with information on the Internet.,medium,10,150,Collection of pages with information on Internet,A computer program,A type of computer,A game
ICT,Basic 6,Introduction to Internet,What is a web browser?,multiple_choice,b,A web browser is a program used to view websites on the Internet.,hard,15,150,A website,Program used to view websites,A computer,A game
ICT,Basic 6,Introduction to Internet,What is a search engine?,multiple_choice,c,A search engine helps us find information on the Internet.,medium,10,120,A type of car,A computer part,Tool that helps find information on Internet,A website game
ICT,Basic 6,Introduction to Internet,What should we be careful about on the Internet?,multiple_choice,d,We should be careful about sharing personal information and talking to strangers.,hard,15,180,Nothing,Only viruses,Only slow connections,Sharing personal information and talking to strangers
ICT,Basic 6,Introduction to Internet,Who should we ask before using the Internet?,multiple_choice,a,We should ask parents teachers or adults for permission and guidance.,medium,10,150,Parents teachers or adults,Other children,No one,Strangers online
ICT,Basic 6,Introduction to Internet,What is email?,multiple_choice,b,Email is electronic mail used to send messages over the Internet.,medium,10,120,A website,Electronic mail for sending messages,A computer program,A game
ICT,Basic 6,Introduction to Internet,Why is the Internet useful for learning?,multiple_choice,c,The Internet has lots of information educational games and learning resources.,medium,10,180,Only has games,Only has pictures,Has information educational games and learning resources,Only has videos
ICT,Basic 6,Computer Applications,What is a word processor?,multiple_choice,a,A word processor is a program used to write and edit text documents.,medium,10,150,Program for writing and editing text,Program for drawing,Program for math,Program for games
ICT,Basic 6,Computer Applications,What can we do with a word processor?,multiple_choice,d,We can type letters stories reports and format text.,medium,10,150,Only type letters,Only type stories,Only type reports,Type letters stories reports and format text
ICT,Basic 6,Computer Applications,What is a paint program?,multiple_choice,b,A paint program is software used to create and edit pictures.,medium,10,120,Program for writing,Program for creating and editing pictures,Program for math,Program for music
ICT,Basic 6,Computer Applications,What can we create with a paint program?,multiple_choice,c,We can create drawings paintings and edit photos.,medium,10,150,Only drawings,Only paintings,Drawings paintings and edit photos,Only text
ICT,Basic 6,Computer Applications,What is educational software?,multiple_choice,d,Educational software is programs designed to help us learn different subjects.,medium,10,180,Only games,Only entertainment,Only work programs,Programs designed to help learn subjects
ICT,Basic 6,Computer Applications,What subjects can educational software help us learn?,multiple_choice,a,Educational software can help with math reading science and many other subjects.,medium,10,180,Math reading science and other subjects,Only math,Only reading,Only science
ICT,Basic 6,Computer Applications,What is a presentation program?,multiple_choice,b,A presentation program helps create slideshows with text and pictures.,hard,15,150,Program for writing letters,Program for creating slideshows,Program for drawing,Program for games
ICT,Basic 6,Computer Applications,What can we include in a presentation?,multiple_choice,c,We can include text pictures sounds and animations.,hard,15,150,Only text,Only pictures,Text pictures sounds and animations,Only sounds
ICT,Basic 6,Computer Applications,What is multimedia?,multiple_choice,d,Multimedia combines text pictures sound and video together.,hard,15,150,Only text,Only pictures,Only sound,Combination of text pictures sound and video
ICT,Basic 6,Computer Applications,How do computer applications help us in school?,multiple_choice,a,They help us learn create projects practice skills and research information.,hard,15,180,Help learn create projects practice skills research,Only for entertainment,Only for games,Only for watching videos
ICT,Basic 6,Digital Citizenship,What does it mean to be a good digital citizen?,multiple_choice,b,Being respectful safe and responsible when using technology.,hard,15,180,Using technology all the time,Being respectful safe and responsible with technology,Only playing games,Only using social media
ICT,Basic 6,Digital Citizenship,What information should we never share online?,multiple_choice,d,We should never share personal information like full name address or phone number.,hard,15,180,Our favorite color,Our favorite food,Our favorite game,Personal information like name address phone number
ICT,Basic 6,Digital Citizenship,What should we do if we see something inappropriate online?,multiple_choice,a,We should tell a trusted adult immediately.,medium,10,120,Tell a trusted adult immediately,Ignore it,Share it with friends,Keep looking at it
ICT,Basic 6,Digital Citizenship,How should we treat others online?,multiple_choice,c,We should treat others with kindness and respect just like in real life.,medium,10,150,We can be mean,We can ignore everyone,With kindness and respect like real life,We can say anything
ICT,Basic 6,Digital Citizenship,What is cyberbullying?,multiple_choice,b,Cyberbullying is being mean to others using technology.,hard,15,120,Being nice online,Being mean to others using technology,Playing games online,Sharing pictures
ICT,Basic 6,Digital Citizenship,What should we do if someone is mean to us online?,multiple_choice,d,We should not respond tell a trusted adult and block the person.,hard,15,180,Be mean back,Ignore it completely,Tell all our friends,Not respond tell adult and block person
ICT,Basic 6,Digital Citizenship,Why is it important to have strong passwords?,multiple_choice,c,Strong passwords protect our accounts and personal information.,hard,15,150,They look cool,They are easy to remember,They protect accounts and personal information,They are required by law
ICT,Basic 6,Digital Citizenship,What makes a password strong?,multiple_choice,a,A strong password is long has different types of characters and is hard to guess.,hard,15,180,Long with different characters hard to guess,Short and simple,Only numbers,Only letters
ICT,Basic 6,Digital Citizenship,Should we believe everything we read online?,multiple_choice,b,No we should check if information is true and comes from reliable sources.,hard,15,180,Yes everything online is true,No we should check if it's true and reliable,Only believe pictures,Only believe videos
ICT,Basic 6,Digital Citizenship,What is digital footprint?,multiple_choice,c,Digital footprint is the trail of information we leave when using technology.,hard,15,180,Footprints on computer,Pictures of feet,Trail of information we leave using technology,Walking with computers
