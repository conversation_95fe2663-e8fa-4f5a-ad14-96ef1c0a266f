#!/usr/bin/env python3
"""
Step-by-step fix for Grade_9_questions.csv
"""
import csv
import re

def fix_csv_step_by_step():
    """Fix CSV by reconstructing broken explanation fields and realigning data"""
    
    expected_header = [
        'subject_name', 'class_level_name', 'topic_title', 'question_text', 
        'question_type', 'correct_answer', 'explanation', 'difficulty', 
        'points', 'time_limit', 'choice_a', 'choice_b', 'choice_c', 'choice_d'
    ]
    
    valid_question_types = ['multiple_choice', 'fill_blank', 'true_false', 'short_answer']
    valid_difficulties = ['easy', 'medium', 'hard']
    
    fixed_rows = []
    fixed_count = 0
    good_count = 0
    error_count = 0
    
    print("🔧 STEP-BY-STEP CSV FIXING...")
    print("=" * 60)
    
    with open('Grade_9_questions.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        
        for row_num, row in enumerate(reader, 1):
            # Remove empty trailing fields
            while row and row[-1] == '':
                row.pop()
            
            # Header row
            if row_num == 1:
                fixed_rows.append(expected_header)
                print(f"✅ Fixed header row")
                continue
            
            # Skip empty rows
            if not row or len(row) < 6:
                continue
            
            try:
                # Check if row is already correctly formatted
                if len(row) >= 14:
                    question_type = row[4].strip().lower()
                    difficulty = row[7].strip().lower() if len(row) > 7 else ""
                    points = row[8] if len(row) > 8 else ""
                    time_limit = row[9] if len(row) > 9 else ""
                    
                    # If row is already good, keep it as is
                    if (question_type in valid_question_types and 
                        difficulty in valid_difficulties and 
                        points.isdigit() and 
                        time_limit.isdigit()):
                        fixed_rows.append(row[:14])
                        good_count += 1
                        continue
                
                # Row needs fixing - reconstruct it
                fixed_row = fix_single_row(row, row_num)
                if fixed_row:
                    fixed_rows.append(fixed_row)
                    fixed_count += 1
                else:
                    error_count += 1
                    print(f"❌ Could not fix row {row_num}")
                    
            except Exception as e:
                error_count += 1
                print(f"❌ Error processing row {row_num}: {e}")
    
    # Write fixed file
    with open('Grade_9_questions_FIXED.csv', 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        for row in fixed_rows:
            writer.writerow(row)
    
    print(f"\n📊 RESULTS:")
    print(f"   ✅ Good rows (kept as-is): {good_count}")
    print(f"   🔧 Fixed rows: {fixed_count}")
    print(f"   ❌ Error rows: {error_count}")
    print(f"   📁 Total output rows: {len(fixed_rows)}")
    print(f"\n💾 Fixed file saved as: Grade_9_questions_FIXED.csv")

def fix_single_row(row, row_num):
    """Fix a single problematic row by reconstructing the explanation field"""
    
    valid_difficulties = ['easy', 'medium', 'hard']
    
    try:
        # Basic structure should be intact for first 6 fields
        if len(row) < 6:
            return None
            
        subject_name = row[0]
        class_level_name = row[1] 
        topic_title = row[2]
        question_text = row[3]
        question_type = row[4]
        correct_answer = row[5]
        
        # Find where the explanation ends by looking for difficulty, points, time_limit pattern
        explanation_parts = []
        remaining_fields = row[6:]
        
        # Look for the pattern: [text...] difficulty points time_limit [choices...]
        # We need to find where difficulty (easy/medium/hard) appears
        difficulty_index = -1
        
        for i, field in enumerate(remaining_fields):
            field_clean = field.strip().lower()
            if field_clean in valid_difficulties:
                # Check if next two fields could be points and time_limit
                if (i + 2 < len(remaining_fields) and 
                    remaining_fields[i + 1].strip().isdigit() and 
                    remaining_fields[i + 2].strip().isdigit()):
                    difficulty_index = i
                    break
        
        if difficulty_index == -1:
            # Fallback: look for numeric patterns that could be points/time
            for i in range(len(remaining_fields) - 2):
                if (remaining_fields[i].strip().isdigit() and 
                    remaining_fields[i + 1].strip().isdigit()):
                    # This might be points and time_limit
                    # Check if previous field could be difficulty
                    if i > 0:
                        prev_field = remaining_fields[i - 1].strip().lower()
                        if prev_field in valid_difficulties:
                            difficulty_index = i - 1
                            break
        
        if difficulty_index == -1:
            return None  # Cannot determine structure
        
        # Reconstruct explanation from parts before difficulty
        explanation_parts = remaining_fields[:difficulty_index]
        explanation = ', '.join(explanation_parts).strip()
        
        # Get the rest of the fields
        difficulty = remaining_fields[difficulty_index].strip()
        points = remaining_fields[difficulty_index + 1].strip()
        time_limit = remaining_fields[difficulty_index + 2].strip()
        
        # Get choices (should be next 4 fields)
        choices_start = difficulty_index + 3
        choices = remaining_fields[choices_start:choices_start + 4]
        
        # Ensure we have 4 choices
        while len(choices) < 4:
            choices.append("")
        
        # Construct final row
        fixed_row = [
            subject_name, class_level_name, topic_title, question_text,
            question_type, correct_answer, explanation, difficulty,
            points, time_limit, choices[0], choices[1], choices[2], choices[3]
        ]
        
        return fixed_row
        
    except Exception as e:
        print(f"   Error fixing row {row_num}: {e}")
        return None

if __name__ == "__main__":
    fix_csv_step_by_step()
