#!/usr/bin/env python3
"""
Check what Grade 9 topics already exist in the database
"""
import os
import sys
import django

# Setup Django
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pentora_platform.settings')
django.setup()

from subjects.models import Subject, ClassLevel, Topic

def check_existing_topics():
    """Check what Grade 9 topics already exist"""
    
    print("🔍 CHECKING EXISTING GRADE 9 TOPICS IN DATABASE...")
    print("=" * 60)
    
    try:
        # Get all Grade 9 class levels
        grade_9_levels = ClassLevel.objects.filter(name__icontains='Grade 9').select_related('subject')
        
        if not grade_9_levels.exists():
            print("✅ No Grade 9 class levels found in database")
            print("   Your CSV should upload without conflicts!")
            return {}
        
        existing_topics = {}
        
        for class_level in grade_9_levels:
            subject_name = class_level.subject.name
            print(f"\n📚 {subject_name} - {class_level.name}:")
            
            topics = Topic.objects.filter(class_level=class_level).order_by('order')
            
            if topics.exists():
                existing_topics[subject_name] = []
                for topic in topics:
                    existing_topics[subject_name].append(topic.title)
                    print(f"   {topic.order:2d}. {topic.title}")
            else:
                print("   (No topics found)")
        
        return existing_topics
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        print("   This might be normal if database is not accessible")
        return {}

def create_conflict_free_csv(existing_topics):
    """Create a CSV that avoids conflicts with existing topics"""
    
    if not existing_topics:
        print(f"\n✅ No existing topics found - your current CSV should work!")
        return
    
    print(f"\n🔧 CREATING CONFLICT-FREE CSV...")
    print("=" * 50)
    
    import csv
    
    # Read current CSV
    new_rows = []
    conflicts = []
    
    with open('Grade_9_questions_PERFECT.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        header = next(reader)
        new_rows.append(header)
        
        for row in reader:
            if len(row) >= 3:
                subject = row[0].strip()
                topic = row[2].strip()
                
                # Check if this topic already exists
                if subject in existing_topics and topic in existing_topics[subject]:
                    conflicts.append((subject, topic))
                else:
                    new_rows.append(row)
    
    if conflicts:
        print(f"⚠️  Found {len(set(conflicts))} conflicting topics:")
        for subject, topic in set(conflicts):
            print(f"   • {subject}: {topic}")
        
        # Write conflict-free CSV
        with open('Grade_9_questions_NO_CONFLICTS.csv', 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f)
            for row in new_rows:
                writer.writerow(row)
        
        print(f"\n✅ Created conflict-free CSV: Grade_9_questions_NO_CONFLICTS.csv")
        print(f"   Original questions: {len(new_rows) - 1}")
        print(f"   Removed conflicts: {len(conflicts)}")
    else:
        print(f"✅ No conflicts found - your current CSV should work!")

if __name__ == "__main__":
    existing = check_existing_topics()
    create_conflict_free_csv(existing)
