#!/usr/bin/env python3
"""
Create a clean CSV with only valid rows for immediate upload
"""
import csv

def create_clean_csv():
    """Extract only valid rows into a clean CSV file"""
    
    valid_question_types = ['multiple_choice', 'fill_blank', 'true_false', 'short_answer']
    valid_difficulties = ['easy', 'medium', 'hard']
    
    clean_rows = []
    invalid_rows = []
    
    print("🧹 CREATING CLEAN CSV FILE...")
    print("=" * 60)
    
    with open('Grade_9_questions_FIXED.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        
        for row_num, row in enumerate(reader, 1):
            if row_num == 1:  # Header
                clean_rows.append(row)
                continue
                
            if len(row) < 14:
                invalid_rows.append((row_num, "Insufficient fields"))
                continue
            
            # Validate key fields
            question_type = row[4].strip().lower()
            difficulty = row[7].strip().lower()
            points = row[8].strip()
            time_limit = row[9].strip()
            
            # Check if row is valid
            is_valid = (
                question_type in valid_question_types and
                difficulty in valid_difficulties and
                points.isdigit() and
                time_limit.isdigit() and
                row[0].strip() and  # subject_name not empty
                row[1].strip() and  # class_level_name not empty
                row[3].strip()      # question_text not empty
            )
            
            if is_valid:
                clean_rows.append(row)
            else:
                invalid_rows.append((row_num, f"Invalid: type='{question_type}', diff='{difficulty}', pts='{points}', time='{time_limit}'"))
    
    # Write clean file
    with open('Grade_9_questions_CLEAN.csv', 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        for row in clean_rows:
            writer.writerow(row)
    
    # Write invalid rows for review
    with open('Grade_9_questions_INVALID.csv', 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['original_row_number', 'error_reason', 'original_data'])
        for row_num, error in invalid_rows:
            writer.writerow([row_num, error, "See original file"])
    
    print(f"📊 RESULTS:")
    print(f"   ✅ Clean rows (ready for upload): {len(clean_rows) - 1}")  # -1 for header
    print(f"   ❌ Invalid rows (need review): {len(invalid_rows)}")
    print(f"\n📁 FILES CREATED:")
    print(f"   ✅ Grade_9_questions_CLEAN.csv - Ready for upload!")
    print(f"   📋 Grade_9_questions_INVALID.csv - For review")
    
    return len(clean_rows) - 1, len(invalid_rows)

if __name__ == "__main__":
    clean_count, invalid_count = create_clean_csv()
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"1. ✅ Upload 'Grade_9_questions_CLEAN.csv' ({clean_count} questions)")
    print(f"2. 📋 Review 'Grade_9_questions_INVALID.csv' ({invalid_count} rows)")
    print(f"3. 🔧 Fix invalid rows manually if needed")
    print(f"\n🚀 You can now upload the CLEAN file - it should work without errors!")
