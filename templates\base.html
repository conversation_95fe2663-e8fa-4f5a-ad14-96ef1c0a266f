<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- SEO Meta Tags -->
    <title>{% block title %}Pentora (Mentora) - Ghana's Leading Online Education Platform | BECE & WASSCE Preparation | Free Quality Education{% endblock %}</title>
    <meta name="description" content="{% block description %}Pentora (formerly Mentora) - Ghana's premier online education platform providing free quality education, BECE preparation, WASSCE preparation, interactive learning, and comprehensive study materials for JHS and SHS students. Access online studies, educational quizzes, and expert-curated content for academic excellence.{% endblock %}">
    <meta name="keywords" content="{% block keywords %}education, online learning, e-learning, digital education, Mentora, Pentora, educational platform, online studies, BECE preparation, WASSCE preparation, Ghana education, primary education, secondary education, JHS, SHS, mathematics, english, science, social studies, online quizzes, study notes, exam preparation, free education, quality education, academic excellence, student learning, educational resources, study materials, interactive learning, personalized education, learning platform, educational technology, EdTech Ghana, online tutoring, curriculum-based learning, grade-specific content, comprehensive education, academic support, learning management, distance learning, virtual classroom, educational app, study platform, learning app, education Ghana, school online, free online learning, grades 1-12, English learning, Mathematics, ICT, Life Skills, online school{% endblock %}">
    <meta name="author" content="Pentora Learning Platform">
    <meta name="robots" content="index, follow">
    <meta name="language" content="English">
    <meta name="revisit-after" content="7 days">
    <meta name="csrf-token" content="{{ csrf_token }}">

    <!-- Open Graph Meta Tags for Social Media -->
    <meta property="og:title" content="{% block og_title %}Pentora - Free Online Learning Platform for All Grades{% endblock %}">
    <meta property="og:description" content="{% block og_description %}Access free educational content, interactive quizzes, and comprehensive study materials for Grades 1-12. Learn English, Math, Science, and more!{% endblock %}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{% block og_url %}{{ request.build_absolute_uri }}{% endblock %}">
    <meta property="og:image" content="{% block og_image %}{{ request.scheme }}://{{ request.get_host }}{% load static %}{% static 'images/Pentora-og-image.jpg' %}{% endblock %}">
    <meta property="og:site_name" content="Pentora Learning Platform">
    <meta property="og:locale" content="en_US">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{% block twitter_title %}Pentora - Free Online Learning Platform{% endblock %}">
    <meta name="twitter:description" content="{% block twitter_description %}Free educational platform with quizzes and study materials for all grades{% endblock %}">
    <meta name="twitter:image" content="{% block twitter_image %}{{ request.scheme }}://{{ request.get_host }}{% load static %}{% static 'images/Pentora-twitter-card.jpg' %}{% endblock %}">

    <!-- Canonical URL -->
    <link rel="canonical" href="{% block canonical_url %}{{ request.build_absolute_uri }}{% endblock %}">

    <!-- Favicon and App Icons -->
    {% load site_images %}
    {% get_site_favicon as custom_favicon %}
    {% get_site_logo as custom_logo %}
    {% if custom_favicon %}
        <link rel="icon" type="image/x-icon" href="{{ custom_favicon }}">
        <link rel="apple-touch-icon" sizes="180x180" href="{{ custom_favicon }}">
        <link rel="icon" type="image/png" sizes="32x32" href="{{ custom_favicon }}">
        <link rel="icon" type="image/png" sizes="16x16" href="{{ custom_favicon }}">
    {% elif custom_logo %}
        <!-- Use uploaded logo as favicon fallback -->
        <link rel="icon" type="image/x-icon" href="{{ custom_logo }}">
        <link rel="apple-touch-icon" sizes="180x180" href="{{ custom_logo }}">
        <link rel="icon" type="image/png" sizes="32x32" href="{{ custom_logo }}">
        <link rel="icon" type="image/png" sizes="16x16" href="{{ custom_logo }}">
    {% else %}
        <!-- Static fallback favicons -->
        <link rel="icon" type="image/x-icon" href="{% load static %}{% static 'favicon.ico' %}">
        <link rel="apple-touch-icon" sizes="180x180" href="{% load static %}{% static 'images/apple-touch-icon.png' %}">
        <link rel="icon" type="image/png" sizes="32x32" href="{% load static %}{% static 'images/favicon-32x32.png' %}">
        <link rel="icon" type="image/png" sizes="16x16" href="{% load static %}{% static 'images/favicon-16x16.png' %}">
    {% endif %}

    <!-- PWA Manifest -->
    <link rel="manifest" href="{% load static %}{% static 'manifest.json' %}">
    <meta name="theme-color" content="#121d83">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Pentora">
    <meta name="mobile-web-app-capable" content="yes">

    <!-- Preload critical resources -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/daisyui@4.12.10/dist/full.min.css" as="style">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style">
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">

    <meta name="msvalidate.01" content="B22122BDFE18A09A555F2621A0E58E75" />
    <meta name="google-site-verification" content="A_Kcye10Uv-VNismGcofJo8uoiHPW5EUPTKRIo37V1c" />

    <!-- Tailwind CSS + DaisyUI -->
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.12.10/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Enhanced UI CSS -->
    <link rel="stylesheet" href="{% load static %}{% static 'css/enhanced-ui.css' %}">

    <!-- Custom CSS -->
    <style>
        :root {
            --primary-blue: #121d83;
            --primary-purple: #39127e;
            --success-green: #22C55E;
            --error-red: #EF4444;
            --bg-light: #F9FAFB;
            --text-dark: #111827;
            --text-gray: #374151;
            --card-white: #FFFFFF;
        }

        .gradient-bg {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-purple) 100%);
        }

        .footer-gradient {
            background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #581c87 100%);
        }

        .card-hover:hover {
            transform: translateY(-2px);
            transition: transform 0.2s ease-in-out;
        }

        .nav-item {
            transition: all 0.2s ease;
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
        }

        .nav-item:hover {
            background: rgba(59, 130, 246, 0.1);
            transform: translateY(-1px);
        }

        .nav-icon {
            font-size: 1.25rem;
            margin-right: 0.5rem;
        }

        .mobile-nav-item {
            padding: 1rem;
            border-bottom: 1px solid #E5E7EB;
            transition: background 0.2s ease;
        }

        .mobile-nav-item:hover {
            background: rgba(59, 130, 246, 0.05);
        }

        .tooltip {
            position: relative;
        }

        .tooltip:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--text-dark);
            color: white;
            padding: 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            white-space: nowrap;
            z-index: 1000;
        }

        body {
            background-color: var(--bg-light);
            color: var(--text-gray);
        }

        .card {
            background: var(--card-white);
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        /* PWA Install Button */
        #install-app {
            display: none;
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }

        /* Offline indicator */
        .offline-indicator {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #f59e0b;
            color: white;
            text-align: center;
            padding: 8px;
            z-index: 1001;
            transform: translateY(-100%);
            transition: transform 0.3s ease;
        }

        .offline-indicator.show {
            transform: translateY(0);
        }

        /* Scroll animations */
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease, transform 0.6s ease;
        }

        .animate-on-scroll.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        /* Keyboard navigation focus styles */
        .keyboard-navigation *:focus {
            outline: 2px solid var(--primary-blue);
            outline-offset: 2px;
        }

        /* Loading skeleton */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Lazy loading images */
        img.lazy {
            opacity: 0;
            transition: opacity 0.3s;
        }

        img.lazy.loaded {
            opacity: 1;
        }

        /* Accessibility Elements */

        /* Hide screen reader only content and unwanted elements */
        .sr-only {
            position: absolute !important;
            width: 1px !important;
            height: 1px !important;
            padding: 0 !important;
            margin: -1px !important;
            overflow: hidden !important;
            clip: rect(0, 0, 0, 0) !important;
            white-space: nowrap !important;
            border: 0 !important;
        }

        .sr-only:focus {
            position: fixed !important;
            top: 1rem !important;
            left: 1rem !important;
            width: auto !important;
            height: auto !important;
            padding: inherit !important;
            margin: inherit !important;
            overflow: visible !important;
            clip: auto !important;
            white-space: normal !important;
            z-index: 9999 !important;
            background: #1e40af !important;
            color: white !important;
            border-radius: 0.375rem !important;
        }

        /* Hide any unwanted symbols or content */
        body::before,
        nav::before,
        .navbar::before {
            display: none !important;
            content: none !important;
        }

        /* Hide DaisyUI dropdown indicators and unwanted elements */
        .dropdown:not(.dropdown-open) .dropdown-content {
            display: none !important;
        }

        /* Hide any unwanted dropdown arrows or indicators */
        .dropdown-toggle::after,
        .dropdown > summary::after,
        .dropdown > details > summary::after {
            display: none !important;
        }

        /* Ensure no unwanted pseudo-elements are visible */
        .navbar *::before,
        .navbar *::after {
            content: none !important;
        }

        /* Override any DaisyUI default dropdown indicators */
        .dropdown > *:not(.dropdown-content)::after {
            display: none !important;
        }

        /* Professional Profile Dropdown Styles */
        .profile-avatar-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border-radius: 50px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            cursor: pointer;
            transition: all 0.2s ease;
            color: white;
        }

        .profile-avatar-btn:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .profile-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .profile-initials {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 12px;
            color: white;
        }

        .profile-dropdown-icon {
            font-size: 10px;
            opacity: 0.8;
            transition: transform 0.2s ease;
        }

        .dropdown.dropdown-open .profile-dropdown-icon {
            transform: rotate(180deg);
        }

        .profile-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            margin-top: 8px;
            width: 280px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(0, 0, 0, 0.08);
            overflow: hidden;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease;
            /* Ensure dropdown is above other elements */
            pointer-events: none;
        }

        .dropdown.dropdown-open .profile-dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
            pointer-events: auto;
        }

        .profile-header {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .profile-avatar-large {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid rgba(255, 255, 255, 0.3);
            flex-shrink: 0;
        }

        .profile-initials-large {
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 16px;
            color: white;
        }

        .profile-info {
            flex: 1;
            min-width: 0;
        }

        .profile-name {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .profile-email {
            font-size: 13px;
            opacity: 0.9;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .profile-grade {
            font-size: 12px;
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 8px;
            border-radius: 12px;
            display: inline-block;
            margin-top: 4px;
        }

        .profile-divider {
            height: 1px;
            background: rgba(0, 0, 0, 0.08);
            margin: 0;
        }

        .profile-menu {
            padding: 8px 0;
        }

        .profile-menu-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.15s ease;
            font-size: 14px;
            font-weight: 500;
            position: relative;
        }

        .profile-menu-item:hover {
            background: #f8fafc;
            color: #2563eb;
            text-decoration: none;
        }

        .profile-menu-item i {
            width: 16px;
            text-align: center;
            opacity: 0.7;
        }

        .profile-menu-item:hover i {
            opacity: 1;
        }

        .profile-menu-item-danger {
            color: #dc2626;
        }

        .profile-menu-item-danger:hover {
            background: #fef2f2;
            color: #dc2626;
        }

        .profile-menu-badge {
            margin-left: auto;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .profile-menu-badge-success {
            background: #dcfce7;
            color: #166534;
        }

        .profile-menu-badge-warning {
            background: #fef3c7;
            color: #92400e;
        }

        /* Mobile Responsive */
        @media (max-width: 640px) {
            .profile-dropdown-menu {
                width: 260px;
                right: -10px;
                z-index: 9999 !important; /* Higher z-index for mobile */
                position: fixed !important; /* Use fixed positioning on mobile */
                top: 60px !important; /* Position below navbar */
                right: 10px !important; /* Ensure it's visible */
                max-height: calc(100vh - 80px); /* Prevent overflow */
                overflow-y: auto; /* Allow scrolling if needed */
            }

            /* Ensure dropdown parent has relative positioning */
            .dropdown.dropdown-end {
                position: relative;
            }

            .profile-header {
                padding: 16px;
            }

            .profile-avatar-large {
                width: 40px;
                height: 40px;
            }

            .profile-name {
                font-size: 15px;
            }

            .profile-email {
                font-size: 12px;
            }

            /* Add backdrop for mobile dropdown */
            .dropdown.dropdown-open::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.1);
                z-index: 9998;
                backdrop-filter: blur(2px);
            }
        }

        /* Professional Mobile Menu Styles */
        #mobile-menu-sidebar {
            box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .mobile-nav-link {
            transition: all 0.2s ease-in-out;
        }

        .mobile-nav-link:hover {
            transform: translateX(4px);
        }

        .mobile-nav-link:active {
            transform: translateX(2px);
        }

        /* Mobile navbar improvements */
        @media (max-width: 1279px) {
            .navbar {
                padding: 0;
                min-height: 4rem;
            }

            .navbar .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }

            /* Mobile menu toggle button */
            #mobile-menu-toggle {
                width: 2.5rem;
                height: 2.5rem;
                min-height: 2.5rem;
                flex-shrink: 0;
                display: block !important;
            }

            /* Logo adjustments for mobile */
            .navbar a[href*="home"] {
                padding: 0.5rem;
            }

            .navbar a[href*="home"] span {
                font-size: 1.25rem;
                font-weight: 700;
            }

            /* User avatar adjustments */
            .btn-circle.avatar {
                width: 2.5rem;
                height: 2.5rem;
                min-height: 2.5rem;
                flex-shrink: 0;
            }

            /* Better mobile button visibility */
            .navbar .btn-ghost {
                background: rgba(255, 255, 255, 0.1) !important;
                border: 1px solid rgba(255, 255, 255, 0.3) !important;
                backdrop-filter: blur(10px);
                color: white !important;
            }

            .navbar .btn-ghost:hover {
                background: rgba(255, 255, 255, 0.2) !important;
                border: 1px solid rgba(255, 255, 255, 0.5) !important;
                color: white !important;
            }

            /* Ensure all navbar text is white */
            .navbar .btn,
            .navbar .btn span,
            .navbar .btn svg {
                color: white !important;
            }

            /* Hide any overflow content */
            .navbar {
                overflow: hidden;
            }
        }

        /* Desktop navbar - Hide mobile toggle completely */
        @media (min-width: 1280px) {
            #mobile-menu-toggle {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                pointer-events: none !important;
            }

            /* Ensure desktop navigation is visible */
            .navbar-center {
                display: flex !important;
            }

            /* Enhanced logo sizing for desktop */
            .navbar a[href*="home"] img {
                max-width: 600px;
            }

            /* Extra large screens */
            @media (min-width: 1536px) {
                .navbar a[href*="home"] img {
                    max-width: 700px;
                }
            }
        }

        /* Smooth transitions for mobile menu */
        #mobile-menu-overlay {
            transition: opacity 0.3s ease-in-out;
        }

        #mobile-menu-sidebar {
            transition: transform 0.3s ease-in-out;
        }

        /* Prevent scrolling when mobile menu is open */
        body.mobile-menu-open {
            overflow: hidden;
        }
    </style>

    {% block extra_css %}{% endblock %}

    <!-- Google Analytics -->
    {% if not debug %}
    <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'GA_MEASUREMENT_ID');
    </script>
    {% endif %}
</head>
<body class="min-h-screen bg-base-100">
    <!-- Navigation -->
    <nav class="navbar gradient-bg shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between w-full h-16">
                <!-- Left Section: Mobile Menu + Logo -->
                <div class="flex items-center space-x-3">
                    <!-- Mobile menu button - Only show on mobile/tablet (hidden on desktop 1280px+) -->
                    <button id="mobile-menu-toggle" class="block xl:!hidden btn btn-ghost btn-circle text-white hover:bg-white hover:bg-opacity-20 border border-white border-opacity-30 w-10 h-10 min-h-10" aria-label="Toggle menu" style="display: block;">
                        <svg id="hamburger-icon" class="w-5 h-5 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                        <svg id="close-icon" class="w-5 h-5 hidden transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>

                    <!-- Logo -->
                    <a href="{% url 'core:home' %}" class="flex items-center text-white hover:text-opacity-80 transition-all duration-200">
                        {% get_site_logo as custom_logo %}
                        {% if custom_logo %}
                            <img src="{{ custom_logo }}" alt="{% get_site_name %}" class="h-12 sm:h-14 lg:h-16 w-auto max-w-[320px] sm:max-w-[420px] lg:max-w-[520px] xl:max-w-[600px] 2xl:max-w-[700px]">
                        {% else %}
                            <div class="flex items-center">
                                <div class="w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-full bg-white bg-opacity-20 flex items-center justify-center mr-3 sm:mr-4">
                                    <i class="fas fa-graduation-cap text-white text-base sm:text-lg lg:text-xl"></i>
                                </div>
                                <span class="font-bold text-xl sm:text-2xl lg:text-3xl xl:text-4xl 2xl:text-5xl">{% get_site_name %}</span>
                            </div>
                        {% endif %}
                    </a>
                </div>

        <!-- Desktop Navigation -->
        <div class="navbar-center hidden xl:flex">
            <ul class="menu menu-horizontal px-1 space-x-3">
                <!-- Always show these to visitors -->
                <li>
                    <a href="{% url 'core:home' %}" class="nav-item tooltip flex items-center text-white hover:bg-white hover:bg-opacity-20 px-4 py-2 rounded-lg" data-tooltip="Home page">
                        <i class="fas fa-home nav-icon text-lg mr-2"></i>
                        <span class="font-medium text-lg">Home</span>
                    </a>
                </li>
                <li>
                    <a href="{% url 'subjects:simple_learn' %}" class="nav-item tooltip flex items-center text-white hover:bg-white hover:bg-opacity-20 px-4 py-2 rounded-lg" data-tooltip="Browse subjects and lessons">
                        <i class="fas fa-book nav-icon text-lg mr-2"></i>
                        <span class="font-medium text-lg">Learn</span>
                    </a>
                </li>
                <li>
                    <a href="{% url 'subjects:quiz_list' %}" class="nav-item tooltip flex items-center text-white hover:bg-white hover:bg-opacity-20 px-4 py-2 rounded-lg" data-tooltip="Practice quizzes">
                        <i class="fas fa-question-circle nav-icon text-lg mr-2"></i>
                        <span class="font-medium text-lg">Quiz</span>
                    </a>
                </li>

                {% if user.is_authenticated %}
                    <li>
                        <a href="{% url 'core:dashboard' %}" class="nav-item tooltip flex items-center text-white hover:bg-white hover:bg-opacity-20 px-4 py-2 rounded-lg" data-tooltip="Your learning dashboard">
                            <i class="fas fa-tachometer-alt nav-icon text-lg mr-2"></i>
                            <span class="font-medium text-lg">Dashboard</span>
                        </a>
                    </li>
                    {% if billing_enabled %}
                    <li>
                        <a href="{% url 'billing:dashboard' %}" class="nav-item tooltip flex items-center text-white hover:bg-white hover:bg-opacity-20 px-4 py-2 rounded-lg" data-tooltip="Manage your subscription">
                            <i class="fas fa-credit-card nav-icon text-lg mr-2"></i>
                            <span class="font-medium text-lg">Billing</span>
                            {% if not user_subscription_active %}
                            <span class="badge badge-warning badge-xs ml-2">!</span>
                            {% endif %}
                        </a>
                    </li>
                    {% endif %}
                {% endif %}
            </ul>
        </div>

                <!-- Right Section: User Menu -->
                <div class="flex items-center space-x-2">
                    {% if user.is_authenticated %}
                        <div class="dropdown dropdown-end">
                            <div tabindex="0" role="button" class="profile-avatar-btn">
                                <div class="profile-avatar">
                                    {% if user.profile_picture %}
                                        <img src="{{ user.profile_picture.url }}" alt="{{ user.full_name }}" class="w-full h-full rounded-full object-cover" />
                                    {% else %}
                                        <div class="profile-initials">
                                            {{ user.first_name|first|upper }}{{ user.last_name|first|upper }}
                                        </div>
                                    {% endif %}
                                </div>
                                <i class="fas fa-chevron-down profile-dropdown-icon"></i>
                            </div>

                            <div tabindex="0" class="dropdown-content profile-dropdown-menu">
                                <!-- User Info Header -->
                                <div class="profile-header">
                                    <div class="profile-avatar-large">
                                        {% if user.profile_picture %}
                                            <img src="{{ user.profile_picture.url }}" alt="{{ user.full_name }}" class="w-full h-full rounded-full object-cover" />
                                        {% else %}
                                            <div class="profile-initials-large">
                                                {{ user.first_name|first|upper }}{{ user.last_name|first|upper }}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="profile-info">
                                        <div class="profile-name">{{ user.full_name }}</div>
                                        <div class="profile-email">{{ user.email }}</div>
                                        {% if user.current_class_level %}
                                            <div class="profile-grade">{{ user.get_current_class_level_display }}</div>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="profile-divider"></div>

                                <!-- Menu Items -->
                                <div class="profile-menu">
                                    <a href="{% url 'users:profile' %}" class="profile-menu-item">
                                        <i class="fas fa-user"></i>
                                        <span>Profile & Settings</span>
                                    </a>

                                    <a href="{% url 'core:dashboard' %}" class="profile-menu-item">
                                        <i class="fas fa-tachometer-alt"></i>
                                        <span>Dashboard</span>
                                    </a>

                                    <a href="{% url 'progress:overview' %}" class="profile-menu-item">
                                        <i class="fas fa-chart-line"></i>
                                        <span>My Progress</span>
                                    </a>

                                    <a href="{% url 'progress:achievements' %}" class="profile-menu-item">
                                        <i class="fas fa-trophy"></i>
                                        <span>Achievements</span>
                                    </a>

                                    {% if billing_enabled %}
                                    <a href="{% url 'billing:dashboard' %}" class="profile-menu-item">
                                        <i class="fas fa-crown"></i>
                                        <span>
                                            {% if user_subscription.is_active %}
                                                {{ user_subscription.plan.name }} Plan
                                            {% else %}
                                                Upgrade to Premium
                                            {% endif %}
                                        </span>
                                        {% if user_subscription.is_active %}
                                            <span class="profile-menu-badge profile-menu-badge-success">Active</span>
                                        {% else %}
                                            <span class="profile-menu-badge profile-menu-badge-warning">Free</span>
                                        {% endif %}
                                    </a>
                                    {% endif %}

                                    <a href="{% url 'core:help' %}" class="profile-menu-item">
                                        <i class="fas fa-question-circle"></i>
                                        <span>Help Center</span>
                                    </a>
                                </div>

                                <div class="profile-divider"></div>

                                <!-- Sign Out -->
                                <div class="profile-menu">
                                    <a href="{% url 'users:logout' %}" class="profile-menu-item profile-menu-item-danger">
                                        <i class="fas fa-sign-out-alt"></i>
                                        <span>Sign Out</span>
                                    </a>
                                </div>
                            </div>
                        </div>
            {% else %}
                <div class="flex items-center space-x-1 sm:space-x-2">
                    <a href="{% url 'users:login' %}" class="btn btn-ghost hover:bg-white hover:bg-opacity-20 font-medium btn-sm sm:btn-md px-2 sm:px-4" style="color: white !important; border: 1px solid rgba(255,255,255,0.3);">
                        <span style="color: white !important;">Sign In</span>
                    </a>
                    <a href="{% url 'users:register' %}" class="btn hover:bg-white hover:text-blue-600 font-medium btn-sm sm:btn-md px-2 sm:px-4" style="background: rgba(255,255,255,0.2); color: white !important; border: 1px solid rgba(255,255,255,0.3);">
                        <span style="color: white !important;">Get Started</span>
                    </a>
                </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- Mobile Menu Overlay -->
    <div id="mobile-menu-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 xl:hidden hidden transition-opacity duration-300"></div>

    <!-- Mobile Menu Sidebar -->
    <div id="mobile-menu-sidebar" class="fixed top-0 left-0 h-full w-80 bg-white shadow-xl z-50 xl:hidden transform -translate-x-full transition-transform duration-300 ease-in-out">
        <div class="flex flex-col h-full">
            <!-- Mobile Menu Header -->
            <div class="flex items-center justify-between p-4 border-b border-gray-200 gradient-bg">
                <div class="flex items-center">
                    {% get_site_logo as custom_logo %}
                    {% if custom_logo %}
                        <img src="{{ custom_logo }}" alt="{% get_site_name %}" class="h-8 w-auto mr-2">
                    {% else %}
                        <div class="w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center mr-2">
                            <i class="fas fa-graduation-cap text-white text-sm"></i>
                        </div>
                        <span class="text-white font-bold text-lg">{% get_site_name %}</span>
                    {% endif %}
                </div>
                <button id="mobile-menu-close" class="btn btn-ghost btn-circle text-white hover:bg-white hover:bg-opacity-20">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Menu Content -->
            <div class="flex-1 overflow-y-auto py-4">
                <!-- Main Navigation -->
                <div class="px-4 mb-6">
                    <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Navigation</h3>
                    <nav class="space-y-1">
                        <a href="{% url 'core:home' %}" class="mobile-nav-link flex items-center px-3 py-2 rounded-lg text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200">
                            <i class="fas fa-home text-blue-500 w-5 text-center mr-3"></i>
                            <span class="font-medium">Home</span>
                        </a>
                        <a href="{% url 'subjects:simple_learn' %}" class="mobile-nav-link flex items-center px-3 py-2 rounded-lg text-gray-700 hover:bg-green-50 hover:text-green-600 transition-colors duration-200">
                            <i class="fas fa-book text-green-500 w-5 text-center mr-3"></i>
                            <span class="font-medium">Learn</span>
                        </a>
                        <a href="{% url 'subjects:quiz_list' %}" class="mobile-nav-link flex items-center px-3 py-2 rounded-lg text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors duration-200">
                            <i class="fas fa-question-circle text-purple-500 w-5 text-center mr-3"></i>
                            <span class="font-medium">Quiz</span>
                        </a>
                    </nav>
                </div>

                {% if user.is_authenticated %}
                    <!-- User Section -->
                    <div class="px-4 mb-6">
                        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Account</h3>
                        <nav class="space-y-1">
                            <a href="{% url 'core:dashboard' %}" class="mobile-nav-link flex items-center px-3 py-2 rounded-lg text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200">
                                <i class="fas fa-tachometer-alt text-blue-600 w-5 text-center mr-3"></i>
                                <span class="font-medium">Dashboard</span>
                            </a>
                            <a href="{% url 'users:profile' %}" class="mobile-nav-link flex items-center px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-50 hover:text-gray-600 transition-colors duration-200">
                                <i class="fas fa-cog text-gray-500 w-5 text-center mr-3"></i>
                                <span class="font-medium">Settings</span>
                            </a>
                            {% if billing_enabled %}
                            <a href="{% url 'billing:dashboard' %}" class="mobile-nav-link flex items-center px-3 py-2 rounded-lg text-gray-700 hover:bg-green-50 hover:text-green-600 transition-colors duration-200">
                                <i class="fas fa-credit-card text-green-500 w-5 text-center mr-3"></i>
                                <span class="font-medium">Billing</span>
                                {% if not user_subscription_active %}
                                <span class="ml-auto bg-yellow-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full">!</span>
                                {% endif %}
                            </a>
                            {% endif %}
                        </nav>
                    </div>
                {% else %}
                    <!-- Guest Section -->
                    <div class="px-4 mb-6">
                        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Get Started</h3>
                        <nav class="space-y-2">
                            <a href="{% url 'users:login' %}" class="mobile-nav-link flex items-center px-3 py-2 rounded-lg text-blue-600 hover:bg-blue-50 transition-colors duration-200 border border-blue-200">
                                <i class="fas fa-sign-in-alt text-blue-500 w-5 text-center mr-3"></i>
                                <span class="font-medium">Sign In</span>
                            </a>
                            <a href="{% url 'users:register' %}" class="mobile-nav-link flex items-center px-3 py-2 rounded-lg text-white bg-green-600 hover:bg-green-700 transition-colors duration-200">
                                <i class="fas fa-user-plus text-white w-5 text-center mr-3"></i>
                                <span class="font-medium">Get Started</span>
                            </a>
                        </nav>
                    </div>
                {% endif %}
            </div>

            {% if user.is_authenticated %}
                <!-- User Info Footer -->
                <div class="border-t border-gray-200 p-4">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold text-sm mr-3">
                            {{ user.first_name|first|upper }}{{ user.last_name|first|upper }}
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">{{ user.full_name }}</p>
                            <p class="text-xs text-gray-500 truncate">{{ user.email }}</p>
                        </div>
                    </div>
                    <a href="{% url 'users:logout' %}" class="mobile-nav-link flex items-center px-3 py-2 rounded-lg text-red-600 hover:bg-red-50 transition-colors duration-200 w-full">
                        <i class="fas fa-sign-out-alt text-red-500 w-5 text-center mr-3"></i>
                        <span class="font-medium">Sign Out</span>
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Main Content -->
    <main id="main-content" class="min-h-screen">
        <!-- Billing Announcements -->
        <div class="container mx-auto px-4 pt-4">
            {% include 'billing/announcement_banner.html' %}
        </div>

        <!-- Messages -->
        {% if messages %}
            <div class="container mx-auto px-4 mb-4">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} mb-2">
                        <span>{{ message }}</span>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        {% block content %}{% endblock %}
    </main>



    <!-- Enhanced Modern Footer -->
    <footer class="footer-gradient text-white mt-12 sm:mt-20 relative overflow-hidden">
        <!-- Decorative background elements -->
        <div class="absolute inset-0 opacity-5 sm:opacity-10">
            <div class="absolute top-10 left-10 w-20 h-20 sm:w-32 sm:h-32 bg-white rounded-full blur-2xl sm:blur-3xl"></div>
            <div class="absolute bottom-10 right-10 w-16 h-16 sm:w-24 sm:h-24 bg-white rounded-full blur-xl sm:blur-2xl"></div>
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 sm:w-40 sm:h-40 bg-white rounded-full blur-2xl sm:blur-3xl"></div>
        </div>

        <div class="relative container mx-auto px-4 sm:px-6 py-8 sm:py-12 lg:py-16">
            <!-- Mobile: Single column with compact layout, Desktop: 4 columns -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 lg:gap-12">
                <!-- Enhanced Brand Section -->
                <div class="col-span-1 sm:col-span-2 lg:col-span-1 text-center lg:text-left">
                    <div class="flex items-center justify-center lg:justify-start mb-4 sm:mb-6">
                        <div class="w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-xl sm:rounded-2xl bg-white bg-opacity-20 flex items-center justify-center mr-3 sm:mr-4 backdrop-blur-sm">
                            <i class="fas fa-graduation-cap text-white text-lg sm:text-xl lg:text-2xl"></i>
                        </div>
                        <h3 class="text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
                            Pentora
                        </h3>
                    </div>
                    <p class="text-white text-opacity-90 mb-4 sm:mb-6 text-sm sm:text-base lg:text-lg leading-relaxed max-w-xs sm:max-w-sm mx-auto lg:mx-0">
                        Empowering underprivileged learners through quality education and innovative learning experiences.
                    </p>
                    <div class="flex justify-center lg:justify-start space-x-3 sm:space-x-4">
                        <a href="#" class="w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-lg sm:rounded-xl bg-white bg-opacity-20 flex items-center justify-center hover:bg-opacity-30 hover:scale-110 transition-all duration-300 backdrop-blur-sm group">
                            <i class="fab fa-facebook-f text-sm sm:text-base lg:text-lg group-hover:text-blue-300 transition-colors"></i>
                        </a>
                        <a href="#" class="w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-lg sm:rounded-xl bg-white bg-opacity-20 flex items-center justify-center hover:bg-opacity-30 hover:scale-110 transition-all duration-300 backdrop-blur-sm group">
                            <i class="fab fa-twitter text-sm sm:text-base lg:text-lg group-hover:text-blue-300 transition-colors"></i>
                        </a>
                        <a href="#" class="w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-lg sm:rounded-xl bg-white bg-opacity-20 flex items-center justify-center hover:bg-opacity-30 hover:scale-110 transition-all duration-300 backdrop-blur-sm group">
                            <i class="fab fa-instagram text-sm sm:text-base lg:text-lg group-hover:text-pink-300 transition-colors"></i>
                        </a>
                        <a href="#" class="w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-lg sm:rounded-xl bg-white bg-opacity-20 flex items-center justify-center hover:bg-opacity-30 hover:scale-110 transition-all duration-300 backdrop-blur-sm group">
                            <i class="fab fa-linkedin-in text-sm sm:text-base lg:text-lg group-hover:text-blue-300 transition-colors"></i>
                        </a>
                    </div>
                </div>

                <!-- Enhanced Quick Links -->
                <div class="text-center lg:text-left">
                    <h4 class="text-base sm:text-lg lg:text-xl font-bold mb-3 sm:mb-4 lg:mb-6 text-white">Quick Links</h4>
                    <ul class="space-y-2 sm:space-y-3">
                        <li><a href="{% url 'core:home' %}" class="text-white text-opacity-90 hover:text-opacity-100 hover:text-blue-200 transition-all text-sm sm:text-base lg:text-lg flex items-center justify-center lg:justify-start group">
                            <i class="fas fa-home mr-2 sm:mr-3 group-hover:scale-110 transition-transform text-xs sm:text-sm"></i>Home
                        </a></li>
                        <li><a href="{% url 'subjects:list' %}" class="text-white text-opacity-90 hover:text-opacity-100 hover:text-blue-200 transition-all text-sm sm:text-base lg:text-lg flex items-center justify-center lg:justify-start group">
                            <i class="fas fa-book mr-2 sm:mr-3 group-hover:scale-110 transition-transform text-xs sm:text-sm"></i>Subjects
                        </a></li>
                        <li><a href="{% url 'core:about' %}" class="text-white text-opacity-90 hover:text-opacity-100 hover:text-blue-200 transition-all text-sm sm:text-base lg:text-lg flex items-center justify-center lg:justify-start group">
                            <i class="fas fa-info-circle mr-2 sm:mr-3 group-hover:scale-110 transition-transform text-xs sm:text-sm"></i>About
                        </a></li>
                        <li><a href="{% url 'core:contact' %}" class="text-white text-opacity-90 hover:text-opacity-100 hover:text-blue-200 transition-all text-sm sm:text-base lg:text-lg flex items-center justify-center lg:justify-start group">
                            <i class="fas fa-envelope mr-2 sm:mr-3 group-hover:scale-110 transition-transform text-xs sm:text-sm"></i>Contact
                        </a></li>
                    </ul>
                </div>

                <!-- Enhanced Learning Section -->
                <div class="text-center lg:text-left">
                    <h4 class="text-lg lg:text-xl font-bold mb-4 lg:mb-6 text-white">Learning</h4>
                    <ul class="space-y-3">
                        {% if user.is_authenticated %}
                            <li><a href="{% url 'core:dashboard' %}" class="text-white text-opacity-90 hover:text-opacity-100 hover:text-green-200 transition-all text-base lg:text-lg flex items-center justify-center lg:justify-start group">
                                <i class="fas fa-tachometer-alt mr-3 group-hover:scale-110 transition-transform"></i>Dashboard
                            </a></li>
                            <li><a href="{% url 'progress:overview' %}" class="text-white text-opacity-90 hover:text-opacity-100 hover:text-green-200 transition-all text-base lg:text-lg flex items-center justify-center lg:justify-start group">
                                <i class="fas fa-chart-line mr-3 group-hover:scale-110 transition-transform"></i>Progress
                            </a></li>
                            <li><a href="{% url 'progress:achievements' %}" class="text-white text-opacity-90 hover:text-opacity-100 hover:text-green-200 transition-all text-base lg:text-lg flex items-center justify-center lg:justify-start group">
                                <i class="fas fa-trophy mr-3 group-hover:scale-110 transition-transform"></i>Achievements
                            </a></li>
                        {% else %}
                            <li><a href="{% url 'users:register' %}" class="text-white text-opacity-90 hover:text-opacity-100 hover:text-green-200 transition-all text-base lg:text-lg flex items-center justify-center lg:justify-start group">
                                <i class="fas fa-user-plus mr-3 group-hover:scale-110 transition-transform"></i>Get Started
                            </a></li>
                            <li><a href="{% url 'users:login' %}" class="text-white text-opacity-90 hover:text-opacity-100 hover:text-green-200 transition-all text-base lg:text-lg flex items-center justify-center lg:justify-start group">
                                <i class="fas fa-sign-in-alt mr-3 group-hover:scale-110 transition-transform"></i>Sign In
                            </a></li>
                        {% endif %}
                        <li><a href="{% url 'subjects:list' %}" class="text-white text-opacity-90 hover:text-opacity-100 hover:text-green-200 transition-all text-base lg:text-lg flex items-center justify-center lg:justify-start group">
                            <i class="fas fa-search mr-3 group-hover:scale-110 transition-transform"></i>Browse
                        </a></li>
                    </ul>
                </div>

                <!-- Enhanced Support Section -->
                <div class="text-center lg:text-left">
                    <h4 class="text-lg lg:text-xl font-bold mb-4 lg:mb-6 text-white">Support</h4>
                    <ul class="space-y-3">
                        <li><a href="{% url 'core:help' %}" class="text-white text-opacity-90 hover:text-opacity-100 hover:text-yellow-200 transition-all text-base lg:text-lg flex items-center justify-center lg:justify-start group">
                            <i class="fas fa-life-ring mr-3 group-hover:scale-110 transition-transform"></i>Help Center
                        </a></li>
                        <li><a href="{% url 'core:privacy' %}" class="text-white text-opacity-90 hover:text-opacity-100 hover:text-yellow-200 transition-all text-base lg:text-lg flex items-center justify-center lg:justify-start group">
                            <i class="fas fa-shield-alt mr-3 group-hover:scale-110 transition-transform"></i>Privacy Policy
                        </a></li>
                        <li><a href="{% url 'core:terms' %}" class="text-white text-opacity-90 hover:text-opacity-100 hover:text-yellow-200 transition-all text-base lg:text-lg flex items-center justify-center lg:justify-start group">
                            <i class="fas fa-file-contract mr-3 group-hover:scale-110 transition-transform"></i>Terms of Service
                        </a></li>
                        <li><a href="{% url 'core:contact' %}" class="text-white text-opacity-90 hover:text-opacity-100 hover:text-yellow-200 transition-all text-base lg:text-lg flex items-center justify-center lg:justify-start group">
                            <i class="fas fa-headset mr-3 group-hover:scale-110 transition-transform"></i>Support
                        </a></li>
                    </ul>
                </div>
            </div>

            <!-- Enhanced Bottom Section -->
            <div class="border-t border-white border-opacity-30 mt-8 sm:mt-12 pt-8 sm:pt-10">
                <div class="flex flex-col lg:flex-row justify-between items-center text-center lg:text-left space-y-4 lg:space-y-0">
                    <div class="text-white text-opacity-90 text-base lg:text-lg font-medium">
                        © 2025 Pentora Educational Platform. Empowering learners to rebuild their dreams.
                    </div>
                    <div class="flex items-center space-x-3 text-base lg:text-lg">
                        <span class="text-white text-opacity-90 font-medium">Made with</span>
                        <i class="fas fa-heart text-red-400 text-xl animate-pulse"></i>
                        <span class="text-white text-opacity-90 font-medium">for underprivileged learners</span>
                    </div>
                </div>

                <!-- Additional stats or info -->
                <div class="mt-6 pt-6 border-t border-white border-opacity-20 text-center">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
                        <div class="text-center">
                            <div class="text-white text-opacity-90 text-sm lg:text-base font-medium">Free Education</div>
                            <div class="text-white text-opacity-70 text-xs lg:text-sm">Always & Forever</div>
                        </div>
                        <div class="text-center">
                            <div class="text-white text-opacity-90 text-sm lg:text-base font-medium">Quality Content</div>
                            <div class="text-white text-opacity-70 text-xs lg:text-sm">Expert Curated</div>
                        </div>
                        <div class="text-center">
                            <div class="text-white text-opacity-90 text-sm lg:text-base font-medium">Progress Tracking</div>
                            <div class="text-white text-opacity-70 text-xs lg:text-sm">Personal Growth</div>
                        </div>
                        <div class="text-center">
                            <div class="text-white text-opacity-90 text-sm lg:text-base font-medium">Community</div>
                            <div class="text-white text-opacity-70 text-xs lg:text-sm">Learn Together</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Accessibility Feedback Widget -->
    {% include 'includes/feedback_widget.html' %}

    <!-- Scripts -->
    <script>
        // Theme toggle functionality
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        }

        // Load saved theme
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
        });
    </script>

    <!-- PWA Install Button -->
    <button id="install-app" class="btn btn-primary btn-circle shadow-lg">
        <i class="fas fa-download"></i>
    </button>

    <!-- Offline Indicator -->
    <div class="offline-indicator" id="offline-indicator">
        <i class="fas fa-wifi-slash mr-2"></i>
        You're offline. Some features may be limited.
    </div>

    <!-- Enhanced JavaScript -->
    <script src="{% load static %}{% static 'js/app.js' %}?v=4"></script>
    <script src="{% load static %}{% static 'js/ux-enhancements.js' %}"></script>
    <script src="{% load static %}{% static 'js/mobile-menu.js' %}?v=1"></script>
    <script src="{% load static %}{% static 'js/performance-optimizer.js' %}?v=4"></script>
    <script src="{% load static %}{% static 'js/ui-interactions.js' %}"></script>
    <script src="{% load static %}{% static 'js/page-specific-enhancements.js' %}?v=3"></script>

    <!-- Global Form Enhancement Script -->
    <script>
    // Toast notification system
    function showToast(message, type = 'info', duration = 5000) {
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full`;

        const colors = {
            success: 'bg-green-100 border border-green-400 text-green-700',
            error: 'bg-red-100 border border-red-400 text-red-700',
            warning: 'bg-yellow-100 border border-yellow-400 text-yellow-700',
            info: 'bg-blue-100 border border-blue-400 text-blue-700'
        };

        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-triangle',
            warning: 'fas fa-exclamation-circle',
            info: 'fas fa-info-circle'
        };

        toast.className += ` ${colors[type] || colors.info}`;
        toast.innerHTML = `
            <div class="flex items-center">
                <i class="${icons[type] || icons.info} mr-2"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-lg font-bold opacity-70 hover:opacity-100">&times;</button>
            </div>
        `;

        document.body.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full');
        }, 100);

        // Auto remove
        setTimeout(() => {
            toast.classList.add('translate-x-full');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }, duration);
    }

    // Profile dropdown functionality
    function setupProfileDropdown() {
        const dropdown = document.querySelector('.dropdown.dropdown-end');
        const dropdownButton = dropdown?.querySelector('[role="button"]');
        const dropdownContent = dropdown?.querySelector('.dropdown-content');

        if (!dropdown || !dropdownButton || !dropdownContent) {
            return;
        }

        // Toggle dropdown on click
        dropdownButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const isOpen = dropdown.classList.contains('dropdown-open');

            // Close all other dropdowns first
            document.querySelectorAll('.dropdown.dropdown-open').forEach(d => {
                d.classList.remove('dropdown-open');
            });

            // Toggle this dropdown
            if (!isOpen) {
                dropdown.classList.add('dropdown-open');

                // On mobile, ensure dropdown is positioned correctly
                if (window.innerWidth <= 640) {
                    setTimeout(() => {
                        const dropdownRect = dropdownContent.getBoundingClientRect();

                        // Ensure dropdown is visible on screen
                        if (dropdownRect.right > window.innerWidth) {
                            dropdownContent.style.right = '10px';
                            dropdownContent.style.left = 'auto';
                        }
                        if (dropdownRect.bottom > window.innerHeight) {
                            dropdownContent.style.top = 'auto';
                            dropdownContent.style.bottom = '70px';
                        }
                    }, 10);
                }
            } else {
                dropdown.classList.remove('dropdown-open');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!dropdown.contains(e.target)) {
                dropdown.classList.remove('dropdown-open');
            }
        });

        // Close dropdown on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                dropdown.classList.remove('dropdown-open');
            }
        });

        // Prevent dropdown content clicks from closing the dropdown
        dropdownContent.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }

    // Global form debugging and enhancement
    document.addEventListener('DOMContentLoaded', function() {

        // Fix profile dropdown functionality
        setupProfileDropdown();

        // Add form enhancements
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                // Show a subtle loading indicator for forms without custom loading
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn && !submitBtn.hasAttribute('data-custom-loading')) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
                    submitBtn.disabled = true;

                    // Show processing toast
                    showToast('Processing your request...', 'info', 10000);

                    // Reset after 30 seconds if no response
                    setTimeout(() => {
                        if (submitBtn.disabled) {
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;

                            // Show timeout error
                            showToast('Request timed out. Please check your internet connection and try again.', 'error', 8000);
                        }
                    }, 30000);
                }
            });
        });

        // Add network status monitoring
        window.addEventListener('online', () => {
            console.log('Network: Online');
            showToast('Connection restored!', 'success', 3000);
        });

        window.addEventListener('offline', () => {
            console.log('Network: Offline');
            showToast('You appear to be offline. Please check your internet connection.', 'warning', 8000);
        });
    });
    </script>

    <!-- Service Worker Registration -->
    <script>
        // Register service worker for PWA functionality
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/static/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }

        // Handle online/offline status
        function updateOnlineStatus() {
            const indicator = document.getElementById('offline-indicator');
            if (navigator.onLine) {
                indicator.classList.remove('show');
            } else {
                indicator.classList.add('show');
            }
        }

        window.addEventListener('online', updateOnlineStatus);
        window.addEventListener('offline', updateOnlineStatus);

        // Initial check
        updateOnlineStatus();

        // Performance monitoring
        window.addEventListener('load', () => {
            // Monitor Core Web Vitals
            if ('web-vitals' in window) {
                // This would require importing the web-vitals library
                // getCLS(console.log);
                // getFID(console.log);
                // getFCP(console.log);
                // getLCP(console.log);
                // getTTFB(console.log);
            }
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
