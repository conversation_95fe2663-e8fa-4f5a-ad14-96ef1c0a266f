{% extends 'admin_panel/base.html' %}
{% load humanize %}

{% block title %}Analytics & Monitoring{% endblock %}
{% block page_title %}Analytics & Monitoring Dashboard{% endblock %}

<!--
Performance Tracking Features Added:
- Interactive performance API testing
- Enable/disable tracking controls
- Real-time metrics viewing
- Console output for debugging
- CSRF token status monitoring
- Consolidated monitoring system (removed duplicates)
- Quick access from admin sidebar
-->

{% block extra_css %}
<style>
    .system-metric-card {
        background: white;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        margin-bottom: 24px;
        border-left: 4px solid var(--primary-color);
    }

    .metric-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 16px;
    }

    .metric-value {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 4px;
    }

    .metric-label {
        color: #6b7280;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .progress-bar-custom {
        background-color: #f3f4f6;
        border-radius: 6px;
        height: 8px;
        overflow: hidden;
        margin-top: 8px;
    }

    .progress-fill {
        height: 100%;
        transition: width 0.3s ease;
        border-radius: 6px;
    }

    .status-good { background-color: #10b981; }
    .status-warning { background-color: #f59e0b; }
    .status-danger { background-color: #ef4444; }

    .analytics-grid {
        display: grid;
        gap: 24px;
        margin-bottom: 24px;
    }

    .grid-4 { grid-template-columns: repeat(4, 1fr); }
    .grid-3 { grid-template-columns: repeat(3, 1fr); }
    .grid-2 { grid-template-columns: repeat(2, 1fr); }

    @media (max-width: 1200px) {
        .grid-4 { grid-template-columns: repeat(2, 1fr); }
    }

    @media (max-width: 768px) {
        .grid-4, .grid-3, .grid-2 { grid-template-columns: 1fr; }
    }

    .data-table {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .table-header {
        background: #f8fafc;
        padding: 16px 24px;
        border-bottom: 1px solid #e5e7eb;
        font-weight: 600;
        color: #374151;
    }

    .table-content {
        padding: 16px 24px;
    }

    .data-row {
        display: flex;
        justify-content: between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f3f4f6;
    }

    .data-row:last-child {
        border-bottom: none;
    }

    .badge-custom {
        display: inline-flex;
        align-items: center;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .badge-success {
        background-color: #dcfce7;
        color: #166534;
    }

    .badge-warning {
        background-color: #fef3c7;
        color: #92400e;
    }

    .badge-danger {
        background-color: #fee2e2;
        color: #991b1b;
    }

    .badge-info {
        background-color: #dbeafe;
        color: #1e40af;
    }
</style>
{% endblock %}

{% block content %}
<!-- System Health Overview -->
<div class="analytics-grid grid-4" id="system-health">
    <div class="stats-card {% if system_metrics.cpu_usage > 80 %}danger{% elif system_metrics.cpu_usage > 60 %}warning{% else %}success{% endif %}">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h3 class="mb-1">{{ system_metrics.cpu_usage|floatformat:1 }}%</h3>
                <p class="mb-0">CPU Usage</p>
            </div>
            <div class="fs-1 opacity-75">
                <i class="fas fa-microchip"></i>
            </div>
        </div>
        <div class="progress-bar-custom mt-2">
            <div class="progress-fill {% if system_metrics.cpu_usage > 80 %}status-danger{% elif system_metrics.cpu_usage > 60 %}status-warning{% else %}status-good{% endif %}"
                 style="width: {{ system_metrics.cpu_usage }}%;"></div>
        </div>
    </div>

    <div class="stats-card {% if system_metrics.memory_usage > 80 %}danger{% elif system_metrics.memory_usage > 60 %}warning{% else %}success{% endif %}">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h3 class="mb-1">{{ system_metrics.memory_usage|floatformat:1 }}%</h3>
                <p class="mb-0">Memory Usage</p>
            </div>
            <div class="fs-1 opacity-75">
                <i class="fas fa-memory"></i>
            </div>
        </div>
        <div class="progress-bar-custom mt-2">
            <div class="progress-fill {% if system_metrics.memory_usage > 80 %}status-danger{% elif system_metrics.memory_usage > 60 %}status-warning{% else %}status-good{% endif %}"
                 style="width: {{ system_metrics.memory_usage }}%;"></div>
        </div>
    </div>

    <div class="stats-card {% if system_metrics.disk_usage > 80 %}danger{% elif system_metrics.disk_usage > 60 %}warning{% else %}success{% endif %}">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h3 class="mb-1">{{ system_metrics.disk_usage|floatformat:1 }}%</h3>
                <p class="mb-0">Disk Usage</p>
            </div>
            <div class="fs-1 opacity-75">
                <i class="fas fa-hdd"></i>
            </div>
        </div>
        <div class="progress-bar-custom mt-2">
            <div class="progress-fill {% if system_metrics.disk_usage > 80 %}status-danger{% elif system_metrics.disk_usage > 60 %}status-warning{% else %}status-good{% endif %}"
                 style="width: {{ system_metrics.disk_usage }}%;"></div>
        </div>
    </div>

    <div class="stats-card primary">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h3 class="mb-1">{{ user_stats.active_users_24h|intcomma }}</h3>
                <p class="mb-0">Active Users (24h)</p>
            </div>
            <div class="fs-1 opacity-75">
                <i class="fas fa-users"></i>
            </div>
        </div>
        <div class="mt-2">
            <small class="opacity-75">
                <i class="fas fa-arrow-up me-1"></i>
                {{ user_stats.new_users_24h }} new today
            </small>
        </div>
    </div>
</div>
<!-- User & Visit Statistics -->
<div class="analytics-grid grid-3 mb-4" id="user-analytics">
    <div class="stats-card info">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h3 class="mb-1">{{ user_stats.total_users|intcomma }}</h3>
                <p class="mb-0">Total Users</p>
            </div>
            <div class="fs-1 opacity-75">
                <i class="fas fa-user-friends"></i>
            </div>
        </div>
        <div class="mt-2">
            <small class="opacity-75">
                <i class="fas fa-plus me-1"></i>
                {{ user_stats.new_users_7d }} this week
            </small>
        </div>
    </div>

    <div class="stats-card success">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h3 class="mb-1">{{ visit_stats.visits_24h|intcomma }}</h3>
                <p class="mb-0">Page Visits (24h)</p>
            </div>
            <div class="fs-1 opacity-75">
                <i class="fas fa-eye"></i>
            </div>
        </div>
        <div class="mt-2">
            <small class="opacity-75">
                <i class="fas fa-chart-line me-1"></i>
                {{ visit_stats.visits_7d|intcomma }} this week
            </small>
        </div>
    </div>

    <div class="stats-card {% if error_stats.unresolved_errors > 0 %}danger{% else %}success{% endif %}">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h3 class="mb-1">{{ error_stats.unresolved_errors }}</h3>
                <p class="mb-0">Unresolved Errors</p>
            </div>
            <div class="fs-1 opacity-75">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
        </div>
        <div class="mt-2">
            <small class="opacity-75">
                <i class="fas fa-bug me-1"></i>
                {{ error_stats.errors_24h }} in last 24h
            </small>
        </div>
    </div>
</div>
<!-- Performance Tracking Tools -->
<div class="row mb-4" id="performance-tracking">
    <div class="col-12">
        <div class="data-table">
            <div class="table-header">
                <i class="fas fa-tachometer-alt me-2"></i>
                Performance Tracking & Monitoring Tools
            </div>
            <div class="table-content">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="fas fa-chart-line fa-2x text-primary"></i>
                                </div>
                                <h6 class="card-title">Performance API</h6>
                                <p class="card-text small text-muted">Test performance tracking endpoint</p>
                                <button onclick="testPerformanceAPI()" class="btn btn-primary btn-sm">
                                    <i class="fas fa-play me-1"></i>Test API
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="fas fa-toggle-on fa-2x text-success"></i>
                                </div>
                                <h6 class="card-title">Enable Tracking</h6>
                                <p class="card-text small text-muted">Enable detailed performance monitoring</p>
                                <button onclick="enablePerformanceTracking()" class="btn btn-success btn-sm">
                                    <i class="fas fa-power-off me-1"></i>Enable
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="fas fa-toggle-off fa-2x text-warning"></i>
                                </div>
                                <h6 class="card-title">Disable Tracking</h6>
                                <p class="card-text small text-muted">Disable performance monitoring</p>
                                <button onclick="disablePerformanceTracking()" class="btn btn-warning btn-sm">
                                    <i class="fas fa-power-off me-1"></i>Disable
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="fas fa-eye fa-2x text-info"></i>
                                </div>
                                <h6 class="card-title">View Metrics</h6>
                                <p class="card-text small text-muted">Check current performance metrics</p>
                                <button onclick="viewCurrentMetrics()" class="btn btn-info btn-sm">
                                    <i class="fas fa-search me-1"></i>View
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Status Display -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="alert alert-info" role="alert">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>Performance Tracking Status
                            </h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <strong>Tracking Enabled:</strong> <span id="tracking-status" class="badge badge-secondary">Checking...</span>
                                </div>
                                <div class="col-md-4">
                                    <strong>CSRF Token:</strong> <span id="csrf-status" class="badge badge-secondary">Checking...</span>
                                </div>
                                <div class="col-md-4">
                                    <strong>Metrics Count:</strong> <span id="metrics-count" class="badge badge-secondary">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Console Output -->
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Console Output:</h6>
                        <div id="console-output" style="background: #000; color: #0f0; padding: 15px; border-radius: 5px; font-family: monospace; height: 200px; overflow-y: auto; font-size: 12px;">
                            Performance tracking console initialized...<br>
                        </div>
                        <div class="mt-2">
                            <button onclick="clearConsole()" class="btn btn-secondary btn-sm">
                                <i class="fas fa-trash me-1"></i>Clear Console
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Data Tables -->
<div class="row mb-4">
    <div class="col-lg-6 mb-4">
        <div class="data-table">
            <div class="table-header">
                <i class="fas fa-fire me-2"></i>
                Popular Pages (7 days)
            </div>
            <div class="table-content">
                {% for page in visit_stats.popular_pages %}
                <div class="data-row">
                    <div>
                        <div class="fw-semibold">{{ page.page_title|default:"Untitled" }}</div>
                        <small class="text-muted">{{ page.page_url|truncatechars:40 }}</small>
                    </div>
                    <div>
                        <span class="badge-custom badge-info">{{ page.visit_count }} visits</span>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-4 text-muted">
                    <i class="fas fa-chart-bar fa-2x mb-2"></i>
                    <p>No page visit data available</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="data-table">
            <div class="table-header">
                <i class="fas fa-globe me-2"></i>
                Geographic Distribution (7 days)
            </div>
            <div class="table-content">
                {% for geo in geographic_data %}
                <div class="data-row">
                    <div class="fw-semibold">
                        <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                        {{ geo.country|default:"Unknown" }}
                    </div>
                    <div>
                        <span class="badge-custom badge-success">{{ geo.count }} visitors</span>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-4 text-muted">
                    <i class="fas fa-globe fa-2x mb-2"></i>
                    <p>No geographic data available</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
<!-- Recent Activity & Errors -->
<div class="row mb-4">
    <div class="col-lg-6 mb-4">
        <div class="data-table">
            <div class="table-header">
                <i class="fas fa-clock me-2"></i>
                Recent User Activity
            </div>
            <div class="table-content">
                {% for activity in recent_activities %}
                <div class="data-row">
                    <div>
                        <div class="fw-semibold">{{ activity.user.get_full_name|default:activity.user.email }}</div>
                        <small class="text-muted">{{ activity.activity_type|title }} - {{ activity.description|truncatechars:40 }}</small>
                    </div>
                    <div>
                        <small class="text-muted">{{ activity.timestamp|timesince }} ago</small>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-4 text-muted">
                    <i class="fas fa-user-clock fa-2x mb-2"></i>
                    <p>No recent activity</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="data-table">
            <div class="table-header">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Recent Errors
                {% if error_stats.unresolved_errors > 0 %}
                    <span class="badge-custom badge-danger ms-2">{{ error_stats.unresolved_errors }} unresolved</span>
                {% endif %}
            </div>
            <div class="table-content">
                {% for error in recent_errors %}
                <div class="data-row">
                    <div>
                        <div class="fw-semibold text-danger">{{ error.error_type }}</div>
                        <small class="text-muted">{{ error.error_message|truncatechars:40 }}</small>
                        {% if error.request_url %}
                        <br><small class="text-muted">{{ error.request_url|truncatechars:30 }}</small>
                        {% endif %}
                    </div>
                    <div>
                        <small class="text-muted">{{ error.timestamp|timesince }} ago</small>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-4 text-success">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <p class="fw-semibold">🎉 No unresolved errors!</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
<!-- System Information & Device Breakdown -->
<div class="row mb-4">
    <div class="col-lg-8 mb-4">
        <div class="data-table">
            <div class="table-header">
                <i class="fas fa-server me-2"></i>
                System Information
            </div>
            <div class="table-content">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-semibold mb-3">
                            <i class="fas fa-memory me-2 text-primary"></i>
                            Memory Usage
                        </h6>
                        <p class="mb-1">Total: <span class="fw-semibold">{{ system_metrics.memory_total|filesizeformat }}</span></p>
                        <p class="mb-3">Used: <span class="fw-semibold">{{ system_metrics.memory_used|filesizeformat }}</span></p>

                        <h6 class="fw-semibold mb-3">
                            <i class="fas fa-hdd me-2 text-success"></i>
                            Storage Usage
                        </h6>
                        <p class="mb-1">Total: <span class="fw-semibold">{{ system_metrics.disk_total|filesizeformat }}</span></p>
                        <p class="mb-1">Used: <span class="fw-semibold">{{ system_metrics.disk_used|filesizeformat }}</span></p>
                        {% if system_metrics.db_size %}
                        <p class="mb-0">Database: <span class="fw-semibold">{{ system_metrics.db_size }}</span></p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        {% if visit_stats.device_breakdown %}
                        <h6 class="fw-semibold mb-3">
                            <i class="fas fa-mobile-alt me-2 text-info"></i>
                            Device Breakdown (7 days)
                        </h6>
                        {% for device in visit_stats.device_breakdown %}
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>
                                {% if device.device_type == 'mobile' %}
                                    <i class="fas fa-mobile-alt me-2"></i>
                                {% elif device.device_type == 'tablet' %}
                                    <i class="fas fa-tablet-alt me-2"></i>
                                {% elif device.device_type == 'desktop' %}
                                    <i class="fas fa-desktop me-2"></i>
                                {% else %}
                                    <i class="fas fa-robot me-2"></i>
                                {% endif %}
                                {{ device.device_type|title }}
                            </span>
                            <span class="badge-custom badge-info">{{ device.count }}</span>
                        </div>
                        {% endfor %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-4">
        <div class="data-table">
            <div class="table-header">
                <i class="fas fa-chart-pie me-2"></i>
                Quick Stats
            </div>
            <div class="table-content">
                <div class="text-center">
                    <div class="mb-3">
                        <h4 class="text-primary mb-1">{{ user_stats.new_users_7d }}</h4>
                        <small class="text-muted">New Users (7 days)</small>
                    </div>
                    <div class="mb-3">
                        <h4 class="text-success mb-1">{{ visit_stats.visits_7d|intcomma }}</h4>
                        <small class="text-muted">Page Views (7 days)</small>
                    </div>
                    <div class="mb-3">
                        <h4 class="{% if error_stats.errors_7d > 0 %}text-danger{% else %}text-success{% endif %} mb-1">{{ error_stats.errors_7d }}</h4>
                        <small class="text-muted">Errors (7 days)</small>
                    </div>
                    <div class="mt-4">
                        <small class="text-muted">
                            <i class="fas fa-sync-alt me-1"></i>
                            Auto-refresh in 5 minutes
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Auto-refresh Script -->
<script>
// Auto-refresh every 5 minutes
setTimeout(function() {
    location.reload();
}, 300000);

// Add loading states for better UX
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth animations to progress bars
    const progressBars = document.querySelectorAll('.progress-fill');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });

    // Initialize performance tracking status
    updatePerformanceStatus();

    // Update status every 10 seconds
    setInterval(updatePerformanceStatus, 10000);
});

// Performance Tracking Functions
function logToConsole(message) {
    const consoleOutput = document.getElementById('console-output');
    const timestamp = new Date().toLocaleTimeString();
    consoleOutput.innerHTML += `[${timestamp}] ${message}<br>`;
    consoleOutput.scrollTop = consoleOutput.scrollHeight;
}

function getCSRFToken() {
    // Try multiple methods to get CSRF token
    const formToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value;
    if (formToken) return formToken;

    const metaToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (metaToken) return metaToken;

    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrftoken') {
            return value;
        }
    }
    return '';
}

function updatePerformanceStatus() {
    const trackingEnabled = localStorage.getItem('enablePerformanceTracking') === 'true';
    const detailedEnabled = localStorage.getItem('enableDetailedMonitoring') === 'true';
    const csrfToken = getCSRFToken();
    const metricsCount = window.performanceMetrics ? Object.keys(window.performanceMetrics).length : 0;

    // Update status badges
    const trackingStatus = document.getElementById('tracking-status');
    const csrfStatus = document.getElementById('csrf-status');
    const metricsCountEl = document.getElementById('metrics-count');

    if (trackingStatus) {
        trackingStatus.textContent = trackingEnabled || detailedEnabled ? 'Enabled' : 'Disabled';
        trackingStatus.className = `badge ${trackingEnabled || detailedEnabled ? 'bg-success' : 'bg-secondary'}`;
    }

    if (csrfStatus) {
        csrfStatus.textContent = csrfToken ? 'Available' : 'Not Found';
        csrfStatus.className = `badge ${csrfToken ? 'bg-success' : 'bg-warning'}`;
    }

    if (metricsCountEl) {
        metricsCountEl.textContent = metricsCount;
        metricsCountEl.className = `badge ${metricsCount > 0 ? 'bg-info' : 'bg-secondary'}`;
    }
}

function enablePerformanceTracking() {
    localStorage.setItem('enablePerformanceTracking', 'true');
    localStorage.setItem('enableDetailedMonitoring', 'true');
    logToConsole('✅ Performance tracking enabled');
    updatePerformanceStatus();
}

function disablePerformanceTracking() {
    localStorage.removeItem('enablePerformanceTracking');
    localStorage.removeItem('enableDetailedMonitoring');
    logToConsole('❌ Performance tracking disabled');
    updatePerformanceStatus();
}

async function testPerformanceAPI() {
    logToConsole('🧪 Testing performance API...');

    const csrfToken = getCSRFToken();
    if (!csrfToken) {
        logToConsole('❌ CSRF token not found');
        return;
    }

    const testData = {
        loadTime: 1234,
        domContentLoaded: 567,
        url: window.location.href,
        timestamp: Date.now(),
        testMetric: 'admin-dashboard-test'
    };

    try {
        const response = await fetch('/api/analytics/performance/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify(testData)
        });

        if (response.ok) {
            const result = await response.json();
            logToConsole('✅ API test successful: ' + JSON.stringify(result));
        } else {
            logToConsole('❌ API test failed: ' + response.status + ' ' + response.statusText);
        }
    } catch (error) {
        logToConsole('❌ API test error: ' + error.message);
    }
}

function viewCurrentMetrics() {
    logToConsole('📊 Current performance metrics:');

    if (window.performanceMetrics && Object.keys(window.performanceMetrics).length > 0) {
        Object.entries(window.performanceMetrics).forEach(([key, value]) => {
            logToConsole(`  ${key}: ${JSON.stringify(value)}`);
        });
    } else {
        logToConsole('  No metrics available');
    }

    // Also show browser performance data
    if ('performance' in window) {
        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation) {
            logToConsole('📈 Browser performance:');
            logToConsole(`  Load time: ${(navigation.loadEventEnd - navigation.fetchStart).toFixed(2)}ms`);
            logToConsole(`  DOM ready: ${(navigation.domContentLoadedEventEnd - navigation.fetchStart).toFixed(2)}ms`);
        }
    }
}

function clearConsole() {
    document.getElementById('console-output').innerHTML = 'Console cleared...<br>';
}
</script>
{% endblock %}
