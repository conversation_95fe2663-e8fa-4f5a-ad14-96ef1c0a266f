subject_name,class_level_name,topic_title,question_text,question_type,correct_answer,explanation,difficulty,points,time_limit,choice_a,choice_b,choice_c,choice_d
ICT,Grade 7,Computer Hardware,What is the main circuit board in a computer called?,multiple_choice,b,The motherboard is the main circuit board that connects all components of a computer.,medium,10,60,Hard drive,Motherboard,RAM,CPU
ICT,Grade 7,Computer Hardware,Which component is considered the 'brain' of the computer?,multiple_choice,c,The CPU (Central Processing Unit) processes all instructions and is called the brain of the computer.,easy,10,45,RAM,Hard drive,CPU,Motherboard
ICT,Grade 7,Computer Hardware,What does RAM stand for?,multiple_choice,a,RAM stands for Random Access Memory which temporarily stores data for quick access.,medium,10,60,Random Access Memory,Read Access Memory,Rapid Access Memory,Remote Access Memory
ICT,Grade 7,Computer Hardware,Which storage device can hold the most data?,multiple_choice,d,Hard drives typically have the largest storage capacity among common storage devices.,medium,10,75,Floppy disk,CD,USB flash drive,Hard drive
ICT,Grade 7,Computer Hardware,What is the purpose of a graphics card?,multiple_choice,b,A graphics card processes visual data and displays images on the monitor.,medium,10,75,Process sound,Display images and graphics,Store data,Connect to internet
ICT,Grade 7,Computer Software,What is an operating system?,multiple_choice,a,An operating system manages computer hardware and provides a platform for other software to run.,medium,10,90,Software that manages hardware and runs other programs,A type of game,Internet browser,Word processor
ICT,Grade 7,Computer Software,Which of these is an example of system software?,multiple_choice,c,Windows is an operating system which is a type of system software.,medium,10,60,Microsoft Word,Google Chrome,Windows,Photoshop
ICT,Grade 7,Computer Software,What is application software?,multiple_choice,b,Application software consists of programs designed to help users accomplish specific tasks.,medium,10,90,Software that runs the computer,Programs that help users accomplish specific tasks,Hardware components,Internet connections
ICT,Grade 7,Computer Software,Which software is used for creating documents?,multiple_choice,a,Word processing software like Microsoft Word is used for creating and editing documents.,easy,10,60,Word processor,Web browser,Media player,Antivirus
ICT,Grade 7,Computer Software,What is a computer virus?,multiple_choice,d,A computer virus is malicious software that can damage files and spread to other computers.,medium,10,90,Helpful software,Hardware problem,Internet connection,Malicious software that damages files
ICT,Grade 7,Internet and Web,What does WWW stand for?,multiple_choice,c,WWW stands for World Wide Web which is a system of interlinked documents on the internet.,easy,10,60,World Wide Window,World Web Works,World Wide Web,World Wide Wireless
ICT,Grade 7,Internet and Web,What is a web browser?,multiple_choice,a,A web browser is software used to access and view websites on the internet.,easy,10,75,Software used to access websites,Type of website,Internet connection,Search engine
ICT,Grade 7,Internet and Web,Which of these is a search engine?,multiple_choice,b,Google is a popular search engine used to find information on the internet.,easy,10,45,Facebook,Google,Microsoft Word,Windows
ICT,Grade 7,Internet and Web,What is a URL?,multiple_choice,d,URL stands for Uniform Resource Locator and is the address of a website.,medium,10,75,User Registration Link,Universal Reading Language,Unified Resource Library,Uniform Resource Locator (website address)
ICT,Grade 7,Internet and Web,What does HTTP stand for?,multiple_choice,a,HTTP stands for HyperText Transfer Protocol which is used to transfer web pages.,hard,10,90,HyperText Transfer Protocol,High Technology Transfer Process,Home Text Transfer Program,Hardware Technology Transfer Protocol
ICT,Grade 7,Digital Communication,What is email?,multiple_choice,b,Email (electronic mail) is a method of sending messages electronically over the internet.,easy,10,60,Electronic game,Electronic mail,Electronic music,Electronic money
ICT,Grade 7,Digital Communication,What symbol is used in email addresses?,multiple_choice,c,The @ (at) symbol separates the username from the domain name in email addresses.,easy,10,30,#,&,@,%
ICT,Grade 7,Digital Communication,What is instant messaging?,multiple_choice,a,Instant messaging allows real-time text communication between users over the internet.,medium,10,90,Real-time text communication,Sending letters,Making phone calls,Video recording
ICT,Grade 7,Digital Communication,Which of these is a social media platform?,multiple_choice,d,Facebook is a popular social media platform for connecting and sharing with others.,easy,10,45,Microsoft Word,Google,Windows,Facebook
ICT,Grade 7,Digital Communication,What is video conferencing?,multiple_choice,b,Video conferencing allows people to have face-to-face meetings using computers and internet.,medium,10,90,Watching videos online,Face-to-face meetings using computers and internet,Recording videos,Playing video games
ICT,Grade 7,Digital Safety,What is cyberbullying?,multiple_choice,c,Cyberbullying is using technology to harass intimidate or harm others online.,medium,10,90,Playing online games,Making new friends online,Using technology to harass or harm others,Learning online
ICT,Grade 7,Digital Safety,What should you do if you receive a message from a stranger online?,multiple_choice,a,You should not respond to messages from strangers and tell a trusted adult.,easy,10,90,Don't respond and tell a trusted adult,Reply immediately,Give personal information,Meet them in person
ICT,Grade 7,Digital Safety,What is a strong password?,multiple_choice,d,A strong password combines letters numbers and symbols and is difficult to guess.,medium,10,120,Your name,Your birthday,123456,Combination of letters numbers and symbols
ICT,Grade 7,Digital Safety,What information should you never share online?,multiple_choice,b,Personal information like full name address phone number and school should never be shared online.,medium,10,120,Your favorite color,Personal information like name address phone number,Your favorite movie,Your hobby
ICT,Grade 7,Digital Safety,What is malware?,multiple_choice,a,Malware is malicious software designed to damage or gain unauthorized access to computers.,hard,10,120,Malicious software designed to damage computers,Helpful software,Internet browser,Email program
ICT,Grade 7,Word Processing,What is word processing software used for?,multiple_choice,b,Word processing software is used to create edit and format text documents.,easy,10,75,Playing games,Creating and editing text documents,Browsing the internet,Sending emails
ICT,Grade 7,Word Processing,Which key combination is used to copy text?,multiple_choice,c,Ctrl+C is the standard keyboard shortcut for copying selected text.,medium,10,60,Ctrl+A,Ctrl+V,Ctrl+C,Ctrl+X
ICT,Grade 7,Word Processing,What does Ctrl+V do?,multiple_choice,a,Ctrl+V pastes previously copied or cut text at the cursor position.,medium,10,60,Paste,Copy,Cut,Select all
ICT,Grade 7,Word Processing,How do you make text bold in most word processors?,multiple_choice,d,Ctrl+B is the common keyboard shortcut to make selected text bold.,medium,10,60,Ctrl+I,Ctrl+U,Ctrl+A,Ctrl+B
ICT,Grade 7,Word Processing,What is spell check?,multiple_choice,b,Spell check is a feature that identifies and suggests corrections for misspelled words.,medium,10,90,Grammar correction,Feature that identifies misspelled words,Font formatting,Page numbering
ICT,Grade 7,Spreadsheets,What is a spreadsheet?,multiple_choice,a,A spreadsheet is a program that organizes data in rows and columns for calculations and analysis.,medium,10,120,Program that organizes data in rows and columns,Word processing document,Presentation software,Web browser
ICT,Grade 7,Spreadsheets,What is a cell in a spreadsheet?,multiple_choice,c,A cell is the intersection of a row and column where data can be entered.,medium,10,75,Entire row,Entire column,Intersection of row and column,The whole spreadsheet
ICT,Grade 7,Spreadsheets,What symbol is used to start a formula in most spreadsheets?,multiple_choice,b,The equals sign (=) is used to begin formulas in spreadsheet applications.,medium,10,60,+,=,*,/
ICT,Grade 7,Spreadsheets,What does the SUM function do?,multiple_choice,d,The SUM function adds up all the numbers in a selected range of cells.,medium,10,75,Subtracts numbers,Multiplies numbers,Divides numbers,Adds up numbers
ICT,Grade 7,Spreadsheets,How are columns labeled in a spreadsheet?,multiple_choice,a,Columns are typically labeled with letters (A B C etc.) in spreadsheet applications.,easy,10,60,Letters (A B C),Numbers (1 2 3),Roman numerals,Symbols
ICT,Grade 7,Presentations,What is presentation software used for?,multiple_choice,b,Presentation software is used to create slideshows for displaying information to an audience.,medium,10,90,Writing documents,Creating slideshows for audiences,Browsing internet,Sending emails
ICT,Grade 7,Presentations,What is a slide in a presentation?,multiple_choice,c,A slide is a single page or screen in a presentation that contains text images or other content.,easy,10,75,The entire presentation,Animation effect,Single page or screen in presentation,Background image
ICT,Grade 7,Presentations,What makes a good presentation slide?,multiple_choice,a,Good slides have clear readable text appropriate images and are not overcrowded with information.,hard,10,120,Clear text appropriate images not overcrowded,Lots of text and many colors,Only text no images,Complex animations
ICT,Grade 7,Presentations,What is an animation in presentations?,multiple_choice,d,Animation is the movement or transition effect applied to text images or slides.,medium,10,90,Background color,Font style,Sound effect,Movement or transition effect
ICT,Grade 7,Presentations,How should you practice for a presentation?,multiple_choice,b,Practice by rehearsing your speech timing yourself and becoming familiar with your content.,medium,10,120,Just read from slides,Rehearse speech time yourself become familiar with content,Memorize everything word for word,Don't practice at all
ICT,Grade 7,Database Basics,What is a database?,multiple_choice,a,A database is an organized collection of related information stored electronically.,medium,10,90,Organized collection of related information,Single document,Web page,Email account
ICT,Grade 7,Database Basics,What is a record in a database?,multiple_choice,c,A record is a complete set of information about one item or person in a database.,medium,10,90,Single piece of data,Column header,Complete set of information about one item,Database name
ICT,Grade 7,Database Basics,What is a field in a database?,multiple_choice,b,A field is a single piece of information within a record such as name or age.,medium,10,90,Entire database,Single piece of information within a record,Complete record,Database table
ICT,Grade 7,Database Basics,Why are databases useful?,multiple_choice,d,Databases help organize store search and retrieve large amounts of information efficiently.,hard,10,120,Only for storing numbers,Only for storing text,Only for storing images,Organize store search retrieve information efficiently
ICT,Grade 7,Database Basics,What is sorting in a database?,multiple_choice,a,Sorting arranges records in a specific order such as alphabetical or numerical.,medium,10,90,Arranging records in specific order,Deleting records,Adding new records,Copying records
ICT,Grade 7,Programming Concepts,What is an algorithm?,multiple_choice,b,An algorithm is a step-by-step procedure for solving a problem or completing a task.,medium,10,90,Computer program,Step-by-step procedure for solving problems,Type of software,Hardware component
ICT,Grade 7,Programming Concepts,What is a flowchart?,multiple_choice,c,A flowchart is a visual diagram that shows the steps and decisions in a process or algorithm.,medium,10,120,Type of computer,Programming language,Visual diagram showing steps and decisions in process,Database table
ICT,Grade 7,Programming Concepts,What does debugging mean in programming?,multiple_choice,a,Debugging is the process of finding and fixing errors in computer programs.,hard,10,90,Finding and fixing errors in programs,Writing new programs,Running programs,Deleting programs
ICT,Grade 7,Programming Concepts,What is a loop in programming?,multiple_choice,d,A loop is a programming structure that repeats a set of instructions multiple times.,hard,10,120,Single instruction,Error in program,End of program,Structure that repeats instructions multiple times
ICT,Grade 7,Programming Concepts,What is input in programming?,multiple_choice,b,Input is data or information that is entered into a program by the user.,medium,10,75,Program output,Data entered into program by user,Programming language,Computer hardware
ICT,Grade 7,File Management,What is a file?,multiple_choice,a,A file is a collection of data or information stored with a specific name on a computer.,easy,10,75,Collection of data stored with specific name,Computer program,Hardware component,Internet connection
ICT,Grade 7,File Management,What is a folder?,multiple_choice,c,A folder is a container used to organize and group related files together.,easy,10,60,Single file,Computer program,Container used to organize files,Hardware device
ICT,Grade 7,File Management,What is a file extension?,multiple_choice,b,A file extension is the part after the dot in a filename that indicates the file type.,medium,10,90,File name,Part after dot indicating file type,File size,File location
ICT,Grade 7,File Management,Which file extension indicates a text document?,multiple_choice,d,The .txt extension indicates a plain text document file.,medium,10,60,.jpg,.mp3,.exe,.txt
ICT,Grade 7,File Management,What does it mean to backup files?,multiple_choice,a,Backing up files means creating copies of important files to prevent data loss.,medium,10,90,Creating copies to prevent data loss,Deleting old files,Moving files to different folders,Renaming files
ICT,Grade 7,Computer Networks,What is a computer network?,multiple_choice,b,A computer network is a group of connected computers that can share resources and information.,medium,10,120,Single computer,Group of connected computers sharing resources,Internet browser,Software program
ICT,Grade 7,Computer Networks,What is the internet?,multiple_choice,c,The internet is a global network of interconnected computers that share information worldwide.,medium,10,120,Single website,Local computer network,Global network of interconnected computers,Email system
ICT,Grade 7,Computer Networks,What does Wi-Fi allow you to do?,multiple_choice,a,Wi-Fi allows devices to connect to networks and the internet wirelessly without cables.,easy,10,90,Connect to networks wirelessly,Only send emails,Only browse websites,Only play games
ICT,Grade 7,Computer Networks,What is bandwidth?,multiple_choice,d,Bandwidth is the amount of data that can be transmitted over a network in a given time.,hard,10,120,Physical size of network,Number of computers,Network security level,Amount of data transmitted over network in given time
ICT,Grade 7,Computer Networks,What is a router?,multiple_choice,b,A router is a device that connects different networks and directs data between them.,hard,10,120,Type of computer,Device that connects networks and directs data,Software program,Storage device
ICT,Grade 7,Digital Ethics,What is digital citizenship?,multiple_choice,c,Digital citizenship is responsible and ethical behavior when using technology and the internet.,medium,10,120,Using computers only,Avoiding technology,Responsible and ethical behavior using technology,Only using social media
ICT,Grade 7,Digital Ethics,What is copyright?,multiple_choice,a,Copyright is legal protection for original creative works preventing unauthorized copying or use.,hard,10,120,Legal protection for original creative works,Permission to copy anything,Free use of all content,Only for books
ICT,Grade 7,Digital Ethics,What is plagiarism?,multiple_choice,b,Plagiarism is using someone else's work or ideas without giving proper credit to the original author.,medium,10,120,Creating original work,Using others' work without giving credit,Sharing information freely,Collaborating with others
ICT,Grade 7,Digital Ethics,What is fair use?,multiple_choice,d,Fair use allows limited use of copyrighted material for education research or criticism without permission.,hard,10,150,Copying anything freely,Using only your own work,Avoiding all copyrighted material,Limited use of copyrighted material for education research criticism
ICT,Grade 7,Digital Ethics,Why is it important to respect others online?,multiple_choice,a,Respecting others online creates a positive environment and prevents harm just like in real life.,medium,10,120,Creates positive environment prevents harm,Only to avoid getting in trouble,Only for making friends,Only for business purposes
ICT,Grade 7,Emerging Technologies,What is artificial intelligence (AI)?,multiple_choice,b,AI is technology that enables machines to perform tasks that typically require human intelligence.,hard,10,150,Robot that looks human,Technology enabling machines to perform tasks requiring human intelligence,Only computer games,Internet connection
ICT,Grade 7,Emerging Technologies,What is virtual reality (VR)?,multiple_choice,c,VR is technology that creates immersive simulated environments that users can interact with.,hard,10,120,Real-world environment,Computer screen,Technology creating immersive simulated environments,Social media platform
ICT,Grade 7,Emerging Technologies,What are smartphones?,multiple_choice,a,Smartphones are mobile devices that combine phone capabilities with computer functions and internet access.,easy,10,120,Mobile devices combining phone computer functions internet access,Only for making calls,Only for texting,Only for games
ICT,Grade 7,Emerging Technologies,What is cloud computing?,multiple_choice,d,Cloud computing allows access to computing resources and data storage over the internet.,hard,10,150,Weather prediction,Physical storage device,Local computer network,Access to computing resources and storage over internet
ICT,Grade 7,Emerging Technologies,How is technology changing education?,multiple_choice,b,Technology enables online learning interactive content global access and personalized education experiences.,hard,10,150,Making education harder,Enabling online learning interactive content global access personalized experiences,Replacing all teachers,Only for wealthy students
ICT,Grade 7,Problem Solving with ICT,How can computers help solve problems?,multiple_choice,a,Computers help by processing large amounts of data quickly performing calculations and running simulations.,hard,10,150,Processing data quickly performing calculations running simulations,Only for entertainment,Only for communication,Only for storing information
ICT,Grade 7,Problem Solving with ICT,What is computational thinking?,multiple_choice,c,Computational thinking is breaking down complex problems into smaller manageable parts to solve them systematically.,hard,10,180,Only thinking about computers,Using computers for everything,Breaking complex problems into smaller manageable parts,Avoiding technology
ICT,Grade 7,Problem Solving with ICT,What steps are involved in problem-solving with ICT?,multiple_choice,b,Steps include identifying the problem analyzing requirements designing solutions implementing and testing them.,hard,10,180,Only using computers,Identifying problem analyzing requirements designing implementing testing solutions,Avoiding the problem,Asking others to solve it
ICT,Grade 7,Problem Solving with ICT,How can spreadsheets help solve mathematical problems?,multiple_choice,d,Spreadsheets can perform calculations create graphs analyze data and model different scenarios automatically.,hard,10,150,Only for storing numbers,Only for making lists,Only for simple addition,Perform calculations create graphs analyze data model scenarios
ICT,Grade 7,Problem Solving with ICT,What is simulation in ICT?,multiple_choice,a,Simulation uses computer models to imitate real-world processes and test different scenarios safely.,hard,10,150,Using computer models to imitate real-world processes test scenarios,Playing computer games,Copying real situations exactly,Only for entertainment
ICT,Basic 7,Computer Hardware,Which component is known as the "brain" of the computer?,multiple_choice,b,The Central Processing Unit (CPU) performs most of the processing inside a computer.,easy,5,30,RAM,CPU,Hard Disk,Monitor
ICT,Basic 7,Computer Hardware,Which of the following is an input device?,multiple_choice,a,A keyboard is used to enter data into the computer.,easy,5,30,Keyboard,Monitor,Printer,Speaker
ICT,Basic 7,Computer Hardware,What is the primary function of a monitor?,multiple_choice,c,A monitor displays visual output from the computer.,easy,5,30,To type text,To print documents,To display output,To store data
ICT,Basic 7,Computer Hardware,Which of these is a storage device?,multiple_choice,d,A USB flash drive is used for portable data storage.,easy,5,30,Mouse,Speaker,Microphone,USB flash drive
ICT,Basic 7,Computer Hardware,RAM stands for?,multiple_choice,b,RAM stands for Random Access Memory, which is volatile memory used for temporary data storage.,medium,10,45,Read Access Memory,Random Access Memory,Real Access Memory,Rapid Access Method
ICT,Basic 7,Computer Hardware,Which port is commonly used to connect a keyboard and mouse to a computer?,multiple_choice,c,USB ports are universal and widely used for peripherals like keyboards and mice.,easy,5,30,HDMI,Ethernet,USB,VGA
ICT,Basic 7,Computer Hardware,What is the function of a printer?,multiple_choice,a,A printer produces hard copies of documents or images.,easy,5,30,To produce hard copies,To display images,To input sound,To store files
ICT,Basic 7,Computer Hardware,Which hardware component holds all other components together and allows them to communicate?,multiple_choice,d,The motherboard is the main circuit board that connects all hardware components.,medium,10,45,CPU,RAM,Hard Disk,Motherboard
ICT,Basic 7,Computer Hardware,What type of memory is volatile, meaning its contents are lost when the computer is turned off?,multiple_choice,c,RAM (Random Access Memory) is volatile memory.,medium,10,45,ROM,Hard Disk,RAM,SSD
ICT,Basic 7,Computer Hardware,Which of the following is an output device?,multiple_choice,b,Speakers produce audio output.,easy,5,30,Scanner,Speakers,Webcam,Joystick
ICT,Basic 7,Computer Hardware,What does ROM stand for?,multiple_choice,a,ROM stands for Read-Only Memory, which stores permanent instructions.,medium,10,45,Read-Only Memory,Random-Only Memory,Run-Only Module,Ready Operational Memory
ICT,Basic 7,Computer Hardware,Which device is used to scan documents and convert them into digital files?,multiple_choice,c,A scanner is an input device that converts physical documents into digital images.,easy,5,30,Printer,Monitor,Scanner,Keyboard
ICT,Basic 7,Computer Hardware,What is the main purpose of a power supply unit (PSU) in a computer?,multiple_choice,d,The PSU converts AC power from the wall outlet into DC power for computer components.,medium,10,60,To store data,To process data,To display output,To provide power to components
ICT,Basic 7,Computer Hardware,Which component stores data permanently, even when the computer is turned off?,multiple_choice,a,The Hard Disk Drive (HDD) or Solid State Drive (SSD) stores data non-volatilely.,easy,5,30,Hard Disk Drive,RAM,CPU,Monitor
ICT,Basic 7,Computer Hardware,Which peripheral device is essential for video conferencing?,multiple_choice,b,A webcam captures video input for video calls.,medium,10,45,Printer,Webcam,Scanner,Plotter
ICT,Basic 7,Computer Software,What is a set of instructions that tells the computer what to do?,multiple_choice,b,Software is a collection of programs and data that operate a computer.,easy,5,30,Hardware,Software,Network,Data
ICT,Basic 7,Computer Software,Which type of software controls the computer's basic functions and allows other programs to run?,multiple_choice,a,Operating System (OS) software manages computer hardware and software resources.,medium,10,45,Operating System,Application Software,Utility Software,Programming Software
ICT,Basic 7,Computer Software,Which of these is an example of an operating system?,multiple_choice,c,Windows is a widely used operating system.,easy,5,30,Microsoft Word,Google Chrome,Microsoft Windows,Adobe Photoshop
ICT,Basic 7,Computer Software,Software used for creating and editing text documents is called a?,multiple_choice,d,Word processing software is used for text document creation.,easy,5,30,Spreadsheet program,Presentation program,Database program,Word processor
ICT,Basic 7,Computer Software,Which software is used for Browse the internet?,multiple_choice,a,A web browser is essential for accessing the internet.,easy,5,30,Web browser,Antivirus software,Word processor,Spreadsheet software
ICT,Basic 7,Computer Software,What is the primary function of antivirus software?,multiple_choice,c,Antivirus software detects and removes malicious software.,medium,10,45,To create documents,To browse the internet,To protect against malware,To play games
ICT,Basic 7,Computer Software,Which of the following is an example of application software?,multiple_choice,b,Microsoft Excel is an application used for spreadsheets.,easy,5,30,Linux,macOS,Microsoft Excel,Ubuntu
ICT,Basic 7,Computer Software,Programs designed to perform specific tasks for users are known as?,multiple_choice,d,Application software caters to specific user tasks.,medium,10,45,System software,Utility software,Programming languages,Application software
ICT,Basic 7,Computer Software,What type of software helps to maintain and optimize the computer's performance?,multiple_choice,a,Utility software includes tools for disk defragmentation, virus scanning, etc.,medium,10,45,Utility software,Operating system,Application software,Firmware
ICT,Basic 7,Computer Software,Which of these is NOT an operating system?,multiple_choice,c,Adobe Acrobat Reader is an application for viewing PDFs, not an OS.,easy,5,30,Android,iOS,Adobe Acrobat Reader,Fedora
ICT,Basic 7,Computer Software,A software license grants permission to?,multiple_choice,b,A software license defines the terms under which software can be used, copied, or distributed.,hard,15,60,Own the software outright,Use the software under specific terms,Sell copies of the software illegally,Modify the source code without permission
ICT,Basic 7,Computer Software,What is firmware?,multiple_choice,d,Firmware is a specialized type of software embedded in hardware devices.,hard,15,75,Software for playing games,Software for writing documents,Software that controls the internet,Software embedded in hardware devices
ICT,Basic 7,Computer Software,Which program is typically used to create presentations with slides?,multiple_choice,b,Microsoft PowerPoint is a popular presentation software.,easy,5,30,Microsoft Word,Microsoft PowerPoint,Microsoft Excel,Microsoft Access
ICT,Basic 7,Computer Software,The process of installing software means?,multiple_choice,a,Installation involves putting software onto a computer's storage so it can be executed.,medium,10,45,Copying software files to the computer's hard drive,Deleting software files from the computer,Turning off the computer,Changing the computer's hardware
ICT,Basic 7,Computer Software,Which software allows users to create and manage large collections of data?,multiple_choice,c,Database management systems (DBMS) are used for this purpose.,medium,10,45,Word processor,Spreadsheet,Database software,Presentation software
ICT,Basic 7,Internet and Web,What is the worldwide collection of interconnected computer networks called?,multiple_choice,a,The Internet is a global network of computers.,easy,5,30,Internet,Intranet,Extranet,Web
ICT,Basic 7,Internet and Web,What does WWW stand for?,multiple_choice,b,WWW stands for World Wide Web, a system of interconnected documents accessible via the Internet.,easy,5,30,World Wide Wait,World Wide Web,Web Wide World,Wide World Web
ICT,Basic 7,Internet and Web,Which of these is a search engine?,multiple_choice,c,Google is a popular search engine.,easy,5,30,Facebook,YouTube,Google,WhatsApp
ICT,Basic 7,Internet and Web,What is a website?,multiple_choice,d,A website is a collection of related web pages, images, videos, etc., accessible via a web address.,easy,5,30,A computer program,A type of email,A mobile app,A collection of related web pages
ICT,Basic 7,Internet and Web,What is the unique address that identifies a website on the internet?,multiple_choice,a,A URL (Uniform Resource Locator) is the web address.,medium,10,45,URL,ISP,WWW,HTTP
ICT,Basic 7,Internet and Web,What does ISP stand for?,multiple_choice,b,ISP stands for Internet Service Provider, a company that provides internet access.,medium,10,45,Internet Server Protocol,Internet Service Provider,Internal System Program,Information Service Point
ICT,Basic 7,Internet and Web,Which protocol is used for securely sending and receiving email?,multiple_choice,a,SMTP (Simple Mail Transfer Protocol) is for sending, and POP3/IMAP are for receiving. SMTP is a common correct choice for sending.,hard,15,60,SMTP,HTTP,FTP,TCP/IP
ICT,Basic 7,Internet and Web,What is the main purpose of an email address?,multiple_choice,c,An email address is used to send and receive electronic messages.,easy,5,30,To open websites,To make phone calls,To send and receive electronic messages,To download software
ICT,Basic 7,Internet and Web,Which of these is a common web browser?,multiple_choice,d,Mozilla Firefox is a popular web browser.,easy,5,30,Microsoft Word,Google Drive,Adobe Photoshop,Mozilla Firefox
ICT,Basic 7,Internet and Web,What is "phishing" in the context of internet safety?,multiple_choice,b,Phishing is a deceptive attempt to obtain sensitive information like usernames, passwords, and credit card details.,medium,10,60,Searching for information online,Sending spam emails,Attempting to trick users into revealing personal information,Creating a personal website
ICT,Basic 7,Internet and Web,What is the full meaning of HTTP?,multiple_choice,a,HTTP stands for Hypertext Transfer Protocol, used for data communication on the Web.,medium,10,45,Hypertext Transfer Protocol,Hyper Transfer Text Protocol,High Technology Transfer Protocol,Home Text Transfer Process
ICT,Basic 7,Internet and Web,Which term refers to illegally downloading copyrighted material from the internet?,multiple_choice,d,Piracy is the unauthorized use or reproduction of copyrighted material.,medium,10,45,Streaming,Blogging,Uploading,Piracy
ICT,Basic 7,Internet and Web,What is cloud computing?,multiple_choice,c,Cloud computing involves delivering computing services—including servers, storage, databases, networking, software, analytics, and intelligence—over the Internet ("the cloud").,hard,15,75,Storing data on your local computer,Using a single large server,Storing and accessing data and programs over the Internet,Sending data via email
ICT,Basic 7,Internet and Web,Which symbol is used to separate the username from the domain name in an email address?,multiple_choice,b,The "@" symbol is used (e.g., <EMAIL>).,easy,5,30,.,/,@,#
ICT,Basic 7,Internet and Web,What is a firewall used for in internet security?,multiple_choice,a,A firewall monitors and controls incoming and outgoing network traffic, protecting against unauthorized access.,medium,10,60,To prevent unauthorized access to a network,To speed up internet connection,To send emails,To create websites
ICT,Basic 7,Digital Communication,Which of these is a form of digital communication?,multiple_choice,d,Sending an SMS text message is a common form of digital communication.,easy,5,30,Talking face-to-face,Sending a letter by post,Using a landline phone,Sending an SMS text message
ICT,Basic 7,Digital Communication,What does SMS stand for?,multiple_choice,b,SMS stands for Short Message Service, used for text messaging.,medium,10,45,Simple Mail System,Short Message Service,System Message Sending,Secure Mobile System
ICT,Basic 7,Digital Communication,Which app is commonly used for instant messaging and voice/video calls over the internet?,multiple_choice,c,WhatsApp is a very popular app for these purposes.,easy,5,30,Microsoft Word,Google Chrome,WhatsApp,Adobe Photoshop
ICT,Basic 7,Digital Communication,What is a blog?,multiple_choice,a,A blog is an online journal or diary regularly updated with new content.,medium,10,45,An online diary or journal,A type of computer game,A hardware device,A search engine
ICT,Basic 7,Digital Communication,Which of the following is a disadvantage of digital communication?,multiple_choice,b,Misinterpretation of tone can occur without visual or auditory cues.,medium,10,60,Speed of communication,Misinterpretation of tone,Cost-effectiveness,Global reach
ICT,Basic 7,Digital Communication,What is a podcast?,multiple_choice,c,A podcast is a digital audio file made available on the Internet for downloading to a computer or mobile device.,hard,15,75,A type of video game,A live online meeting,A digital audio broadcast,A website for sharing photos
ICT,Basic 7,Digital Communication,What does VoIP stand for?,multiple_choice,a,VoIP stands for Voice over Internet Protocol, allowing voice communication over the internet.,medium,10,45,Voice over Internet Protocol,Video over Internet Project,Virtual Office Internet Protocol,Voice Operating Internet Program
ICT,Basic 7,Digital Communication,Which social media platform is primarily used for sharing photos and short videos?,multiple_choice,d,Instagram is known for photo and video sharing.,easy,5,30,Twitter,LinkedIn,Facebook,Instagram
ICT,Basic 7,Digital Communication,What is "netiquette"?,multiple_choice,a,Netiquette refers to the polite and appropriate behavior when communicating online.,medium,10,60,Rules of good behavior on the internet,A type of internet connection,A software program,A way to speed up downloads
ICT,Basic 7,Digital Communication,Which of these is a synchronous form of digital communication (real-time interaction)?,multiple_choice,b,Video conferencing allows real-time communication.,hard,15,75,Email,Video conferencing,Blogging,Text messaging (SMS)
ICT,Basic 7,Digital Communication,What is the purpose of an online forum?,multiple_choice,c,Online forums facilitate discussions among users on various topics.,medium,10,45,To send private messages,To share large files,To host online discussions,To make video calls
ICT,Basic 7,Digital Communication,Which term refers to unsolicited commercial email?,multiple_choice,a,Spam is unsolicited junk email.,easy,5,30,Spam,Phishing,Malware,Virus
ICT,Basic 7,Digital Communication,What is a common benefit of using digital communication for education?,multiple_choice,d,Digital communication facilitates remote learning and access to educational resources.,medium,10,60,Increased cost,Limited reach,Reduced personal interaction,Access to remote learning
ICT,Basic 7,Digital Communication,What is cyberbullying?,multiple_choice,b,Cyberbullying is bullying that takes place using electronic technology.,medium,10,60,Playing online games,Sending friendly messages,Bullying using electronic technology,Sharing funny videos online
ICT,Basic 7,Digital Communication,Which communication technology relies on satellites to transmit signals globally?,multiple_choice,a,Satellite communication uses satellites for global transmission.,medium,10,45,Satellite communication,Fiber optic cables,Bluetooth,Infrared
Science,Basic 7,Digital Safety,"What is the main purpose of a password?",multiple_choice,a,"Passwords protect accounts from unauthorized access.",easy,10,30,To protect your accounts from unauthorized access,To make your computer run faster,To download new software,To connect to the internet
Science,Basic 7,Digital Safety,"Which of these is a strong password?",multiple_choice,c,"Strong passwords combine uppercase and lowercase letters, numbers, and symbols.",easy,10,30,password123,********,P@ssW0rd!,yourname
Science,Basic 7,Digital Safety,"What should you do if you receive a suspicious email asking for your personal information?",multiple_choice,b,"Suspicious emails should be deleted without clicking links or replying.",easy,10,30,Reply asking who they are,Delete it without opening any links,Click on the links to see what happens,Forward it to all your friends
Science,Basic 7,Digital Safety,"What is 'phishing'?",multiple_choice,c,"Phishing is a deceptive attempt to obtain sensitive information by pretending to be a trustworthy entity.",medium,10,45,A type of computer game,A way to send emails quickly,A way to trick people into giving personal information,A new operating system
Science,Basic 7,Digital Safety,"Why is it important to use antivirus software?",multiple_choice,d,"Antivirus software helps detect and remove malicious software.",easy,10,30,To speed up your internet connection,To help you type faster,To make your screen brighter,To protect your computer from viruses
Science,Basic 7,Digital Safety,"Which of the following is an example of personal information you should protect online?",multiple_choice,a,"Your full name is personal information that can be used for identification.",easy,10,30,Your full name,Your favorite color,Your shoe size,Your pet's name
Science,Basic 7,Digital Safety,"What does 'cyberbullying' mean?",multiple_choice,b,"Cyberbullying is bullying that takes place over digital devices.",easy,10,30,Playing online games with friends,Harming or harassing someone using digital technology,Sharing funny videos online,Learning about computers
Science,Basic 7,Digital Safety,"Before clicking on a link in an email, you should:",multiple_choice,c,"Hovering over a link reveals the actual URL, helping to identify malicious links.",medium,10,45,Click it quickly,Copy and paste it into a document,Hover your mouse over it to see the actual link,Send it to a friend first
Science,Basic 7,Digital Safety,"What is 'spam' email?",multiple_choice,a,"Spam refers to unsolicited and often irrelevant or inappropriate messages sent over the Internet.",easy,10,30,Unwanted junk emails,Emails from your friends,Emails from your teacher,Emails with attachments only
Science,Basic 7,Digital Safety,"Why should you be careful about what you post online?",multiple_choice,d,"Once something is online, it can be difficult to remove and can affect your future.",medium,10,45,It takes up too much space,Your friends might not like it,It makes your internet slow,It can stay there forever and be seen by anyone
Science,Basic 7,Digital Safety,"What is a 'firewall' in computer security?",multiple_choice,b,"A firewall acts as a barrier, controlling incoming and outgoing network traffic.",medium,10,45,A program that deletes files,A security system that monitors and controls network traffic,A type of internet browser,A device that cools your computer
Science,Basic 7,Digital Safety,"Updating your software regularly helps improve digital safety by:",multiple_choice,a,"Software updates often include security patches to fix vulnerabilities.",medium,10,45,Fixing security vulnerabilities,Changing your password automatically,Making your computer run faster,Downloading new games
Science,Basic 7,Digital Safety,"What is the best way to keep your personal information private when using social media?",multiple_choice,d,"Adjusting privacy settings ensures only approved individuals can see your content.",easy,10,30,Share everything with everyone,Post your address online,Use a very easy password,Adjust your privacy settings
Science,Basic 7,Digital Safety,"What does 'URL' stand for?",multiple_choice,c,"URL stands for Uniform Resource Locator, an address on the internet.",easy,10,30,Universal Remote Link,User Resource Locator,Uniform Resource Locator,Unique Response Logic
Science,Basic 7,Digital Safety,"Which symbol indicates a secure website (meaning your connection is encrypted)?",multiple_choice,b,"The padlock icon and 'https://' indicate a secure, encrypted connection.",easy,10,30,A red exclamation mark,A padlock icon,A star,A question mark
Science,Basic 7,Word Processing,"In a word processor, what is the 'Save As' command used for?",multiple_choice,a,"'Save As' creates a new file or saves an existing file with a new name/location.",easy,10,30,To save a document with a new name or location,To close a document,To print a document,To open an existing document
Science,Basic 7,Word Processing,"Which of the following is NOT a common feature of word processing software?",multiple_choice,d,"Performing complex calculations is typically done in spreadsheet software.",easy,10,30,Spell check,Font formatting,Copy and paste,Performing complex calculations
Science,Basic 7,Word Processing,"To make text bold in a word processor, you would typically use which button?",multiple_choice,c,"The 'B' button (often accompanied by 'I' for italics and 'U' for underline) makes text bold.",easy,10,30,I,U,B,S
Science,Basic 7,Word Processing,"What is the purpose of 'Undo' button in a word processor?",multiple_choice,a,"'Undo' reverses the last action performed.",easy,10,30,To reverse the last action,To repeat the last action,To save the document,To print the document
Science,Basic 7,Word Processing,"Which tab in Microsoft Word typically contains the 'Font' and 'Paragraph' groups?",multiple_choice,b,"The Home tab contains the most frequently used formatting options.",medium,10,45,Insert,Home,Page Layout,References
Science,Basic 7,Word Processing,"To align text to the center of the page, you would use which alignment option?",multiple_choice,c,"Center alignment positions text equally between the left and right margins.",easy,10,30,Align Left,Align Right,Center,Justify
Science,Basic 7,Word Processing,"What is a 'header' in a word document?",multiple_choice,b,"A header is text or graphics that appears at the top of every page or section.",medium,10,45,Text at the bottom of every page,Text at the top of every page,The title of the document,A section break
Science,Basic 7,Word Processing,"To add a new page to your document, you would typically insert a:",multiple_choice,a,"A page break forces content to start on a new page.",easy,10,30,Page break,Line break,Section break,Column break
Science,Basic 7,Word Processing,"Which shortcut key is commonly used to 'Copy' selected text?",multiple_choice,b,"Ctrl+C (or Cmd+C on Mac) is the standard shortcut for copying.",easy,10,30,Ctrl+X,Ctrl+C,Ctrl+V,Ctrl+Z
Science,Basic 7,Word Processing,"What does 'Spell Check' do in a word processor?",multiple_choice,d,"Spell check identifies and suggests corrections for misspelled words.",easy,10,30,Corrects grammar automatically,Translates text,Adds pictures to your document,Checks for misspelled words
Science,Basic 7,Word Processing,"To apply a predefined set of formatting (like font, size, and color) to text, you would use a:",multiple_choice,c,"Styles apply consistent formatting across a document.",medium,10,45,Template,Theme,Style,Macro
Science,Basic 7,Word Processing,"Which view in a word processor is best for seeing how your document will look when printed?",multiple_choice,a,"Print Layout view shows page breaks, margins, and headers/footers.",medium,10,45,Print Layout View,Web Layout View,Outline View,Draft View
Science,Basic 7,Word Processing,"The vertical blinking line that indicates where your next typed character will appear is called the:",multiple_choice,b,"The insertion point is where text will be added or deleted.",easy,10,30,Cursor,Insertion point,Pointer,Marker
Science,Basic 7,Word Processing,"Which file extension is commonly used for Microsoft Word documents?",multiple_choice,d,".docx is the standard file format for modern Word documents.",easy,10,30,.txt,.pdf,.xlsx,.docx
Science,Basic 7,Word Processing,"What is the purpose of 'Word Wrap'?",multiple_choice,a,"Word wrap automatically moves text to the next line when it reaches the end of the current line.",medium,10,45,To automatically move text to the next line,To break words into syllables,To wrap text around an image,To count the number of words
Science,Basic 7,Spreadsheets,"In a spreadsheet, the intersection of a row and a column is called a:",multiple_choice,b,"A cell is the basic unit for storing data in a spreadsheet.",easy,10,30,Row,Cell,Column,Range
Science,Basic 7,Spreadsheets,"Which of these is a common use for spreadsheet software?",multiple_choice,a,"Spreadsheets are excellent for organizing and analyzing numerical data.",easy,10,30,Organizing and calculating numerical data,Writing letters,Creating presentations,Editing photos
Science,Basic 7,Spreadsheets,"A formula in a spreadsheet always begins with which symbol?",multiple_choice,c,"The equals sign (=) tells the spreadsheet that a calculation is to follow.",easy,10,30,+, -, =, *
Science,Basic 7,Spreadsheets,"In Excel, what does 'SUM(A1:A5)' calculate?",multiple_choice,b,"SUM adds all the values in the specified range of cells.",easy,10,30,The difference between A1 and A5,The total of values from A1 to A5,The average of A1 to A5,The maximum value in A1 to A5
Science,Basic 7,Spreadsheets,"What is a 'worksheet' in a spreadsheet program?",multiple_choice,d,"A worksheet is a single page within a spreadsheet file, consisting of rows and columns.",easy,10,30,The entire file,A type of chart,A row of data,A single sheet within a workbook
Science,Basic 7,Spreadsheets,"Which of the following is an example of a cell address?",multiple_choice,a,"A cell address combines the column letter and row number.",easy,10,30,B7,Row 5,Column C,MyData
Science,Basic 7,Spreadsheets,"To sort data in a spreadsheet, you would typically use which feature?",multiple_choice,b,"Sorting arranges data in ascending or descending order.",easy,10,30,Filter,Sort,Conditional Formatting,Merge Cells
Science,Basic 7,Spreadsheets,"What is the purpose of a 'chart' or 'graph' in a spreadsheet?",multiple_choice,c,"Charts visualize data, making trends and comparisons easier to understand.",easy,10,30,To perform calculations,To enter text,To visually represent data,To save the spreadsheet
Science,Basic 7,Spreadsheets,"To calculate the average of a range of numbers in a spreadsheet, you would use which function?",multiple_choice,d,"AVERAGE is the standard function for calculating the mean.",medium,10,45,SUM,MAX,MIN,AVERAGE
Science,Basic 7,Spreadsheets,"What is 'autofill' in a spreadsheet?",multiple_choice,a,"Autofill automatically extends a data series or copies a formula to adjacent cells.",medium,10,45,Automatically filling a series of data or formulas,Automatically checking spelling,Automatically saving the file,Automatically creating charts
Science,Basic 7,Spreadsheets,"Which operation would you use to combine two or more cells into a single larger cell?",multiple_choice,b,"Merging cells combines them into one, often used for titles.",medium,10,45,Split Cells,Merge Cells,Format Cells,Align Cells
Science,Basic 7,Spreadsheets,"What does the '$' symbol (e.g., $A$1) indicate in a spreadsheet formula?",multiple_choice,a,"The dollar sign creates an absolute cell reference, meaning the reference does not change when copied.",hard,10,75,An absolute cell reference,A relative cell reference,A text string,A currency format
Science,Basic 7,Spreadsheets,"The file extension for a Microsoft Excel workbook is usually:",multiple_choice,b,".xlsx is the standard file format for modern Excel workbooks.",easy,10,30,.doc,.xlsx,.pptx,.txt
Science,Basic 7,Spreadsheets,"What is a 'function' in a spreadsheet?",multiple_choice,d,"Functions are predefined formulas that perform specific calculations.",easy,10,30,A cell's address,A type of chart,A number entered into a cell,A predefined formula
Science,Basic 7,Spreadsheets,"To select an entire column in a spreadsheet, you would click on the:",multiple_choice,c,"Clicking the column header (the letter) selects the entire column.",easy,10,30,Row number,Cell,Column letter,Formula bar
Science,Basic 7,Presentations,"What is the main purpose of presentation software?",multiple_choice,a,"Presentation software helps create visual aids for delivering information to an audience.",easy,10,30,To create visual aids for a presentation,To write letters,To perform calculations,To browse the internet
Science,Basic 7,Presentations,"Each individual page in a presentation is called a:",multiple_choice,b,"A slide is a single page in a presentation.",easy,10,30,Document,Slide,Worksheet,Page
Science,Basic 7,Presentations,"To add text, images, or shapes to a slide, you use:",multiple_choice,c,"Placeholders or text boxes are used to insert content onto a slide.",easy,10,30,Formulas,Functions,Text boxes/placeholders,Macros
Science,Basic 7,Presentations,"What is a 'theme' in presentation software?",multiple_choice,d,"A theme provides a consistent design, including colors, fonts, and backgrounds.",medium,10,45,A specific image,A type of animation,A sound effect,A predefined design template
Science,Basic 7,Presentations,"Which feature allows you to control how one slide moves to the next during a presentation?",multiple_choice,a,"Transitions are visual effects that occur when moving from one slide to the next.",easy,10,30,Transitions,Animations,Slide Master,Hyperlinks
Science,Basic 7,Presentations,"To rearrange the order of your slides, you would typically use which view?",multiple_choice,b,"Slide Sorter view displays all slides as thumbnails, making rearrangement easy.",medium,10,45,Normal View,Slide Sorter View,Outline View,Reading View
Science,Basic 7,Presentations,"What is an 'animation' in presentation software?",multiple_choice,c,"Animations are visual effects applied to individual objects on a slide.",medium,10,45,How slides change,Sound effects,Movement effects applied to objects on a slide,Adding images to a slide
Science,Basic 7,Presentations,"To add a sound clip or a video to your presentation, you would use the 'Insert' tab and then:",multiple_choice,a,"The Insert tab contains options for adding various media types.",easy,10,30,Media or Audio/Video option,Font option,Paragraph option,Style option
Science,Basic 7,Presentations,"What does 'Slide Show' view do?",multiple_choice,d,"Slide Show view displays the presentation in full-screen mode for delivery.",easy,10,30,Allows you to edit slides,Shows all slides as thumbnails,Prints your slides,Displays the presentation in full-screen mode
Science,Basic 7,Presentations,"Which element is used to add short notes for the presenter that are not visible to the audience?",multiple_choice,b,"Speaker notes or notes pane are for the presenter's reference.",medium,10,45,Header,Speaker notes,Footer,Outline
Science,Basic 7,Presentations,"To quickly apply consistent formatting to multiple slides (e.g., logo, background), you would edit the:",multiple_choice,c,"The Slide Master controls the overall design and layout of slides in a presentation.",hard,10,75,Individual slides,Theme,Slide Master,Handout Master
Science,Basic 7,Presentations,"The file extension for a Microsoft PowerPoint presentation is typically:",multiple_choice,a,".pptx is the standard file format for modern PowerPoint presentations.",easy,10,30,.pptx,.docx,.xlsx,.txt
Science,Basic 7,Presentations,"What is the purpose of a 'bullet point' list on a slide?",multiple_choice,b,"Bullet points organize information clearly and concisely.",easy,10,30,To add a new slide,To organize information in a clear, concise way,To draw a picture,To add a formula
Science,Basic 7,Presentations,"When delivering a presentation, it's important to:",multiple_choice,d,"Maintaining eye contact and engaging the audience are key presentation skills.",easy,10,30,Read directly from the slides,Face away from the audience,Speak very quickly,Maintain eye contact with the audience
Science,Basic 7,Presentations,"To insert a picture from your computer into a slide, you would click on the 'Insert' tab and then:",multiple_choice,b,"The 'Pictures' or 'Images' option allows you to add local images.",easy,10,30,Text Box,Pictures/Images,Table,Chart
Science,Basic 7,Digital Safety,"What does 'VPN' stand for?",multiple_choice,a,"VPN stands for Virtual Private Network, which creates a secure connection over a public network.",medium,10,60,Virtual Private Network,Very Personal Node,Virus Protection Now,Volatile Public Network
Science,Basic 7,Digital Safety,"Why is two-factor authentication (2FA) recommended for online accounts?",multiple_choice,b,"2FA adds an extra layer of security by requiring a second verification method.",hard,10,75,It makes your password shorter,It provides an extra layer of security,It speeds up login time,It automatically changes your password
Science,Basic 7,Word Processing,"To cut text means to:",multiple_choice,b,"Cutting removes text from its original location to the clipboard.",easy,10,30,Make a copy of the text,Remove the text from its current location and place it on the clipboard,Add new text to the document,Change the color of the text
Science,Basic 7,Word Processing,"Which feature allows you to change the appearance of text, such as its size, color, or style?",multiple_choice,a,"Font formatting controls the visual characteristics of text.",easy,10,30,Font formatting,Page layout,Spell check,Mail merge
Science,Basic 7,Spreadsheets,"What is a 'row' in a spreadsheet identified by?",multiple_choice,a,"Rows are identified by numbers.",easy,10,30,A number,A letter,A symbol,A name
Science,Basic 7,Spreadsheets,"To quickly sum a range of cells without typing the formula, you can use the:",multiple_choice,c,"The AutoSum feature automatically inserts the SUM function.",medium,10,45,Average button,Filter button,AutoSum button,Sort button
Science,Basic 7,Presentations,"When preparing a presentation, it is generally best to have:",multiple_choice,d,"Slides should be concise and use minimal text to avoid overwhelming the audience.",medium,10,45,Lots of text on each slide,Very small fonts,Many complex images,Minimal text and clear visuals
Science,Basic 7,Presentations,"Which mode in PowerPoint allows you to see your slides with speaker notes visible only to you?",multiple_choice,a,"Presenter View is designed for this purpose, showing notes on a separate screen.",hard,10,75,Presenter View,Slide Show View,Normal View,Outline View
Science,Basic 7,Digital Safety,"What is 'malware'?",multiple_choice,c,"Malware is a broad term for malicious software designed to harm or exploit systems.",medium,10,45,Software for editing photos,A type of computer hardware,Software designed to harm or disrupt a computer system,A popular online game
Science,Basic 7,Digital Safety,"Regularly backing up your important files helps protect against:",multiple_choice,a,"Backups are essential for data recovery in case of loss due to hardware failure, malware, or accidental deletion.",easy,10,30,Data loss,Slow internet,Unauthorized access,Too many emails
Science,Basic 7,Word Processing,"To change the spacing between lines of text, you would adjust the:",multiple_choice,b,"Line spacing controls the vertical distance between lines.",medium,10,45,Font size,Line spacing,Paragraph alignment,Page orientation
Science,Basic 7,Word Processing,"What is a 'template' in a word processor?",multiple_choice,d,"A template is a pre-designed document that serves as a starting point.",medium,10,45,A tool for spell checking,A feature to insert images,A type of font,A pre-formatted document design
Science,Basic 7,Spreadsheets,"What is a 'column' in a spreadsheet identified by?",multiple_choice,b,"Columns are identified by letters.",easy,10,30,A number,A letter,A symbol,A name
Science,Basic 7,Spreadsheets,"Which feature allows you to display only the data that meets specific criteria?",multiple_choice,a,"Filtering hides rows that don't match the specified criteria.",medium,10,45,Filter,Sort,Conditional Formatting,AutoSum
Science,Basic 7,Presentations,"What is the main advantage of using visuals (images, charts) in a presentation?",multiple_choice,c,"Visuals make information more engaging and easier to understand and remember.",easy,10,30,They make the slides longer,They are required by the software,They make information easier to understand and remember,They make the presentation faster to create
Science,Basic 7,Presentations,"Which type of software is Microsoft PowerPoint?",multiple_choice,b,"PowerPoint is a well-known presentation software.",easy,10,30,Word processing software,Presentation software,Spreadsheet software,Graphics software
ICT,Basic 7,Computer Hardware,Which component converts the computer's digital signals into visual output for the screen?,multiple_choice,b,The graphics card (or GPU) is responsible for rendering images to the display.,medium,10,45,Sound card,Graphics card,Network card,Modem
ICT,Basic 7,Computer Hardware,What is the purpose of a cooling fan inside a computer?,multiple_choice,c,Fans dissipate heat generated by components like the CPU and GPU to prevent overheating.,easy,5,30,To make noise,To generate electricity,To prevent overheating,To store data
ICT,Basic 7,Computer Hardware,Which type of cable is commonly used to connect a monitor to the computer's graphics card?,multiple_choice,a,HDMI (High-Definition Multimedia Interface) is a common digital connection for monitors.,easy,5,30,HDMI,Ethernet,USB,Power cable
ICT,Basic 7,Computer Hardware,What is a touchpad typically used for?,multiple_choice,d,A touchpad on a laptop serves as a pointing device, similar to a mouse.,easy,5,30,Typing,Printing,Scanning,Pointing
ICT,Basic 7,Computer Hardware,The amount of data a storage device can hold is measured in?,multiple_choice,b,Storage capacity is measured in bytes (KB, MB, GB, TB).,easy,5,30,Hertz,Bytes,Watts,Meters
ICT,Basic 7,Computer Hardware,Which hardware device is essential for connecting a computer to a wired network?,multiple_choice,c,A Network Interface Card (NIC) is required for wired network connections.,medium,10,45,Bluetooth adapter,Wi-Fi adapter,Network Interface Card (NIC),Sound card
ICT,Basic 7,Computer Hardware,What is the primary difference between HDD (Hard Disk Drive) and SSD (Solid State Drive)?,multiple_choice,a,SSDs use flash memory and have no moving parts, making them faster and more durable than HDDs.,hard,15,75,SSD uses flash memory, HDD uses spinning platters,HDD is faster than SSD,SSD has less storage capacity than HDD,HDD is more expensive than SSD
ICT,Basic 7,Computer Hardware,Which device allows you to control the pointer on the screen by rolling a ball or moving it on a surface?,multiple_choice,b,A mouse is a common pointing device.,easy,5,30,Keyboard,Mouse,Scanner,Joystick
ICT,Basic 7,Computer Hardware,What is the smallest unit of data in a computer?,multiple_choice,c,A bit (binary digit) is the smallest unit of data.,medium,10,45,Byte,Kilobyte,Bit,Megabyte
ICT,Basic 7,Computer Hardware,Which component supplies audio output to headphones or speakers?,multiple_choice,d,A sound card processes and outputs audio.,easy,5,30,Graphics card,Network card,Motherboard,Sound card
ICT,Basic 7,Computer Hardware,What type of memory is used to store the BIOS (Basic Input/Output System) instructions, which are needed to start up the computer?,multiple_choice,b,ROM (Read-Only Memory) stores the BIOS, as it needs to retain information when power is off.,hard,15,90,RAM,ROM,Cache Memory,Flash Memory
ICT,Basic 7,Computer Hardware,Which of these is a portable storage device that uses flash memory and plugs into a USB port?,multiple_choice,a,A USB flash drive is a common portable storage device.,easy,5,30,USB flash drive,CD-ROM,Floppy disk,Hard disk
ICT,Basic 7,Computer Hardware,What is the main function of the motherboard's expansion slots?,multiple_choice,c,Expansion slots allow additional hardware components (like graphics cards, sound cards) to be added.,medium,10,60,To connect the CPU,To store data,To add extra components like graphics cards,To provide power
ICT,Basic 7,Computer Hardware,A device that converts analog signals into digital signals and vice-versa, allowing computers to communicate over telephone lines, is a?,multiple_choice,d,A modem modulates and demodulates signals for communication over phone lines or other media.,medium,10,60,Router,Switch,Hub,Modem
ICT,Basic 7,Computer Hardware,What is the primary role of a server in a network?,multiple_choice,a,A server provides resources, data, or services to other computers (clients) on a network.,hard,15,75,To provide services and resources to clients,To display output,To store personal files,To type documents
ICT,Basic 7,Computer Software,What is a graphical user interface (GUI)?,multiple_choice,b,A GUI allows users to interact with software using visual elements like icons and menus.,medium,10,45,A text-based command system,A visual way to interact with software,A type of programming language,A network protocol
ICT,Basic 7,Computer Software,Which of the following is a type of system software?,multiple_choice,c,Device drivers are system software that enable communication between hardware and the OS.,medium,10,45,Web browser,Word processor,Device driver,Video game
ICT,Basic 7,Computer Software,What is the purpose of software updates?,multiple_choice,d,Updates typically include bug fixes, security patches, and new features.,easy,5,30,To make software slower,To delete user files,To add more ads,To fix bugs and improve security
ICT,Basic 7,Computer Software,Which software would you use to organize and analyze numerical data in rows and columns?,multiple_choice,b,Spreadsheet software like Microsoft Excel is designed for this.,easy,5,30,Word processor,Spreadsheet software,Presentation software,Database software
ICT,Basic 7,Computer Software,What is open-source software?,multiple_choice,a,Open-source software has its source code freely available for modification and distribution.,hard,15,90,Software whose source code is freely available for modification,Software that must be purchased,Software that only works on specific hardware,Software that cannot be modified
ICT,Basic 7,Computer Software,What is a computer virus?,multiple_choice,c,A computer virus is malicious software that can replicate itself and spread to other computers.,medium,10,45,A healthy program,A type of operating system,A malicious program that replicates itself,A software used for gaming
ICT,Basic 7,Computer Software,The process of removing a software program from a computer is called?,multiple_choice,d,Uninstalling software removes its files and settings from the system.,easy,5,30,Installing,Updating,Running,Uninstalling
ICT,Basic 7,Computer Software,Which of these is a programming language?,multiple_choice,a,Python is a popular programming language.,medium,10,45,Python,Windows,Microsoft Word,Google Chrome
ICT,Basic 7,Computer Software,What is software piracy?,multiple_choice,b,Software piracy is the illegal copying or distribution of software.,medium,10,60,Buying legitimate software,Illegal copying or distribution of software,Developing new software,Sharing freeware software
ICT,Basic 7,Computer Software,Which software utility is used to compress and decompress files?,multiple_choice,c,File compression utilities (like WinZip, 7-Zip) reduce file size.,hard,15,75,Antivirus software,Web browser,File compression utility,Disk defragmenter
ICT,Basic 7,Computer Software,What is the term for a software program that allows users to create and manage digital images and graphics?,multiple_choice,d,Graphics software (e.g., Adobe Photoshop, GIMP) is used for this.,easy,5,30,Word processor,Spreadsheet,Presentation software,Graphics software
ICT,Basic 7,Computer Software,Which of the following describes a 'bug' in software?,multiple_choice,a,A bug is an error or flaw in software that causes it to behave unexpectedly.,medium,10,45,An error or flaw in the software,A new feature in the software,A type of computer virus,A harmless warning message
ICT,Basic 7,Computer Software,The instructions that are permanently stored in the computer's hardware and cannot be easily modified are called?,multiple_choice,d,Firmware is embedded in hardware and provides low-level control.,hard,15,90,Application software,Operating system,Utility software,Firmware
ICT,Basic 7,Computer Software,Which software is responsible for booting up the computer and managing its basic operations?,multiple_choice,b,The operating system handles startup and resource management.,medium,10,45,Application software,Operating system,Device driver,Utility software
ICT,Basic 7,Internet and Web,What is the main function of a Uniform Resource Locator (URL)?,multiple_choice,b,A URL specifies the unique address of a resource on the internet (e.g., a website).,medium,10,45,To send emails,To specify the address of a web page,To search for information,To download files
ICT,Basic 7,Internet and Web,What does "http://" at the beginning of a web address signify?,multiple_choice,a,It indicates the Hypertext Transfer Protocol, which is how data is transferred on the web.,easy,5,30,The protocol for transferring web pages,The name of the website,The location of the server,The type of file
ICT,Basic 7,Internet and Web,Which of these is a way to protect your personal information while online?,multiple_choice,c,Using strong, unique passwords helps prevent unauthorized access to accounts.,medium,10,45,Sharing your password with friends,Using the same password for all accounts,Using strong and unique passwords,Clicking on suspicious links
ICT,Basic 7,Internet and Web,What is a webpage?,multiple_choice,d,A webpage is a document or information resource suitable for the World Wide Web.,easy,5,30,A search engine,A mobile app,A type of computer hardware,A single document on the World Wide Web
ICT,Basic 7,Internet and Web,What is "spam" in the context of email?,multiple_choice,a,Spam refers to unsolicited or junk email, often commercial.,easy,5,30,Unsolicited or junk email,A type of antivirus software,A secure email service,A new email client
ICT,Basic 7,Internet and Web,Which of the following is an example of an online social media platform?,multiple_choice,b,Facebook is a widely used social media platform.,easy,5,30,Google Search,Facebook,Wikipedia,Amazon
ICT,Basic 7,Internet and Web,What is bandwidth in relation to internet connection speed?,multiple_choice,c,Bandwidth refers to the maximum amount of data that can be transferred over an internet connection in a given amount of time.,hard,15,75,The physical length of the internet cable,The amount of time it takes to load a webpage,The maximum amount of data that can be transferred per second,The number of devices connected to the internet
ICT,Basic 7,Internet and Web,What is the purpose of an internet browser's 'history' feature?,multiple_choice,a,The history feature keeps a record of visited websites.,medium,10,45,To keep a record of visited websites,To delete temporary files,To block pop-up ads,To improve internet speed
ICT,Basic 7,Internet and Web,Which term refers to the act of connecting to the internet?,multiple_choice,d,Going online or connecting to the internet means accessing it.,easy,5,30,Downloading,Uploading,Streaming,Going online
ICT,Basic 7,Internet and Web,What is a "cookie" in web Browse?,multiple_choice,b,A cookie is a small piece of data stored by the web browser on the user's computer, used for remembering stateful information or recording Browse activity.,hard,15,90,A type of computer virus,A small text file stored on your computer by a website,A popular web browser,A feature that speeds up internet downloads
ICT,Basic 7,Internet and Web,What does VPN stand for?,multiple_choice,a,VPN (Virtual Private Network) provides a secure connection over a public network like the internet.,hard,15,75,Virtual Private Network,Very Personal Network,Visual Public Network,Video Private Node
ICT,Basic 7,Internet and Web,Which of these is a common method for sharing large files over the internet?,multiple_choice,c,Cloud storage services (like Google Drive, Dropbox) are used to share large files.,medium,10,45,Sending via email attachment,Printing and scanning,Using cloud storage services,Saving to a local hard drive
ICT,Basic 7,Internet and Web,What is the main function of a search engine?,multiple_choice,b,Search engines help users find information on the World Wide Web.,easy,5,30,To create websites,To find information on the World Wide Web,To send emails,To make online purchases
ICT,Basic 7,Internet and Web,What is "copyright" in the context of online content?,multiple_choice,a,Copyright protects the original works of authorship (e.g., text, images, music) on the internet.,medium,10,60,Legal protection for original works,A type of computer virus,A method of sharing files,A way to speed up internet Browse
ICT,Basic 7,Internet and Web,Which part of a URL typically indicates the type of organization or country the website belongs to (e.g., .org, .edu, .gh)?,multiple_choice,d,The Top-Level Domain (TLD) provides this indication.,medium,10,45,Protocol,Subdomain,Domain name,Top-Level Domain (TLD)
ICT,Basic 7,Digital Communication,Which of these is a real-time digital communication tool for voice and video?,multiple_choice,c,Video conferencing allows real-time voice and video interaction.,easy,5,30,Email,Blog,Video conferencing,SMS
ICT,Basic 7,Digital Communication,What is the term for a communication method where messages are sent and received almost instantly?,multiple_choice,a,Instant messaging allows for real-time text-based conversation.,easy,5,30,Instant messaging,Email,Blogging,Podcast
ICT,Basic 7,Digital Communication,What does emojis add to digital text communication?,multiple_choice,b,Emojis convey emotions and expressions that text alone might not.,easy,5,30,More formal tone,Emotional expression,Longer messages,Reduced clarity
ICT,Basic 7,Digital Communication,Which digital communication platform is primarily used for professional networking?,multiple_choice,a,LinkedIn is a professional networking site.,easy,5,30,LinkedIn,Snapchat,TikTok,Pinterest
ICT,Basic 7,Digital Communication,What is the act of sharing information, ideas, or feelings through digital means?,multiple_choice,d,This is the core definition of digital communication.,easy,5,30,Physical interaction,Traditional mailing,Face-to-face meeting,Digital communication
ICT,Basic 7,Digital Communication,Which of these is an example of an asynchronous digital communication method?,multiple_choice,b,Email allows for communication where participants do not need to be online at the same time.,medium,10,45,Video call,Email,Instant chat,Online meeting
ICT,Basic 7,Digital Communication,What is a common safety tip for digital communication to protect personal information?,multiple_choice,c,Being careful about sharing personal details online is crucial for safety.,medium,10,60,Share your address online,Use easily guessable passwords,Be cautious about sharing personal details,Accept all friend requests
ICT,Basic 7,Digital Communication,What is a "webinar"?,multiple_choice,b,A webinar is an online seminar or presentation, often interactive.,hard,15,75,A type of social media,An online seminar conducted via the internet,A personal blog,A digital magazine
ICT,Basic 7,Digital Communication,Which feature of digital communication allows you to attach files like documents and images to messages?,multiple_choice,a,File attachments are a standard feature in email and messaging apps.,easy,5,30,File attachment,Emoji,Voice message,Status update
ICT,Basic 7,Digital Communication,What does the "CC" field in an email mean?,multiple_choice,b,CC stands for Carbon Copy, used to send a copy to other recipients who are visible to all.,medium,10,45,Confidential Copy,Carbon Copy,Contact Code,Client Communication
ICT,Basic 7,Digital Communication,Which of the following is a potential risk of oversharing information on social media?,multiple_choice,d,Oversharing can lead to privacy breaches and make one vulnerable to cyberattacks.,medium,10,60,Increased popularity,More job opportunities,Stronger friendships,Privacy breaches and identity theft
ICT,Basic 7,Digital Communication,What is an online profile?,multiple_choice,c,An online profile is a collection of personal data and information that an individual makes available on a social networking site.,medium,10,45,A computer program,A type of website,A collection of personal information on an online platform,A digital game
ICT,Basic 7,Digital Communication,Which communication technology allows a user to "see" and "hear" the other person in real-time using a camera and microphone?,multiple_choice,d,Video calls provide real-time audio and video interaction.,easy,5,30,Email,Text messaging,Audio recording,Video call
ICT,Basic 7,Digital Communication,What is the main purpose of "emoticons" or "smileys" in digital messages?,multiple_choice,a,They help convey emotions and feelings in text-based communication.,easy,5,30,To convey emotions and feelings,To shorten messages,To make messages formal,To replace words entirely
ICT,Basic 7,Digital Communication,The act of using offensive or threatening language in online communication is known as?,multiple_choice,b,Flaming refers to hostile and insulting online interactions.,medium,10,45,Phishing,Flaming,Spamming,Blogging
Science,Basic 7,Digital Safety,"Which of these actions helps prevent identity theft online?",multiple_choice,d,"Keeping personal information private and using strong passwords reduces the risk of identity theft.",easy,10,30,Sharing your password with friends,Clicking on all pop-up ads,Using public Wi-Fi for banking,Being careful about sharing personal information
Science,Basic 7,Digital Safety,"What is the purpose of 'Incognito' or 'Private Browse' mode in a web browser?",multiple_choice,c,"These modes prevent the browser from saving Browse history, cookies, and site data locally.",medium,10,45,To make your internet faster,To download files anonymously,To prevent Browse history from being saved locally,To access blocked websites
Science,Basic 7,Digital Safety,"What does 'HTTPS' at the beginning of a website address indicate?",multiple_choice,a,"HTTPS indicates a secure, encrypted connection, which is important for protecting data.",easy,10,30,The website is secure and encrypted,The website is very popular,The website is for shopping only,The website is new
Science,Basic 7,Digital Safety,"When creating an online account, what information should you avoid using in your password?",multiple_choice,b,"Personal details like birthdays are easily guessable and should be avoided in passwords.",easy,10,30,Numbers and symbols,Your birthday or name,Uppercase and lowercase letters,Random characters
Science,Basic 7,Digital Safety,"What is a 'Trojan horse' in the context of computer security?",multiple_choice,a,"A Trojan horse is malware disguised as legitimate software.",medium,10,45,Malware disguised as legitimate software,A type of antivirus program,A tool for data backup,A network device
Science,Basic 7,Digital Safety,"What is the danger of connecting to unsecured public Wi-Fi networks?",multiple_choice,d,"Unsecured networks make your data vulnerable to interception by others.",medium,10,45,They are always very slow,They require a password,They can inject viruses into your computer without warning,Your data can be easily intercepted by others
Science,Basic 7,Digital Safety,"What is 'copyright' in the digital world?",multiple_choice,b,"Copyright protects original works of authorship from unauthorized use.",medium,10,45,The right to copy anything from the internet,The legal right to control the use of an original work,A type of internet connection,A software update
Science,Basic 7,Digital Safety,"If a website's URL starts with 'http://' instead of 'https://', it means:",multiple_choice,c,"'http://' indicates an unencrypted connection, making it less secure for sensitive information.",easy,10,30,It's a very fast website,It's a trusted website,It's not a secure or encrypted connection,It's a website for downloads
Science,Basic 7,Digital Safety,"Which of these is a good practice for online safety?",multiple_choice,b,"Thinking before posting helps avoid sharing inappropriate or harmful content.",easy,10,30,Sharing personal details with strangers,Thinking before you post something online,Accepting all friend requests,Opening attachments from unknown senders
Science,Basic 7,Digital Safety,"What is the purpose of a 'backup' of your files?",multiple_choice,a,"Backups are copies of data that can be used to restore files in case of data loss.",easy,10,30,To have a copy of your files in case the original is lost,To make your computer run faster,To share files with others,To compress files to save space
Science,Basic 7,Digital Safety,"What does 'ethics' mean in the context of using ICT?",multiple_choice,d,"Digital ethics involve principles of right and wrong behavior in using technology.",medium,10,45,Knowing how to use all software,The speed of your internet connection,The cost of computer hardware,Using technology in a morally and socially responsible way
Science,Basic 7,Digital Safety,"Which of these helps protect your eyes from strain when using a computer for a long time?",multiple_choice,c,"Taking regular breaks (e.g., 20-20-20 rule) reduces eye strain.",easy,10,30,Turning off the screen,Eating while using the computer,Taking regular breaks,Using a very small font size
Science,Basic 7,Word Processing,"To change the direction of text from horizontal to vertical, you would use:",multiple_choice,d,"Some word processors allow changing text orientation for special effects.",hard,10,75,Line spacing,Paragraph alignment,Word wrap,Text direction/orientation
Science,Basic 7,Word Processing,"What is the default text alignment in most word processors?",multiple_choice,a,"Most word processors align text to the left by default.",easy,10,30,Left alignment,Right alignment,Center alignment,Justify
Science,Basic 7,Word Processing,"To repeatedly apply the same formatting (e.g., bold, italic) to different parts of a document, you can use the:",multiple_choice,b,"The Format Painter copies and applies formatting from one selection to another.",medium,10,45,Spell Checker,Format Painter,Undo button,Find and Replace
Science,Basic 7,Word Processing,"What is a 'margin' in a word document?",multiple_choice,c,"Margins are the blank spaces around the edges of the page.",easy,10,30,The line spacing,The font size,The blank space between the edge of the page and the text,The space between paragraphs
Science,Basic 7,Word Processing,"To search for a specific word or phrase in a document and replace it with another, you would use:",multiple_choice,d,"The Find and Replace feature is used for this purpose.",easy,10,30,Spell check,Grammar check,Copy and paste,Find and Replace
Science,Basic 7,Word Processing,"Which ribbon tab in Microsoft Word is used to insert a table, picture, or chart?",multiple_choice,b,"The Insert tab contains commands for adding various objects to a document.",easy,10,30,Home,Insert,Design,Review
Science,Basic 7,Word Processing,"What is a 'footer' in a word document?",multiple_choice,a,"A footer is text or graphics that appears at the bottom of every page or section.",medium,10,45,Text at the bottom of every page,The title of the document,A section break,Text at the top of every page
Science,Basic 7,Word Processing,"Which of these allows you to quickly navigate through a long document by jumping to headings?",multiple_choice,c,"The Navigation Pane (or Document Map) shows a navigable outline of headings.",hard,10,75,Word Count,Spell Check,Navigation Pane,Print Preview
Science,Basic 7,Word Processing,"What is the purpose of 'Page Orientation'?",multiple_choice,b,"Page orientation sets whether the page is portrait (vertical) or landscape (horizontal).",easy,10,30,To change the font size,To change the layout of the page (portrait or landscape),To add page numbers,To insert a new page
Science,Basic 7,Word Processing,"To create a list with numbers (1, 2, 3...), you would use:",multiple_choice,a,"Numbered lists (or ordered lists) use numbers for items.",easy,10,30,Numbered list,Bulleted list,Checklist,Symbol list
Science,Basic 7,Word Processing,"Before printing a document, it is good practice to use:",multiple_choice,c,"Print Preview shows exactly how the document will look when
ICT,Basic 7,Database Basics,What is a collection of related data organized for easy access and management?,multiple_choice,b,A database is designed for efficient storage, retrieval, and management of structured data.,easy,5,30,Spreadsheet,Database,Word document,Presentation
ICT,Basic 7,Database Basics,In a database, what is a single piece of information, such as a name or age, called?,multiple_choice,c,A field (or attribute) is a single piece of data.,easy,5,30,Record,Table,Field,Query
ICT,Basic 7,Database Basics,What is a row in a database table called?,multiple_choice,a,A row in a database table is also known as a record, representing a single entry or entity.,medium,10,45,Record,Field,Form,Report
ICT,Basic 7,Database Basics,What is the purpose of a primary key in a database table?,multiple_choice,d,A primary key uniquely identifies each record in a table.,medium,10,60,To sort data alphabetically,To store large amounts of text,To prevent data entry,To uniquely identify each record
ICT,Basic 7,Database Basics,Which of these is a common software used to create and manage databases?,multiple_choice,b,Microsoft Access is a popular Desktop Database Management System (DBMS).,easy,5,30,Microsoft Word,Microsoft Access,Microsoft Excel,Microsoft PowerPoint
ICT,Basic 7,Database Basics,What is a database query?,multiple_choice,c,A query is a request for data from a database.,medium,10,45,A way to input new data,A way to print a report,A request for data from a database,A tool to design tables
ICT,Basic 7,Database Basics,What is the main advantage of using a database over a simple spreadsheet for managing large amounts of complex data?,multiple_choice,a,Databases offer better organization, relationships, and data integrity for large, complex datasets.,hard,15,75,Better organization and relationships between data,Easier to create,Faster to open,Requires less storage space
ICT,Basic 7,Database Basics,In a school database, "Student ID" would most likely be a?,multiple_choice,d,Student ID is unique to each student, making it suitable as a primary key.,medium,10,45,Report,Form,Query,Primary Key
ICT,Basic 7,Database Basics,What is a collection of related fields in a database called?,multiple_choice,c,A collection of related fields forms a record (or row).,easy,5,30,Query,Report,Record,Table
ICT,Basic 7,Database Basics,Which database object is used to view, enter, and change data easily, often with a user-friendly layout?,multiple_choice,b,Forms provide a user-friendly interface for data entry and viewing.,medium,10,60,Table,Form,Query,Report
ICT,Basic 7,Database Basics,What does DBMS stand for?,multiple_choice,a,DBMS stands for Database Management System, software used to interact with databases.,medium,10,45,Database Management System,Data Backup Management System,Digital Basic Management System,Direct Business Management System
ICT,Basic 7,Database Basics,Which of the following is NOT a typical use of a database?,multiple_choice,d,Drawing and painting typically use graphics software, not database software.,easy,5,30,Managing customer records,Storing product information,Tracking inventory,Drawing and painting
ICT,Basic 7,Database Basics,The process of arranging data in a specific order (e.g., alphabetically or numerically) is called?,multiple_choice,b,Sorting arranges data based on a defined order.,medium,10,45,Filtering,Sorting,Querying,Reporting
ICT,Basic 7,Database Basics,What is the purpose of a database report?,multiple_choice,a,Reports present data in a structured and formatted way for printing or viewing.,medium,10,60,To present data in a printable format,To enter new data,To find specific data,To define relationships between tables
ICT,Basic 7,Database Basics,What is data redundancy in a database?,multiple_choice,c,Data redundancy means the same data is stored multiple times, which can lead to inconsistencies.,hard,15,90,Data being stored securely,Data being quickly accessible,The same data being stored multiple times,Data being corrupted
ICT,Basic 7,Programming Concepts,What is a set of instructions written in a specific language that a computer can understand?,multiple_choice,b,A computer program is a sequence of instructions.,easy,5,30,Hardware,Program,Data,Algorithm
ICT,Basic 7,Programming Concepts,Which of the following is a step-by-step procedure for solving a problem or accomplishing a task?,multiple_choice,a,An algorithm is a precise sequence of instructions.,medium,10,45,Algorithm,Program,Syntax,Bug
ICT,Basic 7,Programming Concepts,What does "coding" refer to in computer programming?,multiple_choice,c,Coding is the act of writing instructions in a programming language.,easy,5,30,Drawing pictures,Playing games,Writing instructions for a computer,Browse the internet
ICT,Basic 7,Programming Concepts,In programming, what is a 'bug'?,multiple_choice,d,A bug is an error in a program that prevents it from running correctly.,easy,5,30,A new feature,A type of computer virus,A quick solution,An error in a program
ICT,Basic 7,Programming Concepts,Which symbol is often used to assign a value to a variable in many programming languages?,multiple_choice,a,The equals sign (=) is commonly used for assignment.,medium,10,45,=,+, -,*
ICT,Basic 7,Programming Concepts,What is the process of finding and fixing errors in a computer program called?,multiple_choice,b,Debugging is the process of identifying and resolving errors.,medium,10,45,Compiling,Debugging,Executing,Running
ICT,Basic 7,Programming Concepts,A 'variable' in programming is a named storage location that can hold?,multiple_choice,c,Variables hold data that can change during program execution.,medium,10,60,A fixed value only,A program's code,Data that can change,Another program
ICT,Basic 7,Programming Concepts,Which programming concept involves making decisions based on certain conditions (e.g., if-then-else statements)?,multiple_choice,d,Conditional statements (selection) allow programs to make decisions.,hard,15,75,Sequence,Loop,Variable,Selection (Condition)
ICT,Basic 7,Programming Concepts,What is the output of a program that asks "What is your name?" and then prints "Hello, [Name]!" if the user types "John"?,multiple_choice,a,If the user types "John", the program will print "Hello, John!". This tests understanding of input/output.,easy,5,30,"Hello, John!",What is your name?,[Name]!,Error
ICT,Basic 7,Programming Concepts,What is 'syntax' in programming?,multiple_choice,b,Syntax refers to the rules that dictate the structure of a programming language.,medium,10,45,The logic of the program,The rules for writing code,The errors in the program,The speed of the program
ICT,Basic 7,Programming Concepts,Which of these is a programming construct that repeats a block of code multiple times?,multiple_choice,c,A loop (iteration) allows code to be executed repeatedly.,medium,10,60,Condition,Variable,Loop,Function
ICT,Basic 7,Programming Concepts,What is the first step in solving a problem using a computer program?,multiple_choice,d,Understanding and defining the problem is crucial before coding.,hard,15,90,Writing the code,Testing the program,Debugging the program,Understanding the problem
ICT,Basic 7,Programming Concepts,Which of the following is NOT a common data type in programming?,multiple_choice,a,Programs are generally not a data type.,medium,10,45,Program,Integer,String,Boolean
ICT,Basic 7,Programming Concepts,What is the process of converting human-readable code into machine-readable code called?,multiple_choice,b,Compiling (or interpreting) converts high-level code into executable machine code.,medium,10,60,Debugging,Compiling,Executing,Testing
ICT,Basic 7,Programming Concepts,A flowchart uses symbols to represent the steps of an algorithm. What symbol typically represents the start or end of a flowchart?,multiple_choice,a,An oval (or rounded rectangle) is used for start/end points.,medium,10,45,Oval/Rounded rectangle,Rectangle,Diamond,Arrow
ICT,Basic 7,File Management,What is a file in a computer?,multiple_choice,b,A file is a collection of related information stored on a storage device.,easy,5,30,A computer program,A collection of related information,A hardware component,A network connection
ICT,Basic 7,File Management,What is a folder (or directory) used for in a computer system?,multiple_choice,c,Folders organize and group files together.,easy,5,30,To store hardware,To browse the internet,To organize and store files,To print documents
ICT,Basic 7,File Management,Which of these is a valid file extension for a text document?,multiple_choice,a,.txt is a common extension for plain text files.,easy,5,30,.txt,.jpg,.mp3,.exe
ICT,Basic 7,File Management,What is the process of permanently removing a file from your computer's storage?,multiple_choice,d,Deleting a file removes it from storage.,easy,5,30,Copying,Moving,Renaming,Deleting
ICT,Basic 7,File Management,Which operation creates an identical copy of a file in a new location while keeping the original?,multiple_choice,b,Copying creates a duplicate; moving transfers the original.,medium,10,45,Moving,Copying,Deleting,Renaming
ICT,Basic 7,File Management,What is the purpose of the "Recycle Bin" (or "Trash") on a computer?,multiple_choice,a,It temporarily holds deleted files, allowing for recovery.,medium,10,45,To temporarily store deleted files,To permanently delete files,To store new files,To store system files
ICT,Basic 7,File Management,What is a file path?,multiple_choice,c,A file path is the unique location of a file within the file system structure.,medium,10,60,The name of a file,The size of a file,The unique location of a file in the file system,The content of a file
ICT,Basic 7,File Management,Which of the following describes renaming a file?,multiple_choice,d,Renaming changes the file's name without altering its content or location.,easy,5,30,Changing its content,Moving it to a new folder,Deleting it,Changing its name
ICT,Basic 7,File Management,Why is it important to organize files and folders?,multiple_choice,b,Good organization makes it easier to find and manage files.,medium,10,45,To take up more storage space,To make files easier to find,To speed up the computer,To prevent viruses
ICT,Basic 7,File Management,What does the file extension .jpg or .jpeg typically indicate?,multiple_choice,a,These extensions are for image files.,easy,5,30,An image file,A video file,An audio file,A text file
ICT,Basic 7,File Management,Which operation moves a file from one location to another, removing it from the original location?,multiple_choice,b,Moving transfers the original file.,medium,10,45,Copying,Moving,Deleting,Renaming
ICT,Basic 7,File Management,What is file compression?,multiple_choice,c,File compression reduces the size of a file by encoding its data more efficiently.,hard,15,75,Increasing the size of a file,Encrypting a file,Reducing the size of a file,Making a file read-only
ICT,Basic 7,File Management,What does .mp3 usually represent as a file extension?,multiple_choice,b,.mp3 is a common extension for audio files.,easy,5,30,Video file,Audio file,Image file,Document file
ICT,Basic 7,File Management,Which key combination is commonly used to "copy" selected text or files?,multiple_choice,a,Ctrl+C (or Command+C on Mac) is the standard shortcut for copying.,medium,10,45,Ctrl + C,Ctrl + X,Ctrl + V,Ctrl + Z
ICT,Basic 7,File Management,What is the purpose of creating a backup of your files?,multiple_choice,d,Backups protect against data loss due to hardware failure, accidental deletion, etc.,medium,10,60,To free up space on your hard drive,To make files difficult to find,To increase file size,To protect against data loss
ICT,Basic 7,Computer Networks,What is a computer network?,multiple_choice,a,A computer network is a group of interconnected computers and devices that can share resources.,easy,5,30,A group of interconnected computers that share resources,A single computer,A computer program,A way to store data
ICT,Basic 7,Computer Networks,Which device connects multiple computers and other network devices together within a local area network (LAN)?,multiple_choice,c,A switch connects devices within a LAN.,medium,10,45,Modem,Router,Switch,Hub
ICT,Basic 7,Computer Networks,What does LAN stand for?,multiple_choice,b,LAN stands for Local Area Network, covering a small geographical area.,easy,5,30,Large Area Network,Local Area Network,Limited Access Network,Long Access Node
ICT,Basic 7,Computer Networks,What is the main purpose of a router in a network?,multiple_choice,d,A router directs data packets between different networks (e.g., between your home network and the internet).,medium,10,60,To connect only two computers,To store network data,To provide power to devices,To direct data between networks
ICT,Basic 7,Computer Networks,Which type of network covers a large geographical area, like across cities or countries?,multiple_choice,a,WAN (Wide Area Network) covers vast distances.,medium,10,45,WAN,LAN,PAN,MAN
ICT,Basic 7,Computer Networks,What is an IP address?,multiple_choice,c,An IP address is a unique numerical label assigned to each device on a computer network.,hard,15,75,A website's name,A software program,A unique address for a device on a network,A type of internet cable
ICT,Basic 7,Computer Networks,What is the benefit of sharing resources over a network?,multiple_choice,b,Sharing resources (printers, files) on a network saves costs and improves efficiency.,easy,5,30,Increased hardware cost,Cost savings and improved efficiency,Slower performance,More complex setup
ICT,Basic 7,Computer Networks,Which of these is a common type of wired network cable?,multiple_choice,a,Ethernet cables are widely used for wired network connections.,easy,5,30,Ethernet cable,USB cable,HDMI cable,Power cable
ICT,Basic 7,Computer Networks,What is Wi-Fi?,multiple_choice,d,Wi-Fi is a technology that allows wireless local area networking using radio waves.,medium,10,45,A type of computer hardware,A wired internet connection,A software program,A wireless network technology
ICT,Basic 7,Computer Networks,What is the purpose of a server in a client-server network model?,multiple_choice,b,A server provides services or resources to client computers.,medium,10,45,To browse the internet,To provide services to clients,To print documents,To connect to the monitor
ICT,Basic 7,Computer Networks,Which network topology connects all devices to a central hub or switch?,multiple_choice,c,Star topology uses a central device.,hard,15,90,Bus topology,Ring topology,Star topology,Mesh topology
ICT,Basic 7,Computer Networks,What does P2P stand for in networking?,multiple_choice,a,P2P (Peer-to-Peer) networks allow computers to directly share files with each other without a central server.,hard,15,75,Peer-to-Peer,Point-to-Point,Personal to Personal,Program to Program
ICT,Basic 7,Computer Networks,Which device is used to connect multiple independent networks or segments and filter traffic between them?,multiple_choice,d,A router directs traffic between different networks.,medium,10,60,Switch,Hub,Modem,Router
ICT,Basic 7,Computer Networks,What is a network protocol?,multiple_choice,b,A network protocol is a set of rules governing how data is exchanged between devices on a network.,medium,10,60,A type of network cable,A set of rules for communication over a network,A computer program,A type of network hardware
ICT,Basic 7,Computer Networks,What is the main function of an internet modem?,multiple_choice,c,A modem converts digital signals from your computer into analog signals for transmission over an ISP's line and vice versa.,medium,10,45,To store network data,To block unwanted websites,To convert digital signals for internet transmission,To connect computers within a single room
ICT,Basic 7,Digital Ethics,"What does 'plagiarism' mean in the context of digital content?",multiple_choice,b,"Plagiarism is using someone else's work or ideas without giving them credit.",easy,10,30,Sharing content online,Buying digital content,Presenting someone else's work as your own,Creating original digital content
ICT,Basic 7,Digital Ethics,"Why is it important to cite sources when using information from the internet?",multiple_choice,a,"Citing sources gives credit to the original creator and avoids plagiarism.",easy,10,30,To avoid plagiarism and give credit,To make your document longer,To show you used the internet,To make your computer faster
ICT,Basic 7,Digital Ethics,"What is 'cyberbullying'?",multiple_choice,c,"Cyberbullying is bullying or harassment using electronic communication.",easy,10,30,Playing online games,Sharing positive messages online,Harming or harassing someone using digital technology,Learning about computers
ICT,Basic 7,Digital Ethics,"Which of these is an example of ethical online behavior?",multiple_choice,d,"Respecting others' privacy and property is key to digital ethics.",easy,10,30,Sharing someone's private photos without permission,Downloading copyrighted music illegally,Spreading false information,Giving credit to original creators when using their work
ICT,Basic 7,Digital Ethics,"What is the main idea behind 'digital citizenship'?",multiple_choice,b,"Digital citizenship means being a responsible and safe user of technology.",medium,10,45,Having access to a computer,Being a fast typist,Being a responsible and safe user of technology,Knowing all computer shortcuts
ICT,Basic 7,Digital Ethics,"Why is it important to protect your personal information online?",multiple_choice,a,"Protecting personal info prevents misuse like identity theft or fraud.",easy,10,30,To prevent identity theft and fraud,To make your profile look better,To get more likes,To speed up your internet connection
ICT,Basic 7,Digital Ethics,"Which of these is an example of 'netiquette'?",multiple_choice,c,"Netiquette refers to polite and respectful communication online.",medium,10,45,Using all capital letters in emails,Sending chain messages,Using polite and respectful language in online communication,Sharing all your personal details
ICT,Basic 7,Digital Ethics,"What is the consequence of illegally downloading copyrighted software or music?",multiple_choice,b,"Illegal downloading can lead to legal penalties, including fines.",medium,10,45,Your computer will slow down,It can result in legal penalties and fines,You will lose internet access,Nothing, it is harmless
ICT,Basic 7,Digital Ethics,"What does it mean to be a 'responsible' digital user?",multiple_choice,d,"Responsible digital use involves thinking about impact, safety, and respect.",easy,10,30,Using the internet for many hours a day,Having the latest gadgets,Ignoring online rules,Considering the impact of your online actions on yourself and others
ICT,Basic 7,Digital Ethics,"Why should you be wary of sharing too much personal information with strangers online?",multiple_choice,a,"Strangers online may have malicious intentions, leading to scams or danger.",easy,10,30,They might use it for scams or harmful purposes,They might judge your fashion sense,They might not reply,It makes your internet slow
ICT,Basic 7,Digital Ethics,"What is the ethical implication of spreading misinformation online?",multiple_choice,c,"Spreading misinformation can lead to confusion, panic, and damage to reputation.",medium,10,45,It helps people learn faster,It makes content more interesting,It can cause harm and confusion,It is a form of free speech
ICT,Basic 7,Digital Ethics,"Before sharing a photo or video of someone online, you should:",multiple_choice,a,"It's respectful and ethical to obtain permission before sharing media involving others.",medium,10,45,Ask for their permission,Post it quickly before they see it,Edit their face out,Tag them without asking
ICT,Basic 7,Digital Ethics,"Which of these is an example of a 'digital right'?",multiple_choice,d,"The right to privacy online is a fundamental digital right.",medium,10,45,The right to free internet access,The right to download any content,The right to bully others,The right to privacy of personal data
ICT,Basic 7,Digital Ethics,"What does 'digital etiquette' primarily focus on?",multiple_choice,a,"Digital etiquette is about respectful and appropriate behavior in online interactions.",easy,10,30,Respectful online communication,Computer programming,Website design,Hardware maintenance
ICT,Basic 7,Digital Ethics,"If you witness cyberbullying, what is an ethical action to take?",multiple_choice,b,"Reporting cyberbullying and supporting the victim are ethical responses.",easy,10,30,Join in the bullying,Report it and offer support to the victim,Ignore it,Laugh at the victim
ICT,Basic 7,Emerging Technologies,"Which of these is an example of 'Artificial Intelligence' (AI)?",multiple_choice,c,"AI refers to machines performing tasks that typically require human intelligence, like recognizing speech.",easy,10,30,A standard calculator,A simple mobile phone,A voice assistant like Siri or Google Assistant,A basic spreadsheet program
ICT,Basic 7,Emerging Technologies,"What is 'Virtual Reality' (VR)?",multiple_choice,a,"VR creates a simulated environment that can be experienced interactively.",medium,10,45,A simulated experience that can be similar to or completely different from the real world,A very fast internet connection,A type of computer virus,A new way to browse websites
ICT,Basic 7,Emerging Technologies,"'Wearable technology' typically refers to devices that are:",multiple_choice,b,"Wearable technology includes smartwatches, fitness trackers, and smart glasses.",easy,10,30,Very expensive,Worn on the body,Used for only one purpose,Only for children
ICT,Basic 7,Emerging Technologies,"Which of these is a characteristic of 'Robotics'?",multiple_choice,d,"Robotics involves designing, constructing, operating, and using robots.",medium,10,45,Only used for gaming,Only for scientific research,Only found in space,Involves designing and building machines that can perform tasks
ICT,Basic 7,Emerging Technologies,"What is the 'Internet of Things' (IoT)?",multiple_choice,b,"IoT connects everyday objects to the internet, allowing them to send and receive data.",medium,10,45,A new way to search the internet,The network of physical objects embedded with sensors and software that connect to the internet,A type of social media,A fast internet service
ICT,Basic 7,Emerging Technologies,"'Augmented Reality' (AR) superimposes virtual objects onto:",multiple_choice,c,"AR overlays digital information onto the real world (e.g., Pokémon Go).",medium,10,60,A completely virtual environment,A digital map,The real world environment,A computer screen only
ICT,Basic 7,Emerging Technologies,"Which of these could be considered an 'emerging technology'?",multiple_choice,a,"Self-driving cars are an example of an emerging technology with ongoing development.",easy,10,30,Self-driving cars,Typewriters,Landline telephones,Black and white televisions
ICT,Basic 7,Emerging Technologies,"What is the potential benefit of '3D printing'?",multiple_choice,d,"3D printing allows for rapid prototyping and custom manufacturing.",medium,10,45,Makes computers run faster,Helps you send emails,Creates digital art,Allows for rapid creation of physical objects from digital designs
ICT,Basic 7,Emerging Technologies,"Which technology enables devices to connect and communicate without physical wires?",multiple_choice,b,"Wireless technology facilitates communication over radio waves.",easy,10,30,Wired connections,Wireless technology,Fiber optics,USB cables
ICT,Basic 7,Emerging Technologies,"'Biometrics' refers to technology that identifies people based on their:",multiple_choice,a,"Biometrics uses unique biological characteristics like fingerprints or facial features.",medium,10,60,Unique physical characteristics (e.g., fingerprints, face),Passwords,Birthdates,Favorite colors
ICT,Basic 7,Emerging Technologies,"What is 'Blockchain' primarily used for?",multiple_choice,c,"Blockchain is a decentralized, distributed ledger technology, known for its use in cryptocurrencies.",hard,10,75,Sending emails quickly,Creating digital art,Securely recording transactions,Browse the internet faster
ICT,Basic 7,Emerging Technologies,"'Smart homes' typically use IoT devices to automate and control what aspects of a home?",multiple_choice,b,"Smart homes integrate devices for lighting, temperature, security, etc.",easy,10,30,Only television,Lighting, temperature, and security,Only cooking appliances,Only cleaning robots
ICT,Basic 7,Emerging Technologies,"What is a 'drone' in the context of emerging technology?",multiple_choice,d,"Drones are unmanned aerial vehicles (UAVs) used for various purposes.",easy,10,30,A type of smartphone,A new type of computer,A robotic pet,An unmanned aerial vehicle (UAV)
ICT,Basic 7,Emerging Technologies,"Which emerging technology is focused on understanding and responding to human language?",multiple_choice,a,"Natural Language Processing (NLP) is a branch of AI focused on this.",medium,10,45,Natural Language Processing (NLP),Virtual Reality (VR),3D Printing,Robotics
ICT,Basic 7,Emerging Technologies,"What is the main goal of 'Genetic Engineering' as an emerging technology?",multiple_choice,b,"Genetic engineering aims to modify an organism's genetic material to change its characteristics.",hard,10,75,To create new computer programs,To modify the genes of organisms for specific traits,To improve internet speed,To design new buildings
ICT,Basic 7,Problem Solving with ICT,"When solving a problem using ICT, what is the first step you should take?",multiple_choice,a,"Defining the problem clearly is crucial before attempting to solve it.",easy,10,30,Define the problem,Start coding immediately,Buy new software,Ask someone else to solve it
ICT,Basic 7,Problem Solving with ICT,"After defining a problem, what is the next logical step in problem-solving with ICT?",multiple_choice,b,"Gathering information helps understand the problem and potential solutions.",easy,10,30,Implement a solution,Gather information and data,Test the solution,Evaluate the solution
ICT,Basic 7,Problem Solving with ICT,"Which ICT tool would be best for organizing and analyzing numerical data to find trends?",multiple_choice,c,"Spreadsheets are ideal for numerical data analysis.",easy,10,30,Word processor,Presentation software,Spreadsheet software,Graphics editor
ICT,Basic 7,Problem Solving with ICT,"If you need to create a visual representation of steps to solve a problem, you might use a:",multiple_choice,d,"Flowcharts use symbols to represent steps and decisions in a process.",medium,10,45,Text document,Chart,Table,Flowchart
ICT,Basic 7,Problem Solving with ICT,"When using ICT to present a solution to a problem, which software would be most suitable?",multiple_choice,b,"Presentation software is designed for communicating information visually.",easy,10,30,Word processor,Presentation software,Spreadsheet,Database
ICT,Basic 7,Problem Solving with ICT,"What does 'debugging' mean in the context of ICT problem solving?",multiple_choice,a,"Debugging is the process of finding and fixing errors in code or systems.",medium,10,45,Finding and fixing errors in a program or system,Creating a new program,Deleting files,Saving a document
ICT,Basic 7,Problem Solving with ICT,"If you are creating instructions for how to use a new software, which ICT tool would be most appropriate?",multiple_choice,c,"Word processors are ideal for creating documents with text and formatting.",easy,10,30,Spreadsheet,Presentation,Word processor,Database
ICT,Basic 7,Problem Solving with ICT,"Which concept involves breaking down a large, complex problem into smaller, more manageable parts?",multiple_choice,d,"Decomposition simplifies problems by breaking them down.",medium,10,60,Abstraction,Pattern recognition,Algorithm,Decomposition
ICT,Basic 7,Problem Solving with ICT,"When designing a solution using ICT, what is the purpose of testing?",multiple_choice,a,"Testing ensures the solution works correctly and identifies any issues.",easy,10,30,To check if the solution works correctly,To make the solution faster,To make the solution more complex,To share the solution with others
ICT,Basic 7,Problem Solving with ICT,"What is an 'algorithm' in problem solving?",multiple_choice,b,"An algorithm is a step-by-step procedure for solving a problem.",medium,10,45,A type of computer hardware,A step-by-step procedure for solving a problem,A computer programming language,A type of software bug
ICT,Basic 7,Problem Solving with ICT,"If you are developing a website to solve a communication problem, which ICT skill is essential?",multiple_choice,c,"Web development skills are needed for creating websites.",easy,10,30,Spreadsheet skills,Word processing skills,Web development skills,Presentation skills
ICT,Basic 7,Problem Solving with ICT,"What is the final stage of the problem-solving process using ICT?",multiple_choice,d,"Evaluating the solution assesses its effectiveness and identifies areas for improvement.",easy,10,30,Defining the problem,Implementing the solution,Testing the solution,Evaluating the solution
ICT,Basic 7,Problem Solving with ICT,"When analyzing data using a spreadsheet, identifying 'outliers' refers to:",multiple_choice,b,"Outliers are data points that are significantly different from others.",medium,10,60,Finding the average value,Identifying data points that are significantly different,Counting the number of cells,Sorting the data
ICT,Basic 7,Problem Solving with ICT,"Which ICT tool can be used for brainstorming ideas and creating mind maps for a problem?",multiple_choice,a,"Mind mapping software or simple drawing tools can assist in brainstorming.",medium,10,45,Mind mapping software,Database software,Video editing software,Antivirus software
ICT,Basic 7,Problem Solving with ICT,"What is 'feedback' important for in problem-solving with ICT?",multiple_choice,c,"Feedback helps refine the solution and improve its effectiveness.",easy,10,30,To make the problem bigger,To ignore the problem,To improve and refine the solution,To make the solution more complicated
ICT,Basic 7,Digital Ethics,"What does 'digital privacy' mean?",multiple_choice,a,"Digital privacy refers to the protection of personal information and activities online.",easy,10,30,The protection of your personal information and activities online,Having a hidden computer,Not using the internet at all,Sharing all your data
ICT,Basic 7,Digital Ethics,"Why is it crucial to respect intellectual property rights online?",multiple_choice,b,"Respecting IP rights acknowledges creators' ownership and avoids legal issues.",medium,10,45,To get more followers,To avoid legal consequences and support creators,To make websites load faster,To make content easier to copy
ICT,Basic 7,Emerging Technologies,"Which of these technologies is used for creating realistic 3D environments that users can interact with?",multiple_choice,b,"Virtual Reality (VR) immerses users in simulated environments.",easy,10,30,Augmented Reality,Virtual Reality,3D Printing,Artificial Intelligence
ICT,Basic 7,Emerging Technologies,"What is 'Machine Learning'?",multiple_choice,c,"Machine Learning is a subset of AI that allows systems to learn from data without explicit programming.",hard,10,75,A new type of computer hardware,A way to make computers faster,A type of artificial intelligence where computers learn from data,A tool for writing documents
ICT,Basic 7,Problem Solving with ICT,"When evaluating a solution, you should compare the outcome with the original:",multiple_choice,d,"Comparing the outcome to the original problem statement helps determine effectiveness.",easy,10,30,Budget,Time limit,Team members,Problem statement
ICT,Basic 7,Problem Solving with ICT,"What is 'computational thinking'?",multiple_choice,a,"Computational thinking involves problem-solving techniques used by computer scientists.",medium,10,60,A set of problem-solving skills and techniques,A new programming language,A type of computer hardware,The process of writing code
ICT,Basic 7,Digital Ethics,"Which action demonstrates 'digital empathy'?",multiple_choice,c,"Digital empathy means understanding and sharing the feelings of others online.",medium,10,45,Ignoring online comments,Posting rude comments,Thinking about how your words affect others online,Sharing private information
ICT,Basic 7,Digital Ethics,"What is the purpose of 'Terms of Service' (ToS) agreements on websites?",multiple_choice,b,"ToS agreements outline the rules and conditions for using a service.",medium,10,45,To collect your personal data,To outline the rules and conditions for using a service,To advertise products,To make the website more attractive
ICT,Basic 7,Emerging Technologies,"Which technology allows devices to understand spoken commands?",multiple_choice,a,"Voice recognition (or speech recognition) converts spoken words into text or commands.",easy,10,30,Voice Recognition,Facial Recognition,Fingerprint Scanning,Gesture Control
ICT,Basic 7,Emerging Technologies,"What is the main purpose of 'Nanotechnology'?",multiple_choice,c,"Nanotechnology involves manipulating matter at the atomic and molecular scale.",hard,10,75,Creating large structures,Developing new computer software,Manipulating materials at an atomic or molecular level,Improving internet speeds
ICT,Basic 7,Problem Solving with ICT,"After implementing a solution, why is it important to 'monitor' it?",multiple_choice,b,"Monitoring ensures the solution continues to work and addresses any new issues.",medium,10,45,To see if it slows down the computer,To ensure it continues to work effectively,To get more clicks,To make it more complex
ICT,Basic 7,Problem Solving with ICT,"What role does 'creativity' play in problem-solving with ICT?",multiple_choice,d,"Creativity helps in generating innovative and unique solutions.",easy,10,30,It makes the problem harder,It is not needed for ICT problems,It slows down the process,It helps in generating innovative solutions
ICT,Basic 7,Digital Ethics,"What is 'data privacy'?",multiple_choice,a,"Data privacy is the right to control who can access and use your personal data.",easy,10,30,The right to control who can access and use your personal data,Having fast internet speed,Storing all your data online,Sharing all your data with companies
ICT,Basic 7,Digital Ethics,"Using ICT resources like public computers or Wi-Fi without permission is an ethical violation known as:",multiple_choice,c,"Unauthorized access is a breach of digital ethics and security.",medium,10,45,Cybersecurity,Digital footprint,Unauthorized access,Data sharing
ICT,Basic 7,Emerging Technologies,"Which of these is a characteristic of 'Quantum Computing'?",multiple_choice,d,"Quantum computing uses quantum-mechanical phenomena like superposition and entanglement.",hard,10,75,Relies on traditional binary bits (0s and 1s),Is a type of cloud storage,Used for everyday tasks like word processing,Uses quantum-mechanical phenomena to solve complex problems
ICT,Basic 7,Emerging Technologies,"'Smartphones' combining various functionalities are an example of:",multiple_choice,a,"Convergence is when different technologies or functions combine into one device.",medium,10,45,Technological convergence,Digital divide,Technological obsolescence,Virtual reality
ICT,Basic 7,Problem Solving with ICT,"When solving a problem, what is 'identifying constraints'?",multiple_choice,b,"Constraints are limitations or restrictions that affect the solution.",medium,10,45,Finding solutions quickly,Identifying limitations or restrictions,Ignoring difficult parts of the problem,Copying existing solutions
ICT,Basic 7,Problem Solving with ICT,"To effectively communicate a complex solution to a non-technical audience, you should:",multiple_choice,c,"Simplifying language and using visuals aids understanding for a non-technical audience.",easy,10,30,Use highly technical jargon,Provide all raw data,Use simple language and visual aids,Speak very quickly
ICT,Basic 7,Digital Ethics,"What is 'acceptable use policy' (AUP) in a school or organization?",multiple_choice,b,"An AUP outlines rules for using computer systems and networks.",medium,10,45,A document that lists available software,A set of rules for using computer systems and networks,A guide for purchasing new computers,A training manual for IT staff
ICT,Basic 7,Digital Ethics,"Why is it unethical to share your password with others?",multiple_choice,a,"Sharing passwords compromises security and can lead to unauthorized access.",easy,10,30,It compromises your account security,It makes your internet faster,It helps others to learn,It is a sign of trust
ICT,Basic 7,Emerging Technologies,"Which of these technologies is often used for creating immersive gaming experiences?",multiple_choice,c,"Both VR and AR are used to enhance gaming experiences.",easy,10,30,3D Printing,Blockchain,Virtual Reality,Biometrics
ICT,Basic 7,Emerging Technologies,"What is a common application of 'Facial Recognition' technology?",multiple_choice,d,"Facial recognition is used for unlocking phones, security, and identification.",easy,10,30,Writing documents,Calculating numbers,Sending emails,Unlocking smartphones or security systems
ICT,Basic 7,Problem Solving with ICT,"What does 'prototyping' mean in problem-solving with ICT?",multiple_choice,a,"Prototyping involves creating a preliminary model to test ideas.",medium,10,60,Creating a preliminary version of a solution for testing,Finalizing the solution,Deleting old files,Writing documentation
ICT,Basic 7,Problem Solving with ICT,"Before implementing a solution, it's wise to consider its potential:",multiple_choice,b,"Anticipating potential risks helps mitigate problems later.",medium,10,45,Color,Size,Risks and side effects,Popularity
ICT,Basic 7,Digital Ethics,"What is the main concern with 'deepfakes' in terms of digital ethics?",multiple_choice,d,"Deepfakes are synthetic media that can be used to create realistic but fabricated content, leading to misinformation.",hard,10,75,They are too expensive to create,They use too much data,They are a new form of digital art,They can be used to spread misinformation and discredit individuals
ICT,Basic 7,Digital Ethics,"What does 'digital divide' refer to?",multiple_choice,a,"The digital divide is the gap between those who have access to technology and those who don't.",medium,10,45,The gap between those who have access to technology and those who do not,A type of internet connection,A software update,A new operating system
ICT,Basic 7,Emerging Technologies,"Which emerging technology is focused on creating intelligent systems that can learn and adapt?",multiple_choice,a,"Artificial Intelligence (AI) encompasses systems that exhibit intelligent behavior.",easy,10,30,Artificial Intelligence (AI),3D Printing,Virtual Reality,Blockchain
ICT,Basic 7,Emerging Technologies,"What is the potential impact of 'Nanotechnology' on medicine?",multiple_choice,c,"Nanotechnology has the potential to revolutionize drug delivery and diagnostics at a molecular level.",hard,10,75,It makes medicine cheaper,It makes medicine taste better,Enabling more precise drug delivery and diagnostics,Making medical instruments heavier
ICT,Basic 7,Problem Solving with ICT,"When evaluating a solution, if it doesn't meet the initial requirements, what should you do?",multiple_choice,d,"If a solution fails, re-evaluating the problem and solution design is necessary.",medium,10,45,Give up,Blame the computer,Force it to work,Revisit the problem definition and solution design
ICT,Basic 7,Problem Solving with ICT,"Which term describes the process of simplifying a problem by focusing on essential details and ignoring irrelevant ones?",multiple_choice,a,"Abstraction helps manage complexity by focusing on the important aspects.",hard,10,75,Abstraction,Decomposition,Pattern recognition,Algorithm design
ICT,Basic 7,Digital Ethics,"What is the importance of 'fair use' in copyright law?",multiple_choice,b,"Fair use allows limited use of copyrighted material without permission for purposes like education or criticism.",hard,10,75,It allows you to use any content for free,It permits limited use of copyrighted material for educational or critical purposes,It means you can share anything on social media,It protects your personal data
ICT,Basic 7,Digital Ethics,"Which of these behaviors is considered 'unethical' online?",multiple_choice,c,"Spreading rumors is a form of online harassment and unethical.",easy,10,30,Helping a friend with homework,Sharing educational videos,Spreading rumors about someone,Reporting inappropriate content
ICT,Basic 7,Emerging Technologies,"Devices like smartwatches and fitness trackers are examples of:",multiple_choice,a,"Wearable technology is designed to be worn on the body.",easy,10,30,Wearable technology,Cloud computing,Robotics,Virtual reality
ICT,Basic 7,Emerging Technologies,"What is the main benefit of 'Cloud Computing'?",multiple_choice,c,"Cloud computing offers on-demand availability of computer system resources, reducing local storage and processing needs.",medium,10,45,It makes your computer run faster,It secures your Wi-Fi connection,It allows access to resources and data over the internet without local storage,It helps you learn programming
ICT,Basic 7,Problem Solving with ICT,"If you encounter an error during the implementation phase, you should:",multiple_choice,a,"Debugging is the process of locating and fixing errors.",easy,10,30,Debug the error,Ignore the error,Restart the entire process,Buy new hardware
ICT,Basic 7,Problem Solving with ICT,"What is a 'solution' in problem-solving?",multiple_choice,d,"A solution is an answer or a means of solving a problem.",easy,10,30,The problem itself,The tools you use,The steps to find the problem,An answer or means of solving a problem
ICT,Basic 7,Digital Ethics,"When conducting online research, what is an ethical responsibility regarding sources?",multiple_choice,c,"It's ethical to verify the credibility of sources to avoid spreading misinformation.",medium,10,45,To only use the first result from a search engine,To copy and paste information without checking,To verify the credibility and accuracy of information,To only use sources you agree with
ICT,Basic 7,Digital Ethics,"What does it mean to be 'accountable' for your actions online?",multiple_choice,b,"Accountability means taking responsibility for the impact of your online behavior.",medium,10,45,To always be online,To take responsibility for the impact of your online behavior,To never make mistakes online,To ignore negative comments
ICT,Basic 7,Digital Ethics,"Which of these is a positive impact of digital ethics on society?",multiple_choice,a,"Digital ethics promote responsible use, fostering a safer online environment.",easy,10,30,Promoting responsible online behavior and safety,Increasing cyberbullying,Encouraging plagiarism,Reducing internet speed
ICT,Basic 7,Digital Ethics,"What ethical consideration is raised by the widespread use of 'tracking cookies' on websites?",multiple_choice,d,"Tracking cookies raise privacy concerns as they collect user data without explicit consent in some cases.",hard,10,75,They make websites load faster,They are only used by advertisers,They enhance user experience by remembering preferences,They raise concerns about user privacy and data collection
ICT,Basic 7,Digital Ethics,"Why is it considered unethical to create fake online profiles?",multiple_choice,c,"Fake profiles can be used for deception, harassment, or spreading misinformation.",medium,10,45,It takes too much time,It makes your internet connection slow,It can be used for deception or harassment,It is a way to be creative
ICT,Basic 7,Digital Ethics,"What ethical principle emphasizes protecting children from harmful online content?",multiple_choice,b,"Child online safety is a critical ethical and legal principle.",easy,10,30,Free speech,Child protection and safety,Data sharing,Online gaming
ICT,Basic 7,Digital Ethics,"What is the ethical responsibility of users regarding online content created by others?",multiple_choice,a,"Respecting intellectual property and obtaining permission when necessary is ethical.",medium,10,45,To respect intellectual property and obtain permission if needed,To share it freely without credit,To modify it without permission,To claim it as your own
ICT,Basic 7,Digital Ethics,"The concept of 'digital footprint' relates to which ethical concern?",multiple_choice,c,"Your digital footprint is permanent and can impact your future reputation.",medium,10,45,Cyberbullying,Illegal downloading,Privacy and online reputation,Fast internet speed
ICT,Basic 7,Digital Ethics,"Why is critical thinking important when consuming information online?",multiple_choice,d,"Critical thinking helps evaluate the accuracy and bias of online information.",easy,10,30,To share everything you see,To avoid thinking too much,To believe everything you read,To evaluate the accuracy and reliability of information
ICT,Basic 7,Digital Ethics,"What is the ethical responsibility concerning online security measures like strong passwords?",multiple_choice,b,"Using strong passwords is an ethical duty to protect your own data and prevent misuse.",easy,10,30,To make your computer faster,To protect your data and prevent unauthorized access,To show off your typing skills,To avoid memorizing complex words
ICT,Basic 7,Digital Ethics,"Which ethical concept addresses the fair access to technology and digital literacy for all?",multiple_choice,c,"Digital equity (or inclusion) aims to bridge the digital divide.",hard,10,75,Digital footprint,Cyberbullying,Digital equity/inclusion,Netiquette
ICT,Basic 7,Digital Ethics,"Why is it unethical to engage in 'doxing' (publishing private information about someone online)?",multiple_choice,a,"Doxing is a severe breach of privacy and can lead to real-world harm.",medium,10,45,It is a severe breach of privacy and can lead to harm,It makes the internet slower,It helps expose criminals,It is a form of free speech
ICT,Basic 7,Digital Ethics,"What does 'digital responsibility' mean?",multiple_choice,d,"Digital responsibility involves being mindful of the impact of your online actions.",easy,10,30,Owning many digital devices,Spending all your time online,Ignoring rules and guidelines,Being accountable and mindful of your online actions
ICT,Basic 7,Digital Ethics,"Ethical considerations regarding 'AI bias' focus on:",multiple_choice,b,"AI bias arises when algorithms perpetuate or amplify societal biases from their training data.",hard,10,75,How fast AI can process data,Ensuring AI systems do not perpetuate unfair discrimination,The cost of AI development,The complexity of AI programming
ICT,Basic 7,Digital Ethics,"What is the ethical stance on 'astroturfing' in online reviews or comments?",multiple_choice,c,"Astroturfing (creating fake grassroots support) is deceptive and unethical.",medium,10,45,It helps businesses grow,It shows genuine support,It is deceptive and unethical as it creates fake support,It is a harmless marketing strategy
ICT,Basic 7,Emerging Technologies,"Which emerging technology enables doctors to perform surgeries remotely?",multiple_choice,a,"Robotics in medicine, specifically tele-surgery, allows remote operations.",medium,10,45,Robotics in surgery,3D printing,Virtual Reality,Wearable technology
ICT,Basic 7,Emerging Technologies,"What is the primary benefit of 'smart grids' in energy management?",multiple_choice,c,"Smart grids optimize electricity delivery, improve efficiency, and reduce waste.",hard,10,75,They produce more energy,They are simpler to manage,They optimize energy distribution and efficiency,They reduce the cost of electricity production
ICT,Basic 7,Emerging Technologies,"'Wearable tech' like smartwatches often include sensors for:",multiple_choice,b,"Wearables commonly have sensors for heart rate, steps, sleep, etc.",easy,10,30,Only time,Health monitoring and fitness tracking,Making phone calls,Browse the internet
ICT,Basic 7,Emerging Technologies,"What is the main promise of '5G technology'?",multiple_choice,d,"5G offers significantly higher speeds, lower latency, and greater capacity for wireless networks.",medium,10,45,Lower internet costs,Simpler phone designs,More secure internet,Faster internet speeds and lower latency
ICT,Basic 7,Emerging Technologies,"'Personalized learning' platforms that adapt to a student's pace and style often use which technology?",multiple_choice,a,"AI and Machine Learning are key to adaptive learning systems.",medium,10,60,Artificial Intelligence (AI),3D Printing,Virtual Reality,Blockchain
ICT,Basic 7,Emerging Technologies,"What is the role of 'drones' in agriculture?",multiple_choice,b,"Drones are used for precision agriculture, including crop monitoring and spraying.",medium,10,45,To plant seeds directly,For crop monitoring, spraying, and mapping,To harvest crops,To water fields automatically
ICT,Basic 7,Emerging Technologies,"Which technology allows you to 'see' a virtual car in your driveway using your phone camera?",multiple_choice,c,"Augmented Reality (AR) overlays digital content onto the real world.",easy,10,30,Virtual Reality (VR),3D Printing,Augmented Reality (AR),Artificial Intelligence
ICT,Basic 7,Emerging Technologies,"'Smart sensors' in an IoT device are used to:",multiple_choice,d,"Sensors collect data about the environment or device's state.",easy,10,30,Process data,Store data,Display data,Collect data from the environment
ICT,Basic 7,Emerging Technologies,"What is 'Bioinformatics'?",multiple_choice,a,"Bioinformatics applies computational tools to manage and analyze biological data.",hard,10,75,The application of computational tools to manage and analyze biological data,A new type of computer,A form of genetic engineering,The study of robots
ICT,Basic 7,Emerging Technologies,"Which emerging technology is changing how manufacturing and product design are done?",multiple_choice,b,"3D printing (additive manufacturing) has revolutionized these fields.",medium,10,45,Robotics,3D Printing,Virtual Reality,Blockchain
ICT,Basic 7,Emerging Technologies,"'Haptic technology' primarily involves what kind of user experience?",multiple_choice,c,"Haptic technology provides tactile feedback (touch sensations).",medium,10,60,Visual,Auditory,Touch/Tactile,Olfactory
ICT,Basic 7,Emerging Technologies,"What is the primary challenge in developing 'Artificial General Intelligence' (AGI)?",multiple_choice,d,"AGI aims for human-level cognitive abilities across various tasks, which is immensely complex.",hard,10,75,The cost of hardware,Lack of data,Slow processing speed,Achieving human-level cognitive abilities across all tasks
ICT,Basic 7,Emerging Technologies,"'Smart cities' leverage emerging technologies primarily to:",multiple_choice,a,"Smart cities use technology to improve urban services and quality of life.",medium,10,45,Improve urban services and quality of life,Build taller buildings,Increase traffic congestion,Replace human workers with robots
ICT,Basic 7,Emerging Technologies,"Which emerging technology focuses on the development of materials at the atomic level?",multiple_choice,b,"Nanotechnology involves manipulation at the nanoscale.",medium,10,45,Biometrics,Nanotechnology,Robotics,Cloud Computing
ICT,Basic 7,Emerging Technologies,"What is 'Edge Computing'?",multiple_choice,c,"Edge computing processes data closer to the source, reducing latency and bandwidth use.",hard,10,75,Storing data in a central cloud server,Processing data far from the source,Processing data closer to the source of data generation,A new type of operating system
ICT,Basic 7,Problem Solving with ICT,"Which stage of problem-solving involves creating a plan or model for the solution?",multiple_choice,b,"Design is the stage where the solution's architecture is planned.",medium,10,45,Defining the problem,Designing the solution,Implementing the solution,Testing the solution
ICT,Basic 7,Problem Solving with ICT,"If a problem involves organizing a large amount of customer contact information, which ICT tool is most suitable?",multiple_choice,c,"Database software is ideal for managing large, structured sets of data.",easy,10,30,Word processor,Spreadsheet,Database software,Presentation software
ICT,Basic 7,Problem Solving with ICT,"What is the purpose of 'documentation' in problem-solving with ICT?",multiple_choice,a,"Documentation records the problem, solution, and usage instructions for future reference.",medium,10,45,To record the problem, solution, and usage instructions,To make the solution more complex,To hide information,To share confidential data
ICT,Basic 7,Problem Solving with ICT,"When encountering a software error, what is a common first step in troubleshooting?",multiple_choice,b,"Restarting the software or device often resolves temporary glitches.",easy,10,30,Immediately reinstall the operating system,Restart the software or device,Buy a new computer,Ignore the error
ICT,Basic 7,Problem Solving with ICT,"Which problem-solving strategy involves identifying similarities between the current problem and previously solved problems?",multiple_choice,c,"Pattern recognition applies known solutions to similar problems.",medium,10,60,Decomposition,Abstraction,Pattern recognition,Algorithm design
ICT,Basic 7,Problem Solving with ICT,"What does 'iterative' mean in the context of ICT problem-solving?",multiple_choice,a,"Iterative problem-solving involves repeating steps or processes, refining the solution with each cycle.",hard,10,75,Repeating steps or processes to refine a solution,Solving a problem in one go,Using only one specific tool,Ignoring feedback
ICT,Basic 7,Problem Solving with ICT,"If you need to create a simple animation to explain a process, which type of ICT tool would be best?",multiple_choice,d,"Simple animation tools or presentation software with animation features can be used.",easy,10,30,Word processor,Spreadsheet,Database,Animation software/Presentation software
ICT,Basic 7,Problem Solving with ICT,"What is the benefit of collaborating with others when solving a complex ICT problem?",multiple_choice,b,"Collaboration brings diverse perspectives and skills to problem-solving.",easy,10,30,It makes the problem harder,It brings diverse perspectives and skills,It speeds up individual work,It allows you to avoid doing work
ICT,Basic 7,Problem Solving with ICT,"What is 'data visualization' and why is it important in problem-solving?",multiple_choice,a,"Data visualization presents data graphically, making trends and insights easier to spot.",medium,10,45,Presenting data graphically to identify trends and patterns,Creating long tables of numbers,Storing large amounts of data,Writing reports with only text
ICT,Basic 7,Problem Solving with ICT,"When selecting an ICT tool for a problem, what key factor should guide your choice?",multiple_choice,c,"The tool's suitability for the specific problem and task is paramount.",easy,10,30,The most expensive tool,The tool your friend uses,The tool's suitability for the specific task and problem,The newest tool available
ICT,Basic 7,Problem Solving with ICT,"What is the purpose of a 'flowchart' in problem-solving?",multiple_choice,b,"Flowcharts visually map out the sequence of steps in a process or algorithm.",medium,10,45,To write a story,To visually represent the steps and decisions in a process,To organize numerical data,To create a presentation
ICT,Basic 7,Problem Solving with ICT,"Before implementing a solution, it's crucial to check for:",multiple_choice,d,"Checking for errors or bugs before implementation prevents issues later.",easy,10,30,Its color,Its popularity,Its weight,Errors or bugs
ICT,Basic 7,Problem Solving with ICT,"If a program crashes repeatedly, what problem-solving technique might you apply?",multiple_choice,a,"Troubleshooting involves systematically diagnosing and solving problems.",easy,10,30,Troubleshooting,Ignoring the problem,Rewriting the entire program,Buying new hardware
ICT,Basic 7,Problem Solving with ICT,"What is the importance of 'user feedback' in the evaluation phase of a solution?",multiple_choice,c,"User feedback provides insights into the solution's effectiveness and usability from a real-world perspective.",medium,10,45,It tells you if the solution is fast,It confirms your own assumptions,It provides real-world insights into the solution's usability and effectiveness,It helps you advertise the solution
ICT,Basic 7,Problem Solving with ICT,"Which concept relates to designing solutions that can be easily updated or scaled up?",multiple_choice,b,"Scalability refers to a system's ability to handle increasing workloads or be easily expanded.",hard,10,75,Efficiency,Scalability,Complexity,Redundancy
ICT,Basic 7,Digital Ethics,"What is the ethical responsibility concerning online advertising and misleading claims?",multiple_choice,c,"Ethical advertising requires honesty and avoiding deceptive practices.",medium,10,45,To make ads catchy,To get as many clicks as possible,To ensure honesty and avoid deceptive claims,To target vulnerable audiences
ICT,Basic 7,Digital Ethics,"What does the 'right to be forgotten' imply in the digital age?",multiple_choice,a,"It implies the right to have personal data removed from public searches under certain conditions.",hard,10,75,The right to have personal data removed from public search results under certain conditions,The right to forget your password,The right to delete your social media accounts instantly,The right to forget how to use technology
ICT,Basic 7,Emerging Technologies,"Which emerging technology is focused on creating a distributed, immutable ledger for secure transactions?",multiple_choice,b,"Blockchain technology provides a secure and transparent record of transactions.",medium,10,45,Artificial Intelligence,Blockchain,Virtual Reality,3D Printing
ICT,Basic 7,Emerging Technologies,"What is the primary goal of 'Human-Computer Interaction' (HCI) research?",multiple_choice,c,"HCI aims to improve the usability and accessibility of technology for humans.",hard,10,75,To make computers faster,To develop new programming languages,To improve the interaction and usability between humans and computers,To make computers smaller
ICT,Basic 7,Problem Solving with ICT,"If a problem requires repetitive calculations on different sets of data, which ICT tool allows for automation?",multiple_choice,a,"Spreadsheet formulas and functions allow for efficient automation of calculations.",medium,10,45,Spreadsheet software with formulas,Word processor,Presentation software,Graphics editor
ICT,Basic 7,Problem Solving with ICT,"What is 'brainstorming' in the context of problem-solving?",multiple_choice,c,"Brainstorming is a technique for generating a large number of ideas rapidly.",easy,10,30,Analyzing existing data,Testing a solution,Generating a large number of ideas,Implementing a solution
ICT,Basic 7,Digital Ethics,"Why is it important to use strong, unique passwords for different online accounts?",multiple_choice,b,"If one account is compromised, unique passwords prevent others from being easily breached.",medium,10,45,To make it easier to remember them,To prevent a breach in one account from affecting others,To speed up login times,To share with family members
ICT,Basic 7,Digital Ethics,"Ethical considerations regarding 'surveillance' in public spaces with CCTV focus on:",multiple_choice,d,"Surveillance raises concerns about privacy versus security.",medium,10,45,The cost of cameras,The quality of video footage,The need for more cameras,The balance between security and individual privacy
ICT,Basic 7,Emerging Technologies,"'Autonomous vehicles' use which set of technologies to operate without human input?",multiple_choice,a,"Autonomous vehicles rely on AI, sensors, machine learning, and navigation systems.",medium,10,45,AI, sensors, machine learning, and GPS,Only GPS,Only human commands,Only pre-programmed routes
ICT,Basic 7,Emerging Technologies,"What is 'Quantum Supremacy'?",multiple_choice,c,"Quantum supremacy is the point where a quantum computer can solve a problem that a classical computer cannot in a feasible timeframe.",hard,10,75,The ability of a computer to be very fast,The largest quantum computer ever built,The point where a quantum computer solves a problem classical computers cannot,A new programming language for quantum computers
ICT,Basic 7,Problem Solving with ICT,"When presenting a solution to a problem using ICT, what should be concise and clear?",multiple_choice,b,"Clarity in communication ensures the solution is understood.",easy,10,30,The problem statement,The explanation of the solution,The list of tools used,The personal opinions of the presenter
ICT,Basic 7,Problem Solving with ICT,"The process of converting a problem solution into a set of instructions that a computer can understand is called:",multiple_choice,d,"This is the essence of programming or coding.",medium,10,45,Debugging,Testing,Analyzing,Coding/Programming
ICT,Basic 7,Digital Ethics,"The ethical dilemma of 'filter bubbles' and 'echo chambers' relates to:",multiple_choice,a,"These phenomena limit exposure to diverse viewpoints, potentially reinforcing existing biases.",hard,10,75,Limited exposure to diverse viewpoints and reinforcement of existing biases,Making online content more relevant to users,Speeding up internet Browse,Promoting balanced news consumption
ICT,Basic 7,Digital Ethics,"What is the ethical implication of sharing unverified news or 'fake news' online?",multiple_choice,c,"Spreading unverified news can lead to public confusion, panic, and erosion of trust.",medium,10,45,It helps people form their own opinions,It makes content more engaging,It can spread misinformation and cause public harm,It is a form of free speech
ICT,Basic 7,Emerging Technologies,"Which technology enables seamless communication between various smart devices in a home?",multiple_choice,b,"The Internet of Things (IoT) facilitates connectivity among smart devices.",easy,10,30,3D Printing,Internet of Things (IoT),Virtual Reality,Nanotechnology
ICT,Basic 7,Emerging Technologies,"What is 'Predictive Analytics'?",multiple_choice,a,"Predictive analytics uses historical data and algorithms to forecast future outcomes.",medium,10,60,Using historical data to forecast future outcomes,Describing past events,Collecting current data,Analyzing only qualitative data
ICT,Basic 7,Problem Solving with ICT,"What is the main purpose of 'User Interface (UI) design' in problem-solving with ICT?",multiple_choice,c,"UI design focuses on making software easy and enjoyable for users to interact with.",hard,10,75,To make the software run faster,To write complex code,To make the software easy and enjoyable for users to interact with,To store large amounts of data
ICT,Basic 7,Problem Solving with ICT,"When a solution requires making calculations and presenting them clearly, you might use a spreadsheet and then:",multiple_choice,d,"Charts from spreadsheets are ideal for visualizing numerical data in presentations.",medium,10,45,Word processor to write a letter,Database to store contact info,Graphics editor to draw a picture,Presentation software to create charts
ICT,Basic 7,Database Basics,Which of the following is an example of a database management system (DBMS)?,multiple_choice,c,MySQL is a popular open-source relational database management system.,medium,10,45,Microsoft Word,Google Chrome,MySQL,Adobe Photoshop
ICT,Basic 7,Database Basics,In a relational database, how are tables typically linked to each other?,multiple_choice,b,Tables are linked using common fields, typically a primary key in one table and a foreign key in another.,hard,15,75,By file name,Using common fields (keys),By their size,By their creation date
ICT,Basic 7,Database Basics,What does "data integrity" mean in the context of databases?,multiple_choice,a,Data integrity refers to the accuracy and consistency of data over its entire life cycle.,medium,10,60,Accuracy and consistency of data,Speed of data retrieval,Amount of data stored,Complexity of queries
ICT,Basic 7,Database Basics,Which database object is used to summarize and present data for printing or viewing?,multiple_choice,d,Reports are used to present data in an organized, readable format.,easy,5,30,Tables,Forms,Queries,Reports
ICT,Basic 7,Database Basics,What is the process of extracting specific information from a database based on criteria?,multiple_choice,a,Querying is the process of retrieving data that matches specified criteria.,medium,10,45,Querying,Reporting,Sorting,Indexing
ICT,Basic 7,Database Basics,In a database, what is a column known as?,multiple_choice,b,A column in a database table is also known as a field or attribute.,easy,5,30,Record,Field,Tuple,Row
ICT,Basic 7,Database Basics,If you wanted to store a student's age, what data type would be most appropriate in a database?,multiple_choice,c,Integer (whole number) is suitable for age.,medium,10,45,Text,Boolean,Integer,Date/Time
ICT,Basic 7,Database Basics,What is a common reason for splitting a large database table into smaller, related tables?,multiple_choice,d,Normalization (splitting tables) reduces data redundancy and improves data integrity.,hard,15,90,To make it slower,To increase redundancy,To make it harder to query,To reduce data redundancy
ICT,Basic 7,Database Basics,Which type of database stores data in tables with rows and columns?,multiple_choice,a,Relational databases are structured with tables, rows, and columns.,medium,10,45,Relational database,Hierarchical database,Network database,Object-oriented database
ICT,Basic 7,Database Basics,What is a foreign key in a database?,multiple_choice,b,A foreign key is a field in one table that refers to the primary key in another table, linking the two.,hard,15,75,A key that opens a database,A field that links to a primary key in another table,A key that prevents access,A key used for encryption
ICT,Basic 7,Database Basics,The process of adding new data into a database is called?,multiple_choice,c,Data entry involves inputting new records or information.,easy,5,30,Searching,Deleting,Data entry,Querying
ICT,Basic 7,Database Basics,Which of these is used to define the structure of a database, including table names, field names, and data types?,multiple_choice,d,The schema or data definition language (DDL) defines the structure.,hard,15,90,Form,Report,Query,Schema
ICT,Basic 7,Database Basics,What is data validation in a database?,multiple_choice,a,Data validation ensures that data entered into the database is accurate and conforms to rules.,medium,10,60,Ensuring data accuracy and consistency,Making data look appealing,Deleting old data,Sorting data
ICT,Basic 7,Database Basics,When you print out a structured list of information from a database, you are generating a?,multiple_choice,b,A report is a structured output of database information.,easy,5,30,Form,Report,Query,Table
ICT,Basic 7,Database Basics,A database can help prevent data duplication, which is also known as?,multiple_choice,c,Data redundancy is the duplication of data.,medium,10,45,Data integrity,Data security,Data redundancy,Data inconsistency
ICT,Basic 7,Programming Concepts,What is a flowchart used for in programming?,multiple_choice,b,Flowcharts visually represent the steps and logic of an algorithm.,medium,10,45,Writing the code,Representing the logic of a program visually,Running the program,Debugging syntax errors
ICT,Basic 7,Programming Concepts,In an algorithm, what does 'input' refer to?,multiple_choice,a,Input is the data that the algorithm receives to process.,easy,5,30,Data given to the program,The result of the program,The steps in the program,Errors in the program
ICT,Basic 7,Programming Concepts,What is a sequence in programming?,multiple_choice,c,Sequence refers to the execution of instructions one after another in order.,medium,10,45,Repeating a block of code,Making decisions,Executing instructions in order,Storing data
ICT,Basic 7,Programming Concepts,Which of these is a low-level programming language directly understood by the computer's CPU?,multiple_choice,b,Assembly language is a low-level language that is very close to machine code.,hard,15,75,Python,Assembly language,Java,Scratch
ICT,Basic 7,Programming Concepts,What is the purpose of comments in programming code?,multiple_choice,d,Comments are for human readers to understand the code; they are ignored by the computer.,medium,10,45,To make the program run faster,To generate errors,To add new features,To explain the code to humans
ICT,Basic 7,Programming Concepts,Which symbol in a flowchart typically represents a process or action?,multiple_choice,a,A rectangle represents a process or calculation step.,medium,10,45,Rectangle,Diamond,Oval,Parallelogram
ICT,Basic 7,Programming Concepts,What is an 'IDE' in programming?,multiple_choice,c,An IDE (Integrated Development Environment) is software that provides comprehensive facilities to computer programmers for software development.,hard,15,90,Internet Data Exchange,Input Device Editor,Integrated Development Environment,Instructional Design Engine
ICT,Basic 7,Programming Concepts,What is the difference between a compiler and an interpreter?,multiple_choice,b,A compiler translates the entire program at once; an interpreter translates and executes line by line.,hard,15,90,A compiler runs code faster; an interpreter runs slower,A compiler translates the whole program at once; an interpreter translates line by line,An interpreter finds errors; a compiler fixes errors,A compiler is for high-level languages; an interpreter is for low-level languages
ICT,Basic 7,Programming Concepts,What is pseudocode?,multiple_choice,a,Pseudocode is an informal high-level description of the operating principle of a computer program or algorithm.,medium,10,60,A high-level description of an algorithm using plain language,A type of programming language,A set of computer errors,A visual representation of data
ICT,Basic 7,Programming Concepts,Which type of error occurs when a program tries to perform an impossible operation, like dividing by zero?,multiple_choice,c,Runtime errors occur during program execution.,hard,15,75,Syntax error,Logic error,Runtime error,Typographical error
ICT,Basic 7,Programming Concepts,What is a 'loop' in programming used for?,multiple_choice,a,Loops repeat a block of code until a certain condition is met.,medium,10,45,To repeat a block of code multiple times,To make decisions,To store values,To display output
ICT,Basic 7,Programming Concepts,The step-by-step process of breaking down a complex problem into smaller, manageable sub-problems is called?,multiple_choice,d,This is problem decomposition or modular programming.,hard,15,90,Debugging,Syntax analysis,Execution,Problem decomposition
ICT,Basic 7,Programming Concepts,What is an array in programming?,multiple_choice,b,An array is a collection of similar data items stored in contiguous memory locations, accessed using an index.,hard,15,90,A single variable,A collection of items of the same data type,A type of loop,A decision-making statement
ICT,Basic 7,Programming Concepts,Which type of error prevents a program from running because it violates the rules of the programming language?,multiple_choice,a,Syntax errors are grammatical mistakes in the code.,medium,10,45,Syntax error,Logic error,Runtime error,Semantic error
ICT,Basic 7,Programming Concepts,What is the purpose of testing a program?,multiple_choice,c,Testing identifies bugs and ensures the program works as expected.,medium,10,45,To make it faster,To make it smaller,To identify and fix errors,To publish it online
ICT,Basic 7,File Management,What is a common extension for a video file?,multiple_choice,d,.mp4 is a widely used video file extension.,easy,5,30,.doc,.xlsx,.ppt,.mp4
ICT,Basic 7,File Management,Which character is commonly used to represent the root directory in a file path (e.g., C:)?',multiple_choice,a,The backslash (\) is commonly used to separate directory names in Windows file paths.,medium,10,45,\,/,:,*
ICT,Basic 7,File Management,What happens when you 'cut' a file?,multiple_choice,c,Cutting a file removes it from its original location and places it in a temporary clipboard for pasting elsewhere.,medium,10,45,It creates a copy of the file,It deletes the file permanently,It removes the file from its original location to be moved,It renames the file
ICT,Basic 7,File Management,Which command is used to change the name of an existing file?,multiple_choice,b,The Rename command changes a file's name.,easy,5,30,Copy,Rename,Move,Delete
ICT,Basic 7,File Management,What is the purpose of creating subfolders within a main folder?,multiple_choice,a,Subfolders help further organize files into categories.,easy,5,30,To further organize files,To increase file size,To make files difficult to find,To share files online
ICT,Basic 7,File Management,Which type of file typically has a .exe extension?,multiple_choice,d,.exe is for executable files (programs).,medium,10,45,Document file,Image file,Audio file,Executable program file
ICT,Basic 7,File Management,What is disk defragmentation?,multiple_choice,a,Defragmentation reorganizes fragmented files on a hard drive to improve performance.,hard,15,75,Reorganizing fragmented files on a disk,Deleting unwanted files,Scanning for viruses,Compressing files
ICT,Basic 7,File Management,What is the full path to a file commonly called?,multiple_choice,c,The full path is often referred to as the file path.,medium,10,45,File name,File extension,File path,File size
ICT,Basic 7,File Management,If you want to quickly find a file on your computer, what tool would you use?,multiple_choice,b,The search function in the operating system helps locate files.,easy,5,30,Printer,Search tool,Scanner,Web browser
ICT,Basic 7,File Management,Which file management operation involves selecting multiple files and treating them as a single unit?,multiple_choice,d,Selecting allows operations to be performed on multiple items at once.,medium,10,45,Renaming,Deleting,Copying,Selecting (multiple files)
ICT,Basic 7,File Management,What is a 'read-only' file attribute?,multiple_choice,a,A read-only file can be viewed but not modified.,medium,10,45,The file can be viewed but not modified,The file cannot be viewed,The file can be deleted only,The file is hidden from view
ICT,Basic 7,File Management,Which unit of measurement is used to indicate the size of a file?,multiple_choice,b,File size is measured in bytes (KB, MB, GB, TB).,easy,5,30,Hertz,Bytes,Pixels,Watts
ICT,Basic 7,File Management,What is an advantage of using cloud storage for file management?,multiple_choice,c,Cloud storage allows access to files from any device with an internet connection.,medium,10,60,Increased local storage,Slower access speeds,Access files from anywhere with internet,Higher security risks
ICT,Basic 7,File Management,What is the primary function of a file system?,multiple_choice,d,A file system organizes and manages how files are stored and retrieved on a storage device.,hard,15,90,To display files,To process files,To share files over a network,To organize and manage files on a storage device
ICT,Basic 7,Computer Networks,Which of the following connects computers in a limited geographical area, such as a school or office building?,multiple_choice,b,A LAN (Local Area Network) covers a small area.,easy,5,30,WAN,LAN,MAN,PAN
ICT,Basic 7,Computer Networks,What is the device that connects your home network to the internet service provider (ISP)'s network?,multiple_choice,a,A modem acts as the bridge between the home network and the ISP.,medium,10,45,Modem,Router,Switch,Hub
ICT,Basic 7,Computer Networks,What does WAN stand for?,multiple_choice,b,WAN stands for Wide Area Network, covering large geographical areas.,easy,5,30,Wireless Area Network,Wide Area Network,Work Area Network,Web Access Network
ICT,Basic 7,Computer Networks,Which network device broadcasts data to all devices connected to it, even if not intended for them?,multiple_choice,a,A hub sends data to all connected devices.,medium,10,45,Hub,Switch,Router,Modem
ICT,Basic 7,Computer Networks,What is the process of translating website names (like www.google.com) into IP addresses?,multiple_choice,c,DNS (Domain Name System) performs this translation.,hard,15,75,HTTP,FTP,DNS resolution,TCP/IP
ICT,Basic 7,Computer Networks,Which protocol is used for transferring files between computers on a network or the internet?,multiple_choice,d,FTP (File Transfer Protocol) is commonly used for this.,medium,10,45,HTTP,SMTP,TCP/IP,FTP
ICT,Basic 7,Computer Networks,What is the main function of a network cable?,multiple_choice,b,Cables provide the physical medium for data transmission.,easy,5,30,To provide wireless connection,To transmit data between devices,To power network devices,To filter network traffic
ICT,Basic 7,Computer Networks,Which security measure encrypts data transmitted over a Wi-Fi network to protect it from unauthorized access?,multiple_choice,a,WPA2/WPA3 are common Wi-Fi security protocols that encrypt data.,medium,10,60,WPA2/WPA3 encryption,Public Wi-Fi,No password,Open network
ICT,Basic 7,Computer Networks,What is the term for a network where all computers have equal capabilities and responsibilities, without a central server?,multiple_choice,c,Peer-to-peer (P2P) networks have no dedicated servers.,medium,10,45,Client-server network,Local Area Network,Peer-to-peer network,Wide Area Network
ICT,Basic 7,Computer Networks,Which type of network connection allows devices to communicate without physical cables?,multiple_choice,d,Wireless networks use radio waves or infrared for communication.,easy,5,30,Wired network,Fiber optic network,Ethernet network,Wireless network
ICT,Basic 7,Computer Networks,What is the main difference between an intranet and the internet?,multiple_choice,a,An intranet is a private network used within an organization, while the Internet is a public global network.,hard,15,90,Intranet is private, Internet is public,Intranet is faster than Internet,Internet is more secure than Intranet,Intranet is for home use, Internet is for business
ICT,Basic 7,Computer Networks,What is a firewall primarily designed to do in a network?,multiple_choice,b,A firewall controls network traffic to protect against unauthorized access and malicious activity.,medium,10,60,To speed up internet connection,To prevent unauthorized access,To store network data,To print documents remotely
ICT,Basic 7,Computer Networks,Which network device operates at the Data Link layer and uses MAC addresses to forward data to specific devices?,multiple_choice,c,A switch is more intelligent than a hub and forwards frames based on MAC addresses.,hard,15,90,Router,Hub,Switch,Modem
ICT,Basic 7,Computer Networks,What is a protocol in the context of computer networks?,multiple_choice,a,A protocol is a set of rules for communication between devices.,medium,10,45,A set of rules for communication,A type of hardware device,A software program,A type of network cable
ICT,Basic 7,Computer Networks,When you share a printer on a network, what are you doing?,multiple_choice,d,You are allowing other computers on the network to use that printer.,easy,5,30,Installing a new printer,Connecting the printer directly to the internet,Making the printer slower,Allowing other computers to use the printer

