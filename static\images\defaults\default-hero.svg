<svg width="1920" height="600" viewBox="0 0 1920 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="heroGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
    </linearGradient>
    
    <!-- Pattern for texture -->
    <pattern id="dots" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
      <circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/>
    </pattern>
  </defs>
  
  <!-- Background -->
  <rect width="1920" height="600" fill="url(#heroGradient)"/>
  
  <!-- Texture overlay -->
  <rect width="1920" height="600" fill="url(#dots)"/>
  
  <!-- Geometric shapes for visual interest -->
  <g opacity="0.1">
    <!-- Large circles -->
    <circle cx="200" cy="150" r="80" fill="white"/>
    <circle cx="1600" cy="400" r="120" fill="white"/>
    <circle cx="800" cy="500" r="60" fill="white"/>
    
    <!-- Triangles -->
    <polygon points="1200,100 1300,100 1250,50" fill="white"/>
    <polygon points="400,450 500,450 450,350" fill="white"/>
    
    <!-- Rectangles -->
    <rect x="1400" y="150" width="100" height="60" rx="10" fill="white"/>
    <rect x="100" y="400" width="80" height="80" rx="15" fill="white"/>
  </g>
  
  <!-- Central content area -->
  <g transform="translate(960, 300)">
    <!-- Education icon -->
    <g fill="white" opacity="0.8">
      <!-- Graduation cap -->
      <path d="M-60,-40 L60,-40 L80,-20 L60,0 L-60,0 L-80,-20 Z"/>
      <rect x="-5" y="-10" width="10" height="30" rx="2"/>
      <circle cx="0" cy="25" r="8"/>
      
      <!-- Books -->
      <g transform="translate(-100, 20)">
        <rect x="0" y="0" width="40" height="25" rx="2" fill="rgba(255,255,255,0.6)"/>
        <rect x="5" y="5" width="30" height="2" fill="rgba(0,0,0,0.1)"/>
        <rect x="5" y="10" width="25" height="2" fill="rgba(0,0,0,0.1)"/>
        <rect x="5" y="15" width="30" height="2" fill="rgba(0,0,0,0.1)"/>
      </g>
      
      <g transform="translate(60, 20)">
        <rect x="0" y="0" width="40" height="25" rx="2" fill="rgba(255,255,255,0.6)"/>
        <rect x="5" y="5" width="30" height="2" fill="rgba(0,0,0,0.1)"/>
        <rect x="5" y="10" width="25" height="2" fill="rgba(0,0,0,0.1)"/>
        <rect x="5" y="15" width="30" height="2" fill="rgba(0,0,0,0.1)"/>
      </g>
    </g>
    
    <!-- Text placeholder -->
    <text x="0" y="80" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" font-weight="bold" opacity="0.9">
      Pentora Learning Platform
    </text>
    <text x="0" y="110" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" opacity="0.7">
      Empowering learners through quality education
    </text>
  </g>
  
  <!-- Decorative elements -->
  <g opacity="0.3">
    <!-- Floating elements -->
    <circle cx="300" cy="100" r="4" fill="white">
      <animate attributeName="cy" values="100;120;100" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="1500" cy="200" r="6" fill="white">
      <animate attributeName="cy" values="200;180;200" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="1700" cy="500" r="3" fill="white">
      <animate attributeName="cy" values="500;520;500" dur="2.5s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg>
