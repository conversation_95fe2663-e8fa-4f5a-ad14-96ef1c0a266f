# Generated by Django 5.2.1 on 2025-07-05 14:54

import django.core.validators
import django.utils.timezone
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ABTestAssignment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('session_key', models.CharField(blank=True, max_length=40, null=True)),
                ('assigned_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
        ),
        migrations.CreateModel(
            name='ABTestVariant',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('test_name', models.CharField(max_length=100)),
                ('variant_name', models.CharField(max_length=50)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('traffic_percentage', models.PositiveIntegerField(default=50, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='ConversionFunnel',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('session_key', models.CharField(blank=True, max_length=40, null=True)),
                ('stage', models.CharField(choices=[('visitor', 'Visitor'), ('signup', 'Sign Up'), ('first_quiz', 'First Quiz'), ('first_lesson', 'First Lesson'), ('active_learner', 'Active Learner'), ('subscriber', 'Subscriber')], max_length=20)),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('metadata', models.JSONField(blank=True, default=dict)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='ErrorLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('error_type', models.CharField(max_length=100)),
                ('error_message', models.TextField()),
                ('stack_trace', models.TextField(blank=True)),
                ('request_url', models.URLField(blank=True)),
                ('request_method', models.CharField(blank=True, max_length=10)),
                ('user_agent', models.TextField(blank=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('resolved', models.BooleanField(default=False)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='PageVisit',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('session_key', models.CharField(blank=True, max_length=40, null=True)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField()),
                ('page_url', models.URLField()),
                ('page_title', models.CharField(blank=True, max_length=200)),
                ('referrer', models.URLField(blank=True, null=True)),
                ('country', models.CharField(blank=True, max_length=100)),
                ('city', models.CharField(blank=True, max_length=100)),
                ('region', models.CharField(blank=True, max_length=100)),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('time_on_page', models.PositiveIntegerField(blank=True, help_text='Time spent on page in seconds', null=True)),
                ('device_type', models.CharField(choices=[('desktop', 'Desktop'), ('mobile', 'Mobile'), ('tablet', 'Tablet'), ('bot', 'Bot')], default='desktop', max_length=20)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='SystemMetrics',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('cpu_usage', models.FloatField(help_text='CPU usage percentage')),
                ('memory_usage', models.FloatField(help_text='Memory usage percentage')),
                ('memory_total', models.BigIntegerField(help_text='Total memory in bytes')),
                ('memory_used', models.BigIntegerField(help_text='Used memory in bytes')),
                ('db_connections', models.PositiveIntegerField(default=0)),
                ('db_size', models.BigIntegerField(blank=True, help_text='Database size in bytes', null=True)),
                ('disk_usage', models.FloatField(help_text='Disk usage percentage')),
                ('disk_total', models.BigIntegerField(help_text='Total disk space in bytes')),
                ('disk_used', models.BigIntegerField(help_text='Used disk space in bytes')),
                ('active_users', models.PositiveIntegerField(default=0)),
                ('total_users', models.PositiveIntegerField(default=0)),
                ('page_views_last_hour', models.PositiveIntegerField(default=0)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='SystemPerformanceMetrics',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('avg_response_time', models.FloatField(help_text='Average response time in seconds')),
                ('max_response_time', models.FloatField(help_text='Maximum response time in seconds')),
                ('min_response_time', models.FloatField(help_text='Minimum response time in seconds')),
                ('avg_query_time', models.FloatField(help_text='Average database query time in seconds')),
                ('total_queries', models.PositiveIntegerField()),
                ('slow_queries', models.PositiveIntegerField(default=0)),
                ('memory_usage_mb', models.PositiveIntegerField(help_text='Memory usage in MB')),
                ('cpu_usage_percent', models.FloatField(validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('total_requests', models.PositiveIntegerField()),
                ('error_rate', models.FloatField(validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='UserActivity',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('activity_type', models.CharField(choices=[('login', 'Login'), ('logout', 'Logout'), ('quiz_start', 'Quiz Started'), ('quiz_complete', 'Quiz Completed'), ('lesson_view', 'Lesson Viewed'), ('profile_update', 'Profile Updated'), ('subscription_change', 'Subscription Changed')], max_length=50)),
                ('description', models.TextField(blank=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='UserEngagementMetrics',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('date', models.DateField()),
                ('total_time_spent', models.PositiveIntegerField(default=0, help_text='Total time in seconds')),
                ('session_count', models.PositiveIntegerField(default=0)),
                ('avg_session_duration', models.PositiveIntegerField(default=0, help_text='Average session duration in seconds')),
                ('pages_viewed', models.PositiveIntegerField(default=0)),
                ('quizzes_taken', models.PositiveIntegerField(default=0)),
                ('lessons_completed', models.PositiveIntegerField(default=0)),
                ('quiz_success_rate', models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
    ]
