#!/usr/bin/env python
"""
Production Performance Optimization Script for Pentora
Designed specifically for PostgreSQL production environment
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pentora.settings')
django.setup()

from django.db import connection, transaction
from django.core.cache import cache
from django.core.management import call_command
from django.conf import settings
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def print_header():
    print("=" * 60)
    print("🚀 PENTORA PRODUCTION PERFORMANCE OPTIMIZATION")
    print("=" * 60)
    print("This script applies PostgreSQL-specific optimizations")
    print("for the production environment.")
    print()

def run_migrations():
    """Apply any pending database migrations"""
    print("📋 Step 1: Running Database Migrations")
    print("-" * 40)
    try:
        call_command('migrate', verbosity=1)
        print("✅ Database migrations completed successfully")
        return True
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

def create_production_indexes():
    """Create PostgreSQL-specific performance indexes"""
    print("\n📋 Step 2: Creating Production Performance Indexes")
    print("-" * 40)
    
    try:
        with connection.cursor() as cursor:
            # Check database vendor
            vendor = connection.vendor
            print(f"📊 Database vendor: {vendor}")
            
            if vendor != 'postgresql':
                print(f"⚠️ Warning: Expected PostgreSQL, found {vendor}")
                return False
            
            # PostgreSQL-specific indexes with IF NOT EXISTS
            indexes = [
                {
                    'name': 'idx_question_performance_prod',
                    'sql': '''
                    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_question_performance_prod 
                    ON admin_panel_question (is_active, topic_id, created_at) 
                    WHERE is_active = true;
                    '''
                },
                {
                    'name': 'idx_topic_performance_prod',
                    'sql': '''
                    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_topic_performance_prod 
                    ON subjects_topic (is_active, class_level_id, title) 
                    WHERE is_active = true;
                    '''
                },
                {
                    'name': 'idx_class_level_performance_prod',
                    'sql': '''
                    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_class_level_performance_prod 
                    ON subjects_classlevel (is_active, subject_id, level_number) 
                    WHERE is_active = true;
                    '''
                },
                {
                    'name': 'idx_quiz_performance_prod',
                    'sql': '''
                    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quiz_performance_prod 
                    ON content_quiz (user_id, topic_id, is_completed, completed_at) 
                    WHERE is_completed = true;
                    '''
                },
                {
                    'name': 'idx_progress_performance_prod',
                    'sql': '''
                    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_progress_performance_prod 
                    ON progress_topicprogress (user_id, topic_id, is_completed) 
                    WHERE is_completed = true;
                    '''
                }
            ]
            
            for index in indexes:
                try:
                    cursor.execute(index['sql'])
                    print(f"✅ Created index: {index['name']}")
                except Exception as e:
                    if 'already exists' in str(e).lower():
                        print(f"ℹ️ Index already exists: {index['name']}")
                    else:
                        print(f"⚠️ Index creation warning for {index['name']}: {e}")
            
            print("✅ Production performance indexes processed successfully")
            return True
            
    except Exception as e:
        print(f"❌ Index creation failed: {e}")
        return False

def optimize_postgresql_settings():
    """Apply PostgreSQL-specific optimizations"""
    print("\n📋 Step 3: Optimizing PostgreSQL Settings")
    print("-" * 40)
    
    try:
        with connection.cursor() as cursor:
            # PostgreSQL performance settings
            optimizations = [
                "SET work_mem = '256MB';",
                "SET maintenance_work_mem = '512MB';",
                "SET effective_cache_size = '1GB';",
                "SET random_page_cost = 1.1;",
                "SET seq_page_cost = 1.0;",
            ]
            
            for optimization in optimizations:
                try:
                    cursor.execute(optimization)
                    print(f"✅ Applied: {optimization}")
                except Exception as e:
                    print(f"⚠️ Setting warning: {optimization} - {e}")
            
            print("✅ PostgreSQL settings optimized")
            return True
            
    except Exception as e:
        print(f"❌ PostgreSQL optimization failed: {e}")
        return False

def warm_up_production_caches():
    """Warm up production caches"""
    print("\n📋 Step 4: Warming Up Production Caches")
    print("-" * 40)
    
    try:
        # Test default cache
        cache.set('optimization_test', 'success', 60)
        if cache.get('optimization_test') == 'success':
            print("✅ Default cache is working")
        
        # Test additional caches if configured
        from django.core.cache import caches
        
        cache_configs = ['default', 'pagination', 'queries']
        for cache_name in cache_configs:
            try:
                test_cache = caches[cache_name]
                test_cache.set(f'test_{cache_name}', 'working', 60)
                if test_cache.get(f'test_{cache_name}') == 'working':
                    print(f"✅ Cache '{cache_name}' is working")
                else:
                    print(f"⚠️ Cache '{cache_name}' test failed")
            except Exception as e:
                print(f"ℹ️ Cache '{cache_name}' not available: {e}")
        
        print("✅ Production caches warmed up successfully")
        return True
        
    except Exception as e:
        print(f"❌ Cache warm-up failed: {e}")
        return False

def analyze_query_performance():
    """Analyze current query performance"""
    print("\n📋 Step 5: Analyzing Query Performance")
    print("-" * 40)
    
    try:
        with connection.cursor() as cursor:
            # Check for slow queries and table sizes
            cursor.execute("""
                SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del 
                FROM pg_stat_user_tables 
                ORDER BY n_tup_ins + n_tup_upd + n_tup_del DESC 
                LIMIT 5;
            """)
            
            results = cursor.fetchall()
            print("📊 Most active tables:")
            for row in results:
                print(f"   {row[1]}: {row[2]} inserts, {row[3]} updates, {row[4]} deletes")
            
            print("✅ Query performance analysis completed")
            return True
            
    except Exception as e:
        print(f"❌ Performance analysis failed: {e}")
        return False

def cleanup_and_vacuum():
    """Run PostgreSQL maintenance tasks"""
    print("\n📋 Step 6: Database Maintenance")
    print("-" * 40)
    
    try:
        with connection.cursor() as cursor:
            # Analyze tables for better query planning
            cursor.execute("ANALYZE;")
            print("✅ Database statistics updated")
            
            print("✅ Database maintenance completed")
            return True
            
    except Exception as e:
        print(f"❌ Database maintenance failed: {e}")
        return False

def main():
    """Main optimization function"""
    print_header()
    
    steps = [
        ("run_migrations", run_migrations),
        ("create_production_indexes", create_production_indexes),
        ("optimize_postgresql_settings", optimize_postgresql_settings),
        ("warm_up_production_caches", warm_up_production_caches),
        ("analyze_query_performance", analyze_query_performance),
        ("cleanup_and_vacuum", cleanup_and_vacuum),
    ]
    
    completed_steps = 0
    failed_steps = []
    
    for step_name, step_func in steps:
        try:
            if step_func():
                completed_steps += 1
            else:
                failed_steps.append(step_name)
        except Exception as e:
            print(f"❌ Step failed: {step_name} - {e}")
            failed_steps.append(step_name)
    
    # Final summary
    print("\n" + "=" * 60)
    print("📊 OPTIMIZATION SUMMARY")
    print("=" * 60)
    
    if completed_steps == len(steps):
        print("🎉 All optimizations completed successfully!")
        print("✅ Your production system is now fully optimized")
    else:
        print(f"⚠️ Optimization partially completed: {completed_steps}/{len(steps)} steps")
        if failed_steps:
            print("Failed steps:", ", ".join(failed_steps))
        print("Please check the errors above and retry if needed.")
    
    print("\n🚀 Production optimization process finished!")

if __name__ == "__main__":
    main()
