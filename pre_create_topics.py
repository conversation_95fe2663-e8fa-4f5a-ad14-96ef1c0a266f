#!/usr/bin/env python3
"""
Pre-create topics to avoid database conflicts during CSV import
"""
import os
import sys
import django

# Setup Django
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pentora_platform.settings')
django.setup()

from subjects.models import Subject, ClassLevel, Topic

def create_topics():
    """Pre-create all topics to avoid conflicts"""
    
    print("🔧 PRE-CREATING TOPICS TO AVOID CONFLICTS...")
    print("=" * 50)
    
    topics_data = {
        "Career Technology": {
            "Grade 9": ['Agriculture & Animal Production', 'Entrepreneurship & Career Development', 'Home Economics & Textile Work', 'Pre-Technical Skills'],
        },
        "Computing": {
            "Grade 9": ['Emailing & Sharing Information', 'Spreadsheet Application', 'The Internet and Web Browsers', 'Word Processing Application'],
        },
        "English Language": {
            "Grade 9": ['Grammar', 'Literature', 'Reading Comprehension', 'Spelling and Punctuation', 'Vocabulary', 'Writing'],
        },
        "Integrated Science": {
            "Grade 9": ['Acids Bases and Salt', 'Dentition in Humans', 'Digestive System', 'Ecology and Environment', 'Energy', 'Farm Animals', 'Farm Tools', 'Food Preservation', 'Human Body Systems', 'Life Cycle of a Mosquito', 'Measurements', 'Pests and Diseases in Crops', 'Plant Nutrition', 'Reproduction', 'Soil', 'Soil & Water Conservation', 'Soil Types', 'Sources of Electricity', 'States of Matter', 'The Atom', 'The Solar System'],
        },
        "Mathematics": {
            "Grade 9": ['Algebraic Expressions and Manipulation', 'Algebraic Fractions', 'Area and Perimeter of Plane Shapes', 'Consumer Mathematics', 'Coordinate Geometry', 'Factorization', 'Geometric Transformations', 'Indices and Logarithms', 'Linear Inequalities', 'Mensuration', 'Probability', 'Quadratic Equations', 'Sequences and Series', 'Sets', 'Simultaneous Equations', 'Statistics', 'Statistics and Probability', 'Surds and Radicals', 'Trigonometry', 'Volume and Surface Area of Solids'],
        },
        "Religious & Moral Education": {
            "Grade 9": ['Environmental Stewardship', 'Moral Values & Character Formation', 'Religious Beliefs & Practices', 'Rights', 'Social Ethics & Relationships'],
        },
        "Social Studies": {
            "Grade 9": ['Conflict & Peacebuilding', 'Culture & National Identity', 'Geography & Environment', 'Governance & Democracy', 'Government Institutions & Leadership', 'Social & Economic Development'],
        },
    }
    
    created_count = 0
    
    for subject_name, class_levels in topics_data.items():
        print(f"\n📚 Processing {subject_name}...")
        
        # Get or create subject
        subject, created = Subject.objects.get_or_create(
            name=subject_name,
            defaults={
                'description': f'Auto-created subject for {subject_name}',
                'icon': '📚',
                'color': '#3B82F6',
                'order': 1
            }
        )
        if created:
            print(f"   ✅ Created subject: {subject_name}")
        
        for class_level_name, topics in class_levels.items():
            print(f"   📋 Processing {class_level_name}...")
            
            # Get or create class level
            class_level, created = ClassLevel.objects.get_or_create(
                subject=subject,
                name=class_level_name,
                defaults={
                    'level_number': 9,  # Assuming Grade 9
                    'description': f'Auto-created class level for {class_level_name}',
                    'pass_percentage': 60
                }
            )
            if created:
                print(f"      ✅ Created class level: {class_level_name}")
            
            # Create topics with proper ordering
            for i, topic_title in enumerate(topics, 1):
                topic, created = Topic.objects.get_or_create(
                    class_level=class_level,
                    title=topic_title,
                    defaults={
                        'description': f'Auto-created topic for {topic_title}',
                        'order': i,  # Use sequential order
                        'difficulty_level': 'beginner',
                        'estimated_duration': 30,
                        'is_active': True
                    }
                )
                if created:
                    print(f"         ✅ Created topic {i:2d}: {topic_title}")
                    created_count += 1
                else:
                    print(f"         ℹ️  Topic exists {i:2d}: {topic_title}")
    
    print(f"\n🎉 COMPLETED!")
    print(f"   Created {created_count} new topics")
    print(f"   Now you can upload your CSV without conflicts!")

if __name__ == "__main__":
    create_topics()
