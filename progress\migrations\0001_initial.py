# Generated by Django 5.2.1 on 2025-07-05 14:54

import django.core.validators
import django.utils.timezone
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Achievement',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('achievement_type', models.CharField(choices=[('first_quiz', 'First Quiz Completed'), ('first_test', 'First Test Completed'), ('level_complete', 'Level Completed'), ('perfect_score', 'Perfect Score'), ('study_streak', 'Study Streak'), ('fast_learner', 'Fast Learner'), ('persistent', 'Persistent Learner')], max_length=20)),
                ('title', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('icon', models.Char<PERSON>ield(blank=True, max_length=50)),
                ('earned_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('points', models.PositiveIntegerField(default=10)),
            ],
            options={
                'verbose_name': 'Achievement',
                'verbose_name_plural': 'Achievements',
                'db_table': 'achievements',
                'ordering': ['-earned_at'],
            },
        ),
        migrations.CreateModel(
            name='StudyNoteProgress',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_read', models.BooleanField(default=False)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Study Note Progress',
                'verbose_name_plural': 'Study Note Progress',
                'db_table': 'study_note_progress',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='StudySession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('started_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('ended_at', models.DateTimeField(blank=True, null=True)),
                ('duration', models.PositiveIntegerField(default=0, help_text='Duration in minutes')),
                ('notes_viewed', models.BooleanField(default=False)),
                ('quiz_taken', models.BooleanField(default=False)),
                ('test_taken', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name': 'Study Session',
                'verbose_name_plural': 'Study Sessions',
                'db_table': 'study_sessions',
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='TopicProgress',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_started', models.BooleanField(default=False)),
                ('is_completed', models.BooleanField(default=False)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('notes_read', models.BooleanField(default=False)),
                ('quiz_completed', models.BooleanField(default=False)),
                ('test_completed', models.BooleanField(default=False)),
                ('best_quiz_score', models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('best_test_score', models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('study_time', models.PositiveIntegerField(default=0, help_text='Study time in minutes')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Topic Progress',
                'verbose_name_plural': 'Topic Progress',
                'db_table': 'topic_progress',
                'ordering': ['topic__order'],
            },
        ),
        migrations.CreateModel(
            name='UserProgress',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_started', models.BooleanField(default=False)),
                ('is_completed', models.BooleanField(default=False)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('topics_completed', models.PositiveIntegerField(default=0)),
                ('total_topics', models.PositiveIntegerField(default=0)),
                ('quizzes_taken', models.PositiveIntegerField(default=0)),
                ('tests_taken', models.PositiveIntegerField(default=0)),
                ('final_score', models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('passed', models.BooleanField(default=False)),
                ('total_study_time', models.PositiveIntegerField(default=0, help_text='Total study time in minutes')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'User Progress',
                'verbose_name_plural': 'User Progress',
                'db_table': 'user_progress',
                'ordering': ['-updated_at'],
            },
        ),
    ]
