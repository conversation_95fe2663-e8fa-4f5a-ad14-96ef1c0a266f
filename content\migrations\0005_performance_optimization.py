# Generated migration for performance optimization

from django.db import migrations, models


def create_performance_indexes(apps, schema_editor):
    """Create additional performance indexes for better query performance"""
    db_alias = schema_editor.connection.alias

    with schema_editor.connection.cursor() as cursor:
        try:
            # Additional composite indexes for complex queries
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_question_complex_filter 
                ON questions(topic_id, is_active, question_type, created_at DESC);
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_question_search_optimized 
                ON questions(is_active, topic_id) 
                WHERE is_active = true;
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_topic_hierarchy 
                ON topics(class_level_id, is_active, title);
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_class_level_hierarchy 
                ON class_levels(subject_id, is_active, level_number);
            """)
            
            # Indexes for duplicate detection performance
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_question_duplicate_check 
                ON questions(is_active, length(question_text), topic_id) 
                WHERE is_active = true;
            """)
            
            print("✅ Created performance optimization indexes")
            
        except Exception as e:
            print(f"⚠️ Could not create performance indexes: {e}")


def remove_performance_indexes(apps, schema_editor):
    """Remove performance indexes"""
    with schema_editor.connection.cursor() as cursor:
        try:
            cursor.execute("DROP INDEX IF EXISTS idx_question_complex_filter;")
            cursor.execute("DROP INDEX IF EXISTS idx_question_search_optimized;")
            cursor.execute("DROP INDEX IF EXISTS idx_topic_hierarchy;")
            cursor.execute("DROP INDEX IF EXISTS idx_class_level_hierarchy;")
            cursor.execute("DROP INDEX IF EXISTS idx_question_duplicate_check;")
            print("✅ Removed performance optimization indexes")
        except Exception as e:
            print(f"⚠️ Could not remove performance indexes: {e}")


class Migration(migrations.Migration):

    dependencies = [
        ('content', '0004_merge_20250713_0137'),
    ]

    operations = [
        migrations.RunPython(create_performance_indexes, remove_performance_indexes),
    ]
