{% extends 'base.html' %}

{% block title %}Password Reset Complete - Pentora{% endblock %}

{% block extra_css %}
<style>
    .complete-container {
        background: linear-gradient(135deg, #F9FAFB 0%, #E5E7EB 100%);
        min-height: calc(100vh - 80px);
    }
    
    .complete-card {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        border: 1px solid #E5E7EB;
    }
    
    .success-icon {
        width: 120px;
        height: 120px;
        background: linear-gradient(135deg, #22C55E, #16A34A);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        box-shadow: 0 20px 40px -10px rgba(34, 197, 94, 0.4);
        animation: successPulse 2s infinite;
    }
    
    @keyframes successPulse {
        0%, 100% { 
            transform: scale(1);
            box-shadow: 0 20px 40px -10px rgba(34, 197, 94, 0.4);
        }
        50% { 
            transform: scale(1.05);
            box-shadow: 0 25px 50px -10px rgba(34, 197, 94, 0.6);
        }
    }
    
    .btn-primary-custom {
        background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
        border: none;
        border-radius: 0.75rem;
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        color: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    
    .btn-primary-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 30px -5px rgba(59, 130, 246, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .feature-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        background: #F9FAFB;
        border-radius: 0.75rem;
        border-left: 4px solid #22C55E;
        transition: all 0.2s ease;
    }
    
    .feature-item:hover {
        background: #F0FDF4;
        transform: translateX(4px);
    }
    
    .feature-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #22C55E, #16A34A);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        flex-shrink: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="complete-container flex items-center justify-center px-4 py-8">
    <div class="w-full max-w-lg">
        <!-- Success Header -->
        <div class="text-center mb-8">
            <div class="success-icon">
                <i class="fas fa-check text-white text-4xl"></i>
            </div>
            <h1 class="text-4xl font-bold text-gray-800 mb-3">Password Reset Complete!</h1>
            <p class="text-gray-600 text-lg">Your password has been successfully updated</p>
        </div>

        <!-- Success Card -->
        <div class="complete-card p-8">
            <div class="text-center mb-8">
                <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                    <div class="flex items-center justify-center mb-3">
                        <i class="fas fa-shield-check text-green-600 text-2xl mr-3"></i>
                        <h2 class="text-xl font-semibold text-green-800">Your Account is Secure</h2>
                    </div>
                    <p class="text-green-700">
                        Your new password has been set and your account is now secure. 
                        You can sign in immediately using your new credentials.
                    </p>
                </div>

                <!-- Sign In Button -->
                <a href="{% url 'users:login' %}" class="btn-primary-custom w-full mb-6">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Sign In to Your Account
                </a>
            </div>

            <!-- What's Next Section -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 text-center">
                    <i class="fas fa-rocket text-blue-500 mr-2"></i>
                    Ready to Continue Learning?
                </h3>
                
                <div class="space-y-3">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-book text-white text-sm"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800">Explore Subjects</h4>
                            <p class="text-sm text-gray-600">Browse our comprehensive curriculum</p>
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line text-white text-sm"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800">Track Progress</h4>
                            <p class="text-sm text-gray-600">Monitor your learning achievements</p>
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-users text-white text-sm"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800">Join Community</h4>
                            <p class="text-sm text-gray-600">Connect with fellow learners</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alternative Actions -->
            <div class="border-t border-gray-200 pt-6">
                <div class="text-center space-y-3">
                    <p class="text-sm text-gray-600 mb-4">Need to do something else?</p>
                    
                    <div class="flex flex-col sm:flex-row gap-3 justify-center">
                        <a href="{% url 'core:home' %}" class="btn btn-outline border-2 border-gray-200 hover:border-blue-300 hover:bg-blue-50 text-gray-700 hover:text-blue-600 font-medium py-2 px-4 rounded-lg">
                            <i class="fas fa-home mr-2"></i>
                            Go to Homepage
                        </a>
                        
                        <a href="{% url 'core:contact' %}" class="btn btn-outline border-2 border-gray-200 hover:border-green-300 hover:bg-green-50 text-gray-700 hover:text-green-600 font-medium py-2 px-4 rounded-lg">
                            <i class="fas fa-question-circle mr-2"></i>
                            Get Help
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Tips -->
        <div class="mt-8">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div class="flex items-center justify-center mb-3">
                    <i class="fas fa-lightbulb text-blue-600 mr-2"></i>
                    <span class="font-medium text-blue-800">Security Tips for the Future</span>
                </div>
                <div class="text-sm text-blue-700 space-y-2">
                    <p>• <strong>Remember your new password</strong> - Store it securely</p>
                    <p>• <strong>Keep it private</strong> - Never share your password with anyone</p>
                    <p>• <strong>Use unique passwords</strong> - Don't reuse this password elsewhere</p>
                    <p>• <strong>Update regularly</strong> - Consider changing it periodically</p>
                </div>
            </div>
        </div>

        <!-- Footer Message -->
        <div class="mt-6 text-center">
            <p class="text-sm text-gray-500">
                Welcome back to Pentora! We're excited to continue your learning journey.
            </p>
        </div>
    </div>
</div>
{% endblock %}
