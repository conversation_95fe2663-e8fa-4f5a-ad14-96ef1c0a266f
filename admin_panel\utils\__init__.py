# Admin panel utilities

def get_quiz_settings():
    """
    Get quiz settings for the application
    """
    return {
        'default_questions_per_quiz': 10,
        'quiz_questions_per_topic': 10,
        'default_time_limit': 300,  # 5 minutes
        'quiz_time_limit': 600,  # 10 minutes total quiz time
        'question_time_limit': 45,  # 45 seconds per question
        'allow_retakes': True,
        'show_correct_answers': True,
        'randomize_questions': True,
        'randomize_answers': True,
        'shuffle_questions': True,
        'shuffle_answers': True,
        'allow_question_skip': False,
        'show_progress_bar': True,
        'minimum_pass_percentage': 50,  # 50% to pass
    }
