{% extends 'admin_panel/base.html' %}
{% load static %}

{% block title %}User Feedback Management{% endblock %}
{% block page_title %}User Feedback Management{% endblock %}

{% block extra_css %}
<style>
    .feedback-card {
        transition: all 0.3s ease;
        border-radius: 8px;
    }
    .feedback-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    .rating-stars {
        color: #fbbf24;
    }
    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }
    .resolved { background: #dcfce7; color: #166534; }
    .unresolved { background: #fef3c7; color: #92400e; }
    .type-badge {
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 11px;
        font-weight: 500;
    }
    .bug-report { background: #fee2e2; color: #dc2626; }
    .feature-request { background: #dbeafe; color: #2563eb; }
    .improvement { background: #f3e8ff; color: #7c3aed; }
    .general { background: #f0f9ff; color: #0369a1; }
    .stats-card {
        background: linear-gradient(135deg, var(--color-start), var(--color-end));
        color: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Header Actions -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <p class="text-muted mb-0">Manage and respond to user feedback</p>
    </div>
    <div>
        <a href="{% url 'core:feedback_analytics' %}" class="btn btn-info">
            <i class="fas fa-chart-bar"></i> Analytics
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card" style="--color-start: #3b82f6; --color-end: #1d4ed8;">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-1">{{ total_feedback }}</h3>
                    <p class="mb-0 opacity-90">Total Feedback</p>
                </div>
                <div class="fs-1 opacity-75">
                    <i class="fas fa-comments"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card" style="--color-start: #10b981; --color-end: #059669;">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-1">{{ resolved_feedback }}</h3>
                    <p class="mb-0 opacity-90">Resolved</p>
                </div>
                <div class="fs-1 opacity-75">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card" style="--color-start: #f59e0b; --color-end: #d97706;">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-1">{{ unresolved_feedback }}</h3>
                    <p class="mb-0 opacity-90">Unresolved</p>
                </div>
                <div class="fs-1 opacity-75">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card" style="--color-start: #8b5cf6; --color-end: #7c3aed;">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-1">{{ avg_rating }}</h3>
                    <p class="mb-0 opacity-90">Avg Rating</p>
                </div>
                <div class="fs-1 opacity-75">
                    <i class="fas fa-star"></i>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Status</label>
                    <select name="status" class="form-select">
                        <option value="">All Status</option>
                        <option value="resolved" {% if current_filters.status == 'resolved' %}selected{% endif %}>Resolved</option>
                        <option value="unresolved" {% if current_filters.status == 'unresolved' %}selected{% endif %}>Unresolved</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Type</label>
                    <select name="type" class="form-select">
                        <option value="">All Types</option>
                        <option value="bug_report" {% if current_filters.type == 'bug_report' %}selected{% endif %}>Bug Report</option>
                        <option value="feature_request" {% if current_filters.type == 'feature_request' %}selected{% endif %}>Feature Request</option>
                        <option value="improvement" {% if current_filters.type == 'improvement' %}selected{% endif %}>Improvement</option>
                        <option value="general" {% if current_filters.type == 'general' %}selected{% endif %}>General</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Rating</label>
                    <select name="rating" class="form-select">
                        <option value="">All Ratings</option>
                        <option value="1" {% if current_filters.rating == '1' %}selected{% endif %}>1 Star</option>
                        <option value="2" {% if current_filters.rating == '2' %}selected{% endif %}>2 Stars</option>
                        <option value="3" {% if current_filters.rating == '3' %}selected{% endif %}>3 Stars</option>
                        <option value="4" {% if current_filters.rating == '4' %}selected{% endif %}>4 Stars</option>
                        <option value="5" {% if current_filters.rating == '5' %}selected{% endif %}>5 Stars</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Search</label>
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="Search feedback..." value="{{ current_filters.search }}">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div class="card mb-4" id="bulk-actions" style="display: none;">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <span class="me-3">Selected: <span id="selected-count">0</span> feedback(s)</span>
                <button class="btn btn-success btn-sm me-2" onclick="bulkAction('resolve')">
                    <i class="fas fa-check"></i> Mark Resolved
                </button>
                <button class="btn btn-warning btn-sm me-2" onclick="bulkAction('unresolve')">
                    <i class="fas fa-undo"></i> Mark Unresolved
                </button>
                <button class="btn btn-danger btn-sm" onclick="bulkAction('delete')">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        </div>
    </div>

    <!-- Feedback List -->
    <div class="card">
        <div class="card-body">
            {% if feedbacks %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="30">
                                <input type="checkbox" id="select-all">
                            </th>
                            <th>User</th>
                            <th>Type</th>
                            <th>Rating</th>
                            <th>Message</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for feedback in feedbacks %}
                        <tr class="feedback-card">
                            <td>
                                <input type="checkbox" class="feedback-checkbox" value="{{ feedback.id }}">
                            </td>
                            <td>
                                {% if feedback.user %}
                                    <strong>{{ feedback.user.get_full_name|default:feedback.user.email }}</strong>
                                    <br><small class="text-muted">{{ feedback.user.email }}</small>
                                {% elif feedback.email %}
                                    <strong>Anonymous</strong>
                                    <br><small class="text-muted">{{ feedback.email }}</small>
                                {% else %}
                                    <span class="text-muted">Anonymous</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="type-badge {{ feedback.feedback_type }}">
                                    {{ feedback.get_feedback_type_display }}
                                </span>
                            </td>
                            <td>
                                {% if feedback.rating %}
                                    <div class="rating-stars">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= feedback.rating %}
                                                <i class="fas fa-star"></i>
                                            {% else %}
                                                <i class="far fa-star"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <span class="text-muted">No rating</span>
                                {% endif %}
                            </td>
                            <td>
                                <div style="max-width: 300px;">
                                    {{ feedback.message|truncatewords:15 }}
                                </div>
                            </td>
                            <td>
                                <span class="status-badge {% if feedback.is_resolved %}resolved{% else %}unresolved{% endif %}">
                                    {% if feedback.is_resolved %}Resolved{% else %}Unresolved{% endif %}
                                </span>
                            </td>
                            <td>
                                <small>{{ feedback.created_at|date:"M d, Y H:i" }}</small>
                            </td>
                            <td>
                                <a href="{% url 'core:feedback_detail' feedback.id %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> View
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <nav aria-label="Feedback pagination">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.rating %}&rating={{ request.GET.rating }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">First</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.rating %}&rating={{ request.GET.rating }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Previous</a>
                        </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
                    </li>

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.rating %}&rating={{ request.GET.rating }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.rating %}&rating={{ request.GET.rating }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Last</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}

            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                <h5>No feedback found</h5>
                <p class="text-muted">No feedback matches your current filters.</p>
            </div>
            {% endif %}
        </div>
    </div>

<script>
// Bulk actions functionality
document.addEventListener('DOMContentLoaded', function() {
    const selectAll = document.getElementById('select-all');
    const checkboxes = document.querySelectorAll('.feedback-checkbox');
    const bulkActions = document.getElementById('bulk-actions');
    const selectedCount = document.getElementById('selected-count');

    function updateBulkActions() {
        const selected = document.querySelectorAll('.feedback-checkbox:checked');
        selectedCount.textContent = selected.length;
        bulkActions.style.display = selected.length > 0 ? 'block' : 'none';
    }

    selectAll.addEventListener('change', function() {
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });

    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });

    window.bulkAction = function(action) {
        const selected = Array.from(document.querySelectorAll('.feedback-checkbox:checked')).map(cb => cb.value);
        
        if (selected.length === 0) {
            alert('Please select feedback to perform bulk action');
            return;
        }

        if (action === 'delete' && !confirm('Are you sure you want to delete selected feedback?')) {
            return;
        }

        fetch('{% url "core:bulk_feedback_action" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
            },
            body: JSON.stringify({
                action: action,
                feedback_ids: selected
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    };
});
</script>
{% endblock %}
