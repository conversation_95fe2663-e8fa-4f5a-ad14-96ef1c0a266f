{% extends 'admin_panel/base.html' %}

{% block title %}Edit User{% endblock %}
{% block page_title %}Edit User: {{ object.full_name }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    Edit User: {{ object.full_name }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <!-- Personal Information -->
                    <h6 class="border-bottom pb-2 mb-3">Personal Information</h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                First Name <span class="text-danger">*</span>
                            </label>
                            {{ form.first_name }}
                            {% if form.first_name.errors %}
                                <div class="text-danger small">{{ form.first_name.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                Last Name <span class="text-danger">*</span>
                            </label>
                            {{ form.last_name }}
                            {% if form.last_name.errors %}
                                <div class="text-danger small">{{ form.last_name.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                Email Address <span class="text-danger">*</span>
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="text-danger small">{{ form.email.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.phone_number.id_for_label }}" class="form-label">
                                Phone Number
                            </label>
                            {{ form.phone_number }}
                            {% if form.phone_number.errors %}
                                <div class="text-danger small">{{ form.phone_number.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.date_of_birth.id_for_label }}" class="form-label">
                                Date of Birth
                            </label>
                            {{ form.date_of_birth }}
                            {% if form.date_of_birth.errors %}
                                <div class="text-danger small">{{ form.date_of_birth.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.current_class_level.id_for_label }}" class="form-label">
                                Current Class Level
                            </label>
                            {{ form.current_class_level }}
                            {% if form.current_class_level.errors %}
                                <div class="text-danger small">{{ form.current_class_level.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.learning_goals.id_for_label }}" class="form-label">
                            Learning Goals
                        </label>
                        {{ form.learning_goals }}
                        {% if form.learning_goals.errors %}
                            <div class="text-danger small">{{ form.learning_goals.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Account Settings -->
                    <h6 class="border-bottom pb-2 mb-3 mt-4">Account Settings</h6>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    Active Account
                                </label>
                            </div>
                            <div class="form-text">
                                Inactive users cannot log in
                            </div>
                            {% if form.is_active.errors %}
                                <div class="text-danger small">{{ form.is_active.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                {{ form.is_staff }}
                                <label class="form-check-label" for="{{ form.is_staff.id_for_label }}">
                                    Staff Access
                                </label>
                            </div>
                            <div class="form-text">
                                Can access admin panel
                            </div>
                            {% if form.is_staff.errors %}
                                <div class="text-danger small">{{ form.is_staff.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                {{ form.is_email_verified }}
                                <label class="form-check-label" for="{{ form.is_email_verified.id_for_label }}">
                                    Email Verified
                                </label>
                            </div>
                            <div class="form-text">
                                Email verification status
                            </div>
                            {% if form.is_email_verified.errors %}
                                <div class="text-danger small">{{ form.is_email_verified.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- User Statistics -->
                    <h6 class="border-bottom pb-2 mb-3 mt-4">User Statistics</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="text-primary mb-1">{{ object.quizzes.count }}</h4>
                                <small class="text-muted">Quizzes Taken</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="text-success mb-1">{{ object.progress.count }}</h4>
                                <small class="text-muted">Progress Records</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="text-info mb-1">{{ object.date_joined|date:"M d, Y" }}</h4>
                                <small class="text-muted">Joined Date</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="text-warning mb-1">{{ object.last_login|date:"M d, Y"|default:"Never" }}</h4>
                                <small class="text-muted">Last Login</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{% url 'admin_panel:users' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Users
                        </a>
                        <div>
                            {% if not object.is_superuser %}
                                <button type="button" 
                                        class="btn btn-danger me-2"
                                        onclick="confirmDelete('{{ object.full_name }}', '{% url 'admin_panel:delete_user' object.id %}')">
                                    <i class="fas fa-trash me-2"></i>
                                    Delete User
                                </button>
                            {% endif %}
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Update User
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the user "<span id="deleteItemName"></span>"?</p>
                <p class="text-danger"><strong>Warning:</strong> This will permanently delete all user data including progress and quiz results.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Delete User</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(itemName, deleteUrl) {
    document.getElementById('deleteItemName').textContent = itemName;
    document.getElementById('deleteForm').action = deleteUrl;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Staff access warning
document.addEventListener('DOMContentLoaded', function() {
    const staffCheckbox = document.getElementById('{{ form.is_staff.id_for_label }}');
    
    if (staffCheckbox) {
        staffCheckbox.addEventListener('change', function() {
            if (this.checked) {
                if (!confirm('Are you sure you want to grant staff access to this user? They will be able to access the admin panel.')) {
                    this.checked = false;
                }
            }
        });
    }
});
</script>
{% endblock %}
