{% extends 'base.html' %}

{% block title %}Page Not Found - Pentora{% endblock %}
{% block description %}The page you're looking for doesn't exist. Explore our free K-12 learning platform with interactive quizzes and study materials.{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center px-4">
    <div class="max-w-2xl mx-auto text-center">
        <!-- 404 Illustration -->
        <div class="mb-8">
            <div class="text-8xl md:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 mb-4">
                404
            </div>
            <div class="text-6xl mb-6">
                <i class="fas fa-search text-gray-300"></i>
            </div>
        </div>

        <!-- Error Message -->
        <h1 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            Oops! Page Not Found
        </h1>
        <p class="text-lg md:text-xl text-gray-600 mb-8 leading-relaxed">
            The page you're looking for seems to have wandered off. 
            Don't worry, there's plenty of great learning content to explore!
        </p>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <a href="{% url 'core:home' %}" class="btn-primary">
                <i class="fas fa-home mr-2"></i>
                Go Home
            </a>
            <a href="{% url 'subjects:list' %}" class="btn-secondary">
                <i class="fas fa-book mr-2"></i>
                Browse Subjects
            </a>
            <a href="{% url 'subjects:quiz_list' %}" class="btn-secondary">
                <i class="fas fa-question-circle mr-2"></i>
                Take a Quiz
            </a>
        </div>

        <!-- Popular Links -->
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Popular Learning Paths</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <a href="{% url 'subjects:list' %}" class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-book-open text-blue-600"></i>
                    </div>
                    <div class="text-left">
                        <div class="font-semibold text-gray-800">English Language Arts</div>
                        <div class="text-sm text-gray-600">Reading & Writing Skills</div>
                    </div>
                </a>
                
                <a href="{% url 'subjects:list' %}" class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-calculator text-green-600"></i>
                    </div>
                    <div class="text-left">
                        <div class="font-semibold text-gray-800">Mathematics</div>
                        <div class="text-sm text-gray-600">Numbers & Problem Solving</div>
                    </div>
                </a>
                
                <a href="{% url 'subjects:list' %}" class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-microscope text-purple-600"></i>
                    </div>
                    <div class="text-left">
                        <div class="font-semibold text-gray-800">Science</div>
                        <div class="text-sm text-gray-600">Explore the Natural World</div>
                    </div>
                </a>
                
                <a href="{% url 'subjects:list' %}" class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-globe text-orange-600"></i>
                    </div>
                    <div class="text-left">
                        <div class="font-semibold text-gray-800">Social Studies</div>
                        <div class="text-sm text-gray-600">History & Geography</div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Help Section -->
        <div class="mt-8 text-center">
            <p class="text-gray-600 mb-4">
                Still can't find what you're looking for?
            </p>
            <div class="flex flex-col sm:flex-row gap-3 justify-center">
                <a href="{% url 'core:contact' %}" class="text-blue-600 hover:text-blue-800 font-semibold">
                    <i class="fas fa-envelope mr-2"></i>
                    Contact Support
                </a>
                <a href="{% url 'core:help' %}" class="text-blue-600 hover:text-blue-800 font-semibold">
                    <i class="fas fa-question mr-2"></i>
                    Help Center
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.btn-primary {
    background: linear-gradient(135deg, #3B82F6, #1D4ED8);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    min-width: 140px;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.4);
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background: transparent;
    border: 2px solid #3B82F6;
    color: #3B82F6;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    min-width: 140px;
}

.btn-secondary:hover {
    background: #3B82F6;
    color: white;
    transform: translateY(-2px);
    text-decoration: none;
}

@media (max-width: 640px) {
    .btn-primary, .btn-secondary {
        width: 100%;
        max-width: 280px;
        margin: 0 auto;
    }
}
</style>
{% endblock %}
