# Generated by Django 5.2.1 on 2025-07-05 14:54

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('analytics', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='abtestassignment',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterUniqueTogether(
            name='abtestvariant',
            unique_together={('test_name', 'variant_name')},
        ),
        migrations.AddField(
            model_name='abtestassignment',
            name='variant',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='analytics.abtestvariant'),
        ),
        migrations.AddField(
            model_name='conversionfunnel',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='errorlog',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='pagevisit',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='systemmetrics',
            index=models.Index(fields=['timestamp'], name='analytics_s_timesta_ce0522_idx'),
        ),
        migrations.AddIndex(
            model_name='systemperformancemetrics',
            index=models.Index(fields=['timestamp'], name='analytics_s_timesta_b50180_idx'),
        ),
        migrations.AddField(
            model_name='useractivity',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='userengagementmetrics',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='abtestassignment',
            index=models.Index(fields=['variant', 'assigned_at'], name='analytics_a_variant_fa375e_idx'),
        ),
        migrations.AddIndex(
            model_name='abtestassignment',
            index=models.Index(fields=['user', 'variant'], name='analytics_a_user_id_8077c3_idx'),
        ),
        migrations.AddIndex(
            model_name='conversionfunnel',
            index=models.Index(fields=['stage', 'timestamp'], name='analytics_c_stage_a8f3fb_idx'),
        ),
        migrations.AddIndex(
            model_name='conversionfunnel',
            index=models.Index(fields=['user', 'stage'], name='analytics_c_user_id_be6c87_idx'),
        ),
        migrations.AddIndex(
            model_name='errorlog',
            index=models.Index(fields=['timestamp'], name='analytics_e_timesta_df942d_idx'),
        ),
        migrations.AddIndex(
            model_name='errorlog',
            index=models.Index(fields=['error_type', 'timestamp'], name='analytics_e_error_t_7bf40c_idx'),
        ),
        migrations.AddIndex(
            model_name='errorlog',
            index=models.Index(fields=['resolved', 'timestamp'], name='analytics_e_resolve_da9b7f_idx'),
        ),
        migrations.AddIndex(
            model_name='pagevisit',
            index=models.Index(fields=['timestamp'], name='analytics_p_timesta_0e7d9f_idx'),
        ),
        migrations.AddIndex(
            model_name='pagevisit',
            index=models.Index(fields=['user', 'timestamp'], name='analytics_p_user_id_d7a3b3_idx'),
        ),
        migrations.AddIndex(
            model_name='pagevisit',
            index=models.Index(fields=['ip_address', 'timestamp'], name='analytics_p_ip_addr_687c2f_idx'),
        ),
        migrations.AddIndex(
            model_name='useractivity',
            index=models.Index(fields=['user', 'timestamp'], name='analytics_u_user_id_54ee7d_idx'),
        ),
        migrations.AddIndex(
            model_name='useractivity',
            index=models.Index(fields=['activity_type', 'timestamp'], name='analytics_u_activit_f59ac3_idx'),
        ),
        migrations.AddIndex(
            model_name='userengagementmetrics',
            index=models.Index(fields=['date'], name='analytics_u_date_aa1b1e_idx'),
        ),
        migrations.AddIndex(
            model_name='userengagementmetrics',
            index=models.Index(fields=['user', 'date'], name='analytics_u_user_id_2064f7_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='userengagementmetrics',
            unique_together={('user', 'date')},
        ),
    ]
