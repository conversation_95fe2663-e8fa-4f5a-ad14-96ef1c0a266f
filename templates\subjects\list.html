{% extends 'base.html' %}

{% block title %}Subjects - Pentora{% endblock %}

{% block extra_css %}
<style>
    /* Modern gradient background with fallback */
    .hero-gradient {
        background: #667eea !important; /* Fallback */
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #db2777 100%) !important;
        min-height: 500px;
        position: relative;
    }

    .hero-pattern {
        background-image:
            radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%) !important;
    }

    /* Ensure text is always visible */
    .hero-gradient * {
        color: white !important;
    }

    .hero-gradient .text-white {
        color: white !important;
    }

    .hero-gradient .bg-gradient-to-r {
        background: linear-gradient(to right, #fbbf24, #f59e0b) !important;
        -webkit-background-clip: text !important;
        background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        color: transparent !important;
    }

    /* Enhanced subject cards */
    .subject-card {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 16px;
        padding: 2rem;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(10px);
    }

    .subject-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
        transition: left 0.6s ease;
    }

    .subject-card:hover::before {
        left: 100%;
    }

    .subject-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
        border-color: #3b82f6;
    }

    /* Modern level cards */
    .level-card {
        background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
        border: 2px solid #e2e8f0;
        border-radius: 20px;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .level-card::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .level-card:hover {
        border-color: #3b82f6;
        box-shadow: 0 12px 24px rgba(59, 130, 246, 0.2);
        transform: translateY(-4px);
    }

    .level-card:hover::after {
        transform: scaleX(1);
    }

    .level-card.selected {
        border-color: #3b82f6;
        background: linear-gradient(145deg, #eff6ff 0%, #dbeafe 100%);
        box-shadow: 0 8px 16px rgba(59, 130, 246, 0.25);
    }

    .level-card.selected::after {
        transform: scaleX(1);
    }

    /* Enhanced step indicators */
    .step-indicator {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        color: white;
        font-size: 0.875rem;
        font-weight: 700;
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        margin-right: 1rem;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        position: relative;
        overflow: hidden;
    }

    .step-indicator::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.6s ease;
    }

    .step-indicator:hover::before {
        left: 100%;
    }

    /* Modern glass morphism effect */
    .glass-card {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    }

    /* Ensure glass card text is dark and visible */
    .glass-card h3,
    .glass-card p,
    .glass-card div {
        color: #1f2937 !important;
    }

    .glass-card .text-gray-900 {
        color: #1f2937 !important;
    }

    .glass-card .text-gray-600 {
        color: #4b5563 !important;
    }

    .glass-card .text-gray-700 {
        color: #374151 !important;
    }

    /* Enhanced icons */
    .icon-container {
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        transition: all 0.3s ease;
    }

    .level-card:hover .icon-container {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        transform: scale(1.1) rotate(5deg);
    }

    .level-card:hover .icon-container i {
        color: white !important;
    }

    /* Animated progress indicators */
    .progress-ring {
        width: 60px;
        height: 60px;
        transform: rotate(-90deg);
    }

    .progress-ring-circle {
        fill: none;
        stroke: #e2e8f0;
        stroke-width: 4;
        stroke-dasharray: 157;
        stroke-dashoffset: 157;
        transition: stroke-dashoffset 0.6s ease;
    }

    .progress-ring-progress {
        fill: none;
        stroke: #3b82f6;
        stroke-width: 4;
        stroke-linecap: round;
        stroke-dasharray: 157;
        stroke-dashoffset: 157;
        transition: stroke-dashoffset 0.6s ease;
    }

    /* Hero section specific styles */
    .hero-section {
        background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #db2777 100%) !important;
        color: white !important;
        position: relative;
        overflow: hidden;
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 60%, rgba(255,255,255,0.05) 0%, transparent 50%);
        pointer-events: none;
    }

    .hero-content {
        position: relative;
        z-index: 10;
    }

    /* Ensure all text in hero is visible */
    .hero-section h1,
    .hero-section p,
    .hero-section div,
    .hero-section span {
        color: white !important;
    }

    .hero-section .text-transparent {
        background: linear-gradient(to right, #fbbf24, #f59e0b) !important;
        -webkit-background-clip: text !important;
        background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
        .subject-card {
            padding: 1.5rem;
        }

        .level-card {
            border-radius: 16px;
        }

        .hero-gradient {
            min-height: 400px;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Modern Hero Section -->
<div class="hero-section hero-gradient hero-pattern relative overflow-hidden" style="background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #db2777 100%) !important; color: white !important;">
    <!-- Animated background elements -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute top-10 left-10 w-20 h-20 bg-white rounded-full animate-pulse"></div>
        <div class="absolute top-32 right-20 w-16 h-16 bg-white rounded-full animate-pulse delay-1000"></div>
        <div class="absolute bottom-20 left-1/4 w-12 h-12 bg-white rounded-full animate-pulse delay-2000"></div>
        <div class="absolute bottom-32 right-1/3 w-8 h-8 bg-white rounded-full animate-pulse delay-3000"></div>
    </div>

    <div class="hero-content relative container mx-auto px-6 py-16 lg:py-24" style="z-index: 10;">
        <div class="text-center max-w-4xl mx-auto">
            <!-- Main heading with gradient text -->
            <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight" style="color: white !important;">
                <span class="text-white" style="color: white !important;">Discover Your</span>
                <span class="block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent" style="background: linear-gradient(to right, #fbbf24, #f59e0b) !important; -webkit-background-clip: text !important; background-clip: text !important; -webkit-text-fill-color: transparent !important;">
                    Learning Journey
                </span>
            </h1>

            <!-- Enhanced description -->
            <p class="text-xl lg:text-2xl text-white text-opacity-90 leading-relaxed mb-8 max-w-3xl mx-auto" style="color: white !important;">
                Choose your grade level and explore subjects designed to unlock your potential and accelerate your academic success.
            </p>

            <!-- Modern stats section -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12 max-w-2xl mx-auto">
                <div class="text-center">
                    <div class="text-2xl lg:text-3xl font-bold mb-1" style="color: white !important;">12</div>
                    <div class="text-sm" style="color: rgba(255,255,255,0.9) !important;">Grade Levels</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl lg:text-3xl font-bold mb-1" style="color: white !important;">50+</div>
                    <div class="text-sm" style="color: rgba(255,255,255,0.9) !important;">Subjects</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl lg:text-3xl font-bold mb-1" style="color: white !important;">1000+</div>
                    <div class="text-sm" style="color: rgba(255,255,255,0.9) !important;">Topics</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl lg:text-3xl font-bold mb-1" style="color: white !important;">Free</div>
                    <div class="text-sm" style="color: rgba(255,255,255,0.9) !important;">Forever</div>
                </div>
            </div>

            {% if show_login_prompt %}
                <!-- Enhanced CTA section -->
                <div class="glass-card rounded-2xl p-8 max-w-lg mx-auto" style="background: rgba(255, 255, 255, 0.95) !important;">
                    <div class="flex items-center justify-center mb-6">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-rocket text-white text-xl"></i>
                        </div>
                        <div class="text-left">
                            <h3 class="font-bold text-lg" style="color: #1f2937 !important;">Ready to Launch?</h3>
                            <p class="text-sm" style="color: #4b5563 !important;">Join thousands of learners today</p>
                        </div>
                    </div>

                    <p class="mb-8 leading-relaxed" style="color: #374151 !important;">
                        Create your free account to access personalized lessons, track progress, and unlock your full learning potential!
                    </p>

                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="{% url 'users:register' %}" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg">
                            <i class="fas fa-user-plus mr-2"></i>
                            Start Learning Free
                        </a>
                        <a href="{% url 'users:login' %}" class="bg-white border-2 border-gray-200 px-8 py-4 rounded-xl font-semibold hover:bg-gray-50 hover:border-gray-300 transition-all duration-300" style="color: #374151 !important;">
                            <i class="fas fa-sign-in-alt mr-2"></i>
                            Sign In
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Bottom wave -->
    <div class="absolute bottom-0 left-0 right-0">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="w-full h-12 lg:h-20">
            <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="#f8fafc"></path>
            <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="#f8fafc"></path>
            <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="#f8fafc"></path>
        </svg>
    </div>
</div>

<div class="bg-gray-50 py-16">
    <div class="container mx-auto px-6">
        <!-- Step 1: Class Level Selection -->
        <div class="bg-white rounded-xl shadow-md border border-gray-200 p-8 mb-8" id="classLevelStep">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-semibold text-gray-900">
                    <span class="step-indicator">Step 1</span>
                    Select Your Grade Level
                </h2>
                {% if user.is_authenticated and user.current_class_level %}
                    <button class="text-sm text-blue-600 hover:text-blue-800 font-medium" onclick="selectUserLevel()">
                        Use My Level: Grade {{ user.current_class_level }}
                    </button>
                {% endif %}
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Elementary Levels -->
                <div class="level-card group" data-level-range="primary" onclick="selectLevelRange('primary')">
                    <div class="text-center p-8">
                        <div class="icon-container w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 relative">
                            <i class="fas fa-seedling text-green-600 text-3xl transition-all duration-300"></i>
                            <div class="absolute inset-0 bg-gradient-to-r from-green-400 to-blue-500 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">Elementary School</h3>
                        <div class="bg-gradient-to-r from-green-500 to-blue-500 text-white px-4 py-2 rounded-full text-sm font-semibold mb-3 inline-block">
                            Grades 1-6
                        </div>
                        <p class="text-gray-600 mb-4">Ages 6-12 • Foundation Learning</p>
                        <div class="flex items-center justify-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-book"></i>
                            <span>Basic Concepts</span>
                            <span>•</span>
                            <i class="fas fa-puzzle-piece"></i>
                            <span>Fun Activities</span>
                        </div>
                    </div>
                </div>

                <!-- Middle School Levels -->
                <div class="level-card group" data-level-range="jhs" onclick="selectLevelRange('jhs')">
                    <div class="text-center p-8">
                        <div class="icon-container w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 relative">
                            <i class="fas fa-rocket text-purple-600 text-3xl transition-all duration-300"></i>
                            <div class="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-500 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-purple-600 transition-colors">Middle School</h3>
                        <div class="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-2 rounded-full text-sm font-semibold mb-3 inline-block">
                            Grades 7-9
                        </div>
                        <p class="text-gray-600 mb-4">Ages 13-15 • Intermediate Learning</p>
                        <div class="flex items-center justify-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-flask"></i>
                            <span>Experiments</span>
                            <span>•</span>
                            <i class="fas fa-calculator"></i>
                            <span>Problem Solving</span>
                        </div>
                    </div>
                </div>

                <!-- High School Levels -->
                <div class="level-card group" data-level-range="shs" onclick="selectLevelRange('shs')">
                    <div class="text-center p-8">
                        <div class="icon-container w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 relative">
                            <i class="fas fa-graduation-cap text-indigo-600 text-3xl transition-all duration-300"></i>
                            <div class="absolute inset-0 bg-gradient-to-r from-indigo-400 to-cyan-500 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-indigo-600 transition-colors">High School</h3>
                        <div class="bg-gradient-to-r from-indigo-500 to-cyan-500 text-white px-4 py-2 rounded-full text-sm font-semibold mb-3 inline-block">
                            Grades 10-12
                        </div>
                        <p class="text-gray-600 mb-4">Ages 16-18 • Advanced Learning</p>
                        <div class="flex items-center justify-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-microscope"></i>
                            <span>Advanced Topics</span>
                            <span>•</span>
                            <i class="fas fa-trophy"></i>
                            <span>Exam Prep</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 2: Specific Level Selection (Hidden initially) -->
        <div class="bg-white rounded-xl shadow-md border border-gray-200 p-8 mb-8 hidden" id="specificLevelStep">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-semibold text-gray-900">
                    <span class="step-indicator">Step 2</span>
                    Choose Specific Grade
                </h2>
                <button class="text-sm text-gray-600 hover:text-gray-800" onclick="goBackToLevelRange()">
                    <i class="fas fa-arrow-left mr-1"></i>
                    Back to Grade Range
                </button>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4" id="specificLevelGrid">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>

        <!-- Step 3: Subject Selection (Hidden initially) -->
        <div class="bg-white rounded-xl shadow-md border border-gray-200 p-8 mb-8 hidden" id="subjectSelectionStep">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-semibold text-gray-900">
                    <span class="step-indicator">Step 3</span>
                    Select Subject to Learn
                </h2>
                <button class="text-sm text-gray-600 hover:text-gray-800" onclick="goBackToSpecificLevel()">
                    <i class="fas fa-arrow-left mr-1"></i>
                    Back to Grade Selection
                </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="subjectGrid">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>

        <!-- Initial instruction message -->
        <div class="text-center py-16" id="instructionMessage">
            <div class="w-20 h-20 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-route text-blue-600 text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Ready to Learn?</h3>
            <p class="text-gray-600 text-lg max-w-2xl mx-auto leading-relaxed">
                Follow the steps above to select your grade level, then choose a subject to start your learning journey.
                Our curriculum is aligned with international educational standards.
            </p>
        </div>


    </div>
</div>

<!-- Login Prompt Modal -->
{% if not user.is_authenticated %}
<div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg max-w-md w-full p-6">
        <div class="text-center">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-graduation-cap text-blue-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-2">Ready to start learning?</h3>
            <p class="text-gray-600 mb-6" id="subjectName">Sign in to access this subject and track your progress!</p>

            <div class="flex gap-3 justify-center">
                <a href="{% url 'users:register' %}" class="btn bg-blue-600 text-white hover:bg-blue-700">
                    <i class="fas fa-user-plus mr-2"></i>
                    Create Free Account
                </a>
                <a href="{% url 'users:login' %}" class="btn btn-outline border-gray-300 text-gray-600 hover:bg-gray-50">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Sign In
                </a>
            </div>

            <button onclick="closeLoginPrompt()" class="mt-4 text-gray-500 hover:text-gray-700 text-sm">
                Maybe later
            </button>
        </div>
    </div>
</div>
{% endif %}

<script>
// Learning flow state management
let currentState = {
    step: 1,
    selectedLevelRange: null,
    selectedSpecificLevel: null,
    selectedSubject: null
};

// Level mappings
const levelMappings = {
    primary: {
        name: 'Elementary School',
        levels: [
            {num: 1, name: 'Grade 1'},
            {num: 2, name: 'Grade 2'},
            {num: 3, name: 'Grade 3'},
            {num: 4, name: 'Grade 4'},
            {num: 5, name: 'Grade 5'},
            {num: 6, name: 'Grade 6'}
        ]
    },
    jhs: {
        name: 'Middle School',
        levels: [
            {num: 7, name: 'Grade 7'},
            {num: 8, name: 'Grade 8'},
            {num: 9, name: 'Grade 9'}
        ]
    },
    shs: {
        name: 'High School',
        levels: [
            {num: 10, name: 'Grade 10'},
            {num: 11, name: 'Grade 11'},
            {num: 12, name: 'Grade 12'}
        ]
    }
};

// Available subjects
const subjects = [
    {id: 1, name: 'English Language', icon: 'fas fa-book-open', color: '#3B82F6', description: 'Develop reading, writing, speaking and listening skills'},
    {id: 2, name: 'Mathematics', icon: 'fas fa-calculator', color: '#10B981', description: 'Build strong mathematical foundations and problem-solving skills'},
    {id: 3, name: 'Science', icon: 'fas fa-flask', color: '#8B5CF6', description: 'Explore the natural world through scientific inquiry'},
    {id: 4, name: 'Social Studies', icon: 'fas fa-globe-africa', color: '#F59E0B', description: 'Understand society, culture, and civic responsibilities'},
    {id: 5, name: 'Ghanaian Language', icon: 'fas fa-language', color: '#EF4444', description: 'Learn and appreciate local Ghanaian languages'},
    {id: 6, name: 'Religious and Moral Education', icon: 'fas fa-heart', color: '#EC4899', description: 'Develop moral values and religious understanding'}
];

// Step 1: Select level range (Primary, JHS, SHS)
function selectLevelRange(levelRange) {
    currentState.selectedLevelRange = levelRange;
    currentState.step = 2;

    // Hide step 1, show step 2
    document.getElementById('classLevelStep').classList.add('hidden');
    document.getElementById('specificLevelStep').classList.remove('hidden');
    document.getElementById('instructionMessage').classList.add('hidden');

    // Populate specific levels
    populateSpecificLevels(levelRange);
}

// Populate specific level options
function populateSpecificLevels(levelRange) {
    const grid = document.getElementById('specificLevelGrid');
    const levels = levelMappings[levelRange].levels;

    grid.innerHTML = levels.map(level => `
        <div class="specific-level-card" onclick="selectSpecificLevel(${level.num}, '${level.name}')">
            <div class="text-center p-4 bg-white border-2 border-gray-200 rounded-xl hover:border-blue-500 hover:bg-blue-50 transition-all cursor-pointer level-card">
                <div class="text-lg font-semibold text-gray-900">${level.name}</div>
            </div>
        </div>
    `).join('');
}

// Step 2: Select specific level
function selectSpecificLevel(levelNum, levelName) {
    currentState.selectedSpecificLevel = {num: levelNum, name: levelName};
    currentState.step = 3;

    // Hide step 2, show step 3
    document.getElementById('specificLevelStep').classList.add('hidden');
    document.getElementById('subjectSelectionStep').classList.remove('hidden');

    // Populate subjects
    populateSubjects();
}

// Populate subject options
function populateSubjects() {
    const grid = document.getElementById('subjectGrid');
    const levelNumber = currentState.selectedSpecificLevel.num;

    // Show loading state
    grid.innerHTML = `
        <div class="col-span-full text-center py-8">
            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                <i class="fas fa-spinner fa-spin text-blue-600"></i>
            </div>
            <p class="text-gray-600">Loading subjects...</p>
        </div>
    `;

    // Fetch subjects for the selected level
    fetch(`/subjects/api/subjects-by-level/${levelNumber}/`)
        .then(response => response.json())
        .then(data => {
            if (data.subjects && data.subjects.length > 0) {
                grid.innerHTML = data.subjects.map(subject => `
                    <div class="subject-card" onclick="selectSubject('${subject.id}', '${subject.name}')">
                        <div class="p-6 bg-white border border-gray-200 rounded-xl hover:border-gray-400 hover:shadow-lg transition-all cursor-pointer group">
                            <div class="flex items-center mb-4">
                                <div class="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mr-4">
                                    <i class="${subject.icon} text-2xl text-gray-600"></i>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-xl font-bold text-gray-900 mb-1">${subject.name}</h3>
                                    <p class="text-sm text-gray-600">${subject.description}</p>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-xs font-medium text-gray-600 bg-gray-100 px-2 py-1 rounded">
                                    ${currentState.selectedSpecificLevel.name}
                                </span>
                                <div class="flex items-center text-gray-700">
                                    <span class="text-sm font-medium mr-2">Start Learning</span>
                                    <i class="fas fa-arrow-right"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('');
            } else {
                grid.innerHTML = `
                    <div class="col-span-full text-center py-16">
                        <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-info-circle text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-600 mb-2">No Subjects Available</h3>
                        <p class="text-gray-500">No subjects available for ${currentState.selectedSpecificLevel.name} yet.</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading subjects:', error);
            grid.innerHTML = `
                <div class="col-span-full text-center py-16">
                    <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">Error Loading Subjects</h3>
                    <p class="text-gray-500">Please try refreshing the page.</p>
                </div>
            `;
        });
}

// Step 3: Select subject (redirect to topic list for the selected class and subject)
function selectSubject(subjectId, subjectName) {
    currentState.selectedSubject = {id: subjectId, name: subjectName};

    // Find the level ID for the selected class and subject
    // We need to make an API call to get the level ID
    const levelNumber = currentState.selectedSpecificLevel.num;

    // Show loading state
    const grid = document.getElementById('subjectGrid');
    grid.innerHTML = `
        <div class="col-span-full text-center py-8">
            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                <i class="fas fa-spinner fa-spin text-blue-600"></i>
            </div>
            <p class="text-gray-600">Loading topics...</p>
        </div>
    `;

    // Get the level ID and redirect to topic list
    fetch(`/subjects/api/quiz-topics/${levelNumber}/${subjectId}/`)
        .then(response => response.json())
        .then(data => {
            if (data.level && data.level.id) {
                // Redirect to the topic list for this specific class level and subject
                window.location.href = `/subjects/${subjectId}/levels/${data.level.id}/`;
            } else {
                // Show error if no level found
                grid.innerHTML = `
                    <div class="col-span-full text-center py-16">
                        <div class="w-20 h-20 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-info-circle text-yellow-600 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">No Content Available</h3>
                        <p class="text-gray-600 mb-4">
                            No content available for <strong>${currentState.selectedSpecificLevel.name} ${subjectName}</strong> yet.
                        </p>
                        <button onclick="goBackToSubjects()" class="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Choose Different Subject
                        </button>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading level info:', error);
            grid.innerHTML = `
                <div class="col-span-full text-center py-16">
                    <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">Error Loading Content</h3>
                    <p class="text-gray-500 mb-6">Please try refreshing the page.</p>
                    <button onclick="goBackToSubjects()" class="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Choose Different Subject
                    </button>
                </div>
            `;
        });
}

// Navigation functions
function goBackToLevelRange() {
    currentState.step = 1;
    document.getElementById('specificLevelStep').classList.add('hidden');
    document.getElementById('classLevelStep').classList.remove('hidden');
    document.getElementById('instructionMessage').classList.remove('hidden');
}

function goBackToSpecificLevel() {
    currentState.step = 2;
    document.getElementById('subjectSelectionStep').classList.add('hidden');
    document.getElementById('specificLevelStep').classList.remove('hidden');
}

function goBackToSubjects() {
    currentState.step = 3;
    populateSubjects();
}

// Quick select user's current level
function selectUserLevel() {
    {% if user.is_authenticated and user.current_class_level %}
        const userLevel = {{ user.current_class_level }};
        const userLevelName = "{{ user.get_current_class_level_display }}";

        // Determine level range
        let levelRange;
        if (userLevel >= 1 && userLevel <= 6) levelRange = 'primary';
        else if (userLevel >= 7 && userLevel <= 9) levelRange = 'jhs';
        else if (userLevel >= 10 && userLevel <= 12) levelRange = 'shs';

        // Set state and go directly to subject selection
        currentState.selectedLevelRange = levelRange;
        currentState.selectedSpecificLevel = {num: userLevel, name: userLevelName};
        currentState.step = 3;

        // Hide step 1, show step 3
        document.getElementById('classLevelStep').classList.add('hidden');
        document.getElementById('subjectSelectionStep').classList.remove('hidden');
        document.getElementById('instructionMessage').classList.add('hidden');

        // Populate subjects
        populateSubjects();
    {% endif %}
}

// Login modal functions
function showLoginPrompt(subjectName) {
    document.getElementById('subjectName').textContent = `Sign in to access ${subjectName} and track your progress!`;
    document.getElementById('loginModal').classList.remove('hidden');
}

function closeLoginPrompt() {
    document.getElementById('loginModal').classList.add('hidden');
}

// Close modal when clicking outside
{% if not user.is_authenticated %}
document.getElementById('loginModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeLoginPrompt();
    }
});
{% endif %}
</script>
{% endblock %}
