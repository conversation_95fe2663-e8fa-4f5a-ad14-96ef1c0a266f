#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix Grade_9_questions.csv formatting issues
"""
import csv
import sys

def fix_csv_file(input_file, output_file):
    """Fix CSV file by removing extra columns and ensuring proper format"""

    # Expected columns in correct order
    expected_columns = [
        'subject_name', 'class_level_name', 'topic_title', 'question_text',
        'question_type', 'correct_answer', 'explanation', 'difficulty',
        'points', 'time_limit', 'choice_a', 'choice_b', 'choice_c', 'choice_d'
    ]

    fixed_rows = []
    errors = []

    try:
        with open(input_file, 'r', encoding='utf-8') as infile:
            # Use Python's CSV reader to properly handle quoted fields
            reader = csv.reader(infile)

            for line_num, row in enumerate(reader, 1):
                # Remove empty trailing fields
                while row and row[-1] == '':
                    row.pop()

                # Take only the first 14 fields (remove extra empty columns)
                fields = row[:14]

                # Ensure we have exactly 14 fields
                while len(fields) < 14:
                    fields.append("")

                # For header row
                if line_num == 1:
                    fixed_rows.append(expected_columns)
                else:
                    # Validate data rows
                    if len(fields) >= 14:
                        # Check question_type is valid
                        question_type = fields[4].strip().lower()
                        valid_types = ['multiple_choice', 'fill_blank', 'true_false', 'short_answer']

                        if question_type not in valid_types:
                            errors.append(f"Row {line_num}: Invalid question type '{fields[4]}'. Must be one of: {', '.join(valid_types)}")
                            continue

                        # Check difficulty is valid
                        difficulty = fields[7].strip().lower()
                        valid_difficulties = ['easy', 'medium', 'hard']

                        if difficulty not in valid_difficulties:
                            errors.append(f"Row {line_num}: Invalid difficulty '{fields[7]}'. Must be one of: {', '.join(valid_difficulties)}")
                            continue

                        # Check points is numeric
                        try:
                            int(fields[8])
                        except ValueError:
                            errors.append(f"Row {line_num}: Points must be numeric, got '{fields[8]}'")
                            continue

                        # Check time_limit is numeric
                        try:
                            int(fields[9])
                        except ValueError:
                            errors.append(f"Row {line_num}: Time limit must be numeric, got '{fields[9]}'")
                            continue

                        fixed_rows.append(fields)
                    else:
                        errors.append(f"Row {line_num}: Insufficient fields ({len(fields)} found, 14 required)")

        # Write fixed file
        with open(output_file, 'w', encoding='utf-8', newline='') as outfile:
            writer = csv.writer(outfile)
            for row in fixed_rows:
                writer.writerow(row)

        print(f"✅ Fixed file saved as: {output_file}")
        print(f"✅ Processed {len(fixed_rows)} rows (including header)")

        if errors:
            print(f"\n⚠️  Found {len(errors)} errors:")
            for error in errors[:10]:  # Show first 10 errors
                print(f"   {error}")
            if len(errors) > 10:
                print(f"   ... and {len(errors) - 10} more errors")
        else:
            print("✅ No errors found!")

    except Exception as e:
        print(f"❌ Error processing file: {e}")
        return False

    return len(errors) == 0

if __name__ == "__main__":
    input_file = "Grade_9_questions.csv"
    output_file = "Grade_9_questions_fixed.csv"
    
    print("🔧 Fixing CSV file format...")
    success = fix_csv_file(input_file, output_file)
    
    if success:
        print("\n🎉 File fixed successfully! You can now upload Grade_9_questions_fixed.csv")
    else:
        print("\n⚠️  File has been processed but contains errors that need manual review.")
