{% extends 'base.html' %}

{% block title %}Reset Instructions Sent - Pentora{% endblock %}

{% block extra_css %}
<style>
    .sent-container {
        background: linear-gradient(135deg, #F9FAFB 0%, #E5E7EB 100%);
        min-height: calc(100vh - 80px);
    }
    
    .sent-card {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        border: 1px solid #E5E7EB;
    }
    
    .success-icon {
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, #22C55E, #16A34A);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        box-shadow: 0 10px 25px -5px rgba(34, 197, 94, 0.3);
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }
    
    .step-item {
        display: flex;
        align-items: flex-start;
        padding: 1rem;
        background: #F9FAFB;
        border-radius: 0.75rem;
        border-left: 4px solid #22C55E;
    }
    
    .step-number {
        width: 32px;
        height: 32px;
        background: #22C55E;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 0.875rem;
        margin-right: 1rem;
        flex-shrink: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="sent-container flex items-center justify-center px-4 py-8">
    <div class="w-full max-w-lg">
        <!-- Success Header -->
        <div class="text-center mb-8">
            <div class="success-icon">
                <i class="fas fa-envelope text-white text-3xl"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Check Your Email!</h1>
            <p class="text-gray-600 text-lg">Password reset instructions have been sent</p>
        </div>

        <!-- Instructions Card -->
        <div class="sent-card p-8">
            <div class="text-center mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-2">What happens next?</h2>
                <p class="text-gray-600">Follow these simple steps to reset your password:</p>
            </div>

            <!-- Steps -->
            <div class="space-y-4 mb-8">
                <div class="step-item">
                    <div class="step-number">1</div>
                    <div>
                        <h3 class="font-semibold text-gray-800 mb-1">Check Your Email</h3>
                        <p class="text-sm text-gray-600">Look for an email from Pentora in your inbox. Don't forget to check your spam folder!</p>
                    </div>
                </div>

                <div class="step-item">
                    <div class="step-number">2</div>
                    <div>
                        <h3 class="font-semibold text-gray-800 mb-1">Click the Reset Link</h3>
                        <p class="text-sm text-gray-600">Click the "Reset Password" button in the email to open the password reset page.</p>
                    </div>
                </div>

                <div class="step-item">
                    <div class="step-number">3</div>
                    <div>
                        <h3 class="font-semibold text-gray-800 mb-1">Create New Password</h3>
                        <p class="text-sm text-gray-600">Enter your new password and confirm it. Make sure it's strong and secure!</p>
                    </div>
                </div>

                <div class="step-item">
                    <div class="step-number">4</div>
                    <div>
                        <h3 class="font-semibold text-gray-800 mb-1">Sign In</h3>
                        <p class="text-sm text-gray-600">Use your new password to sign in and continue your learning journey.</p>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-3">
                <a href="{% url 'users:login' %}" class="btn btn-primary w-full py-3 rounded-xl font-medium">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Back to Sign In
                </a>
                
                <a href="{% url 'users:password_reset' %}" class="btn btn-outline w-full border-2 border-gray-200 hover:border-blue-300 hover:bg-blue-50 text-gray-700 hover:text-blue-600 font-medium py-3 rounded-xl">
                    <i class="fas fa-redo mr-2"></i>
                    Send Another Email
                </a>
            </div>
        </div>

        <!-- Help Section -->
        <div class="mt-8">
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div class="flex items-center justify-center mb-2">
                    <i class="fas fa-exclamation-triangle text-yellow-600 mr-2"></i>
                    <span class="font-medium text-yellow-800">Didn't receive the email?</span>
                </div>
                <div class="text-sm text-yellow-700 space-y-2">
                    <p>• Check your spam or junk mail folder</p>
                    <p>• Make sure you entered the correct email address</p>
                    <p>• Wait a few minutes - emails can sometimes be delayed</p>
                    <p>• Try sending another reset email using the button above</p>
                </div>
                <div class="mt-4 text-center">
                    <a href="{% url 'core:contact' %}" class="text-sm text-yellow-700 hover:text-yellow-900 font-medium underline">
                        Still need help? Contact our support team
                    </a>
                </div>
            </div>
        </div>

        <!-- Security Notice -->
        <div class="mt-6 text-center">
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-center mb-2">
                    <i class="fas fa-shield-alt text-gray-500 mr-2"></i>
                    <span class="font-medium text-gray-700">Security Notice</span>
                </div>
                <p class="text-xs text-gray-600">
                    For your security, password reset links expire after 1 hour. 
                    If your link expires, you can request a new one anytime.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
