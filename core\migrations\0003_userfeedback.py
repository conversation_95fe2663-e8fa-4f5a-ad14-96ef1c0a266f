# Generated by Django 5.2.1 on 2025-07-05 22:17

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserFeedback',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('rating', models.IntegerField(blank=True, choices=[(1, '1 Star'), (2, '2 Stars'), (3, '3 Stars'), (4, '4 Stars'), (5, '5 Stars')], null=True)),
                ('feedback_type', models.CharField(choices=[('bug_report', 'Bug Report'), ('feature_request', 'Feature Request'), ('improvement', 'Improvement'), ('general', 'General')], default='general', max_length=20)),
                ('message', models.TextField()),
                ('include_screenshot', models.BooleanField(default=False)),
                ('page_url', models.URLField(blank=True, max_length=500)),
                ('user_agent', models.TextField(blank=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('is_resolved', models.BooleanField(default=False)),
                ('admin_notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Feedback',
                'verbose_name_plural': 'User Feedback',
                'db_table': 'user_feedback',
                'ordering': ['-created_at'],
            },
        ),
    ]
