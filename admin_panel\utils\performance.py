"""
Performance monitoring utilities for tracking system performance
"""

import time
import logging
from functools import wraps
from django.db import connection
from django.core.cache import cache
from django.conf import settings
import json
from datetime import datetime, timedelta

logger = logging.getLogger('performance')


class PerformanceMonitor:
    """
    Monitor and track performance metrics
    """
    
    def __init__(self):
        self.metrics = {}
        self.slow_queries = []
        self.cache_stats = {}
    
    def track_query_time(self, operation_name):
        """Decorator to track query execution time"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                start_queries = len(connection.queries)
                
                try:
                    result = func(*args, **kwargs)
                    success = True
                    error = None
                except Exception as e:
                    result = None
                    success = False
                    error = str(e)
                    raise
                finally:
                    end_time = time.time()
                    execution_time = end_time - start_time
                    query_count = len(connection.queries) - start_queries
                    
                    # Log performance metrics
                    self.log_performance(
                        operation_name,
                        execution_time,
                        query_count,
                        success,
                        error
                    )
                
                return result
            return wrapper
        return decorator
    
    def log_performance(self, operation, execution_time, query_count, success, error=None):
        """Log performance metrics"""
        metric = {
            'operation': operation,
            'execution_time': execution_time,
            'query_count': query_count,
            'success': success,
            'error': error,
            'timestamp': datetime.now().isoformat()
        }
        
        # Store in memory (in production, you'd use a proper logging system)
        if operation not in self.metrics:
            self.metrics[operation] = []
        
        self.metrics[operation].append(metric)
        
        # Keep only last 100 entries per operation
        if len(self.metrics[operation]) > 100:
            self.metrics[operation] = self.metrics[operation][-100:]
        
        # Log slow operations
        if execution_time > 2.0:  # Slower than 2 seconds
            self.slow_queries.append(metric)
            logger.warning(f"Slow operation: {operation} took {execution_time:.2f}s with {query_count} queries")
        
        # Log to Django logger
        if settings.DEBUG:
            logger.info(f"{operation}: {execution_time:.3f}s, {query_count} queries")
    
    def get_performance_summary(self):
        """Get performance summary"""
        summary = {}
        
        for operation, metrics in self.metrics.items():
            if not metrics:
                continue
                
            times = [m['execution_time'] for m in metrics if m['success']]
            queries = [m['query_count'] for m in metrics if m['success']]
            
            if times:
                summary[operation] = {
                    'count': len(times),
                    'avg_time': sum(times) / len(times),
                    'max_time': max(times),
                    'min_time': min(times),
                    'avg_queries': sum(queries) / len(queries) if queries else 0,
                    'success_rate': len(times) / len(metrics) * 100
                }
        
        return summary
    
    def get_slow_operations(self, threshold=2.0):
        """Get operations slower than threshold"""
        return [q for q in self.slow_queries if q['execution_time'] > threshold]


class DatabasePerformanceMonitor:
    """
    Monitor database performance specifically
    """
    
    @staticmethod
    def get_query_stats():
        """Get database query statistics"""
        if not settings.DEBUG:
            return {"message": "Query logging only available in DEBUG mode"}
        
        queries = connection.queries
        if not queries:
            return {"total_queries": 0}
        
        total_time = sum(float(q['time']) for q in queries)
        slow_queries = [q for q in queries if float(q['time']) > 0.1]
        
        return {
            'total_queries': len(queries),
            'total_time': total_time,
            'avg_time': total_time / len(queries),
            'slow_queries': len(slow_queries),
            'slowest_query': max(queries, key=lambda q: float(q['time'])) if queries else None
        }
    
    @staticmethod
    def analyze_duplicate_queries():
        """Analyze for duplicate queries"""
        if not settings.DEBUG:
            return {"message": "Query analysis only available in DEBUG mode"}
        
        queries = connection.queries
        query_counts = {}
        
        for query in queries:
            sql = query['sql']
            if sql in query_counts:
                query_counts[sql] += 1
            else:
                query_counts[sql] = 1
        
        duplicates = {sql: count for sql, count in query_counts.items() if count > 1}
        
        return {
            'total_unique_queries': len(query_counts),
            'duplicate_queries': len(duplicates),
            'most_repeated': max(duplicates.items(), key=lambda x: x[1]) if duplicates else None
        }


class CachePerformanceMonitor:
    """
    Monitor cache performance
    """
    
    def __init__(self):
        self.cache_hits = 0
        self.cache_misses = 0
        self.cache_operations = []
    
    def track_cache_operation(self, operation, key, hit=None):
        """Track cache operations"""
        self.cache_operations.append({
            'operation': operation,
            'key': key,
            'hit': hit,
            'timestamp': datetime.now().isoformat()
        })
        
        if hit is True:
            self.cache_hits += 1
        elif hit is False:
            self.cache_misses += 1
        
        # Keep only last 1000 operations
        if len(self.cache_operations) > 1000:
            self.cache_operations = self.cache_operations[-1000:]
    
    def get_cache_stats(self):
        """Get cache statistics"""
        total_operations = self.cache_hits + self.cache_misses
        hit_rate = (self.cache_hits / total_operations * 100) if total_operations > 0 else 0
        
        return {
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'hit_rate': hit_rate,
            'total_operations': total_operations
        }


# Global performance monitors
performance_monitor = PerformanceMonitor()
cache_monitor = CachePerformanceMonitor()


def monitor_performance(operation_name):
    """Decorator for monitoring function performance"""
    return performance_monitor.track_query_time(operation_name)


def log_page_load_time(view_name, execution_time, query_count):
    """Log page load performance"""
    performance_monitor.log_performance(
        f"page_load_{view_name}",
        execution_time,
        query_count,
        True
    )


def get_system_performance_report():
    """Get comprehensive system performance report"""
    return {
        'performance_summary': performance_monitor.get_performance_summary(),
        'slow_operations': performance_monitor.get_slow_operations(),
        'database_stats': DatabasePerformanceMonitor.get_query_stats(),
        'duplicate_queries': DatabasePerformanceMonitor.analyze_duplicate_queries(),
        'cache_stats': cache_monitor.get_cache_stats(),
        'generated_at': datetime.now().isoformat()
    }


def clear_performance_data():
    """Clear all performance monitoring data"""
    global performance_monitor, cache_monitor
    performance_monitor = PerformanceMonitor()
    cache_monitor = CachePerformanceMonitor()


class PerformanceMiddleware:
    """
    Middleware to automatically track page performance
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        start_time = time.time()
        start_queries = len(connection.queries)
        
        response = self.get_response(request)
        
        end_time = time.time()
        execution_time = end_time - start_time
        query_count = len(connection.queries) - start_queries
        
        # Log performance for admin panel pages
        if request.path.startswith('/my-admin/'):
            view_name = request.resolver_match.url_name if request.resolver_match else 'unknown'
            log_page_load_time(view_name, execution_time, query_count)
        
        # Add performance headers in debug mode
        if settings.DEBUG:
            response['X-Performance-Time'] = f"{execution_time:.3f}s"
            response['X-Performance-Queries'] = str(query_count)
        
        return response
