{% extends 'admin_panel/base.html' %}

{% block title %}Site Settings{% endblock %}
{% block page_title %}Site Settings{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Success/Error Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <form method="post" enctype="multipart/form-data" id="settings-form">
        {% csrf_token %}

        <div class="row">
            <!-- Main Settings Column -->
            <div class="col-lg-8">

                <!-- Site Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2 text-primary"></i>
                            Site Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.site_name.id_for_label }}" class="form-label fw-bold">
                                    Site Name <span class="text-danger">*</span>
                                </label>
                                {{ form.site_name }}
                                {% if form.site_name.errors %}
                                    <div class="text-danger small mt-1">{{ form.site_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.contact_email.id_for_label }}" class="form-label fw-bold">
                                    Contact Email <span class="text-danger">*</span>
                                </label>
                                {{ form.contact_email }}
                                {% if form.contact_email.errors %}
                                    <div class="text-danger small mt-1">{{ form.contact_email.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.site_description.id_for_label }}" class="form-label fw-bold">
                                Site Description
                            </label>
                            <textarea name="{{ form.site_description.name }}"
                                     id="{{ form.site_description.id_for_label }}"
                                     class="form-control"
                                     rows="3"
                                     placeholder="Brief description of your educational platform">{{ form.site_description.value|default_if_none:"" }}</textarea>
                            <div class="form-text">Brief description of your educational platform (expandable)</div>
                            {% if form.site_description.errors %}
                                <div class="text-danger small mt-1">{{ form.site_description.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.contact_phone.id_for_label }}" class="form-label fw-bold">
                                    Contact Phone
                                </label>
                                {{ form.contact_phone }}
                                {% if form.contact_phone.errors %}
                                    <div class="text-danger small mt-1">{{ form.contact_phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.contact_address.id_for_label }}" class="form-label fw-bold">
                                Contact Address
                            </label>
                            <textarea name="{{ form.contact_address.name }}"
                                     id="{{ form.contact_address.id_for_label }}"
                                     class="form-control"
                                     rows="3"
                                     placeholder="Physical address or mailing address">{{ form.contact_address.value|default_if_none:"" }}</textarea>
                            <div class="form-text">Physical address or mailing address (expandable)</div>
                            {% if form.contact_address.errors %}
                                <div class="text-danger small mt-1">{{ form.contact_address.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Quiz Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-question-circle me-2 text-success"></i>
                            Quiz Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.quiz_questions_per_topic.id_for_label }}" class="form-label fw-bold">
                                    Questions per Quiz <span class="text-danger">*</span>
                                </label>
                                {{ form.quiz_questions_per_topic }}
                                <div class="form-text">Number of questions in each quiz (1-50)</div>
                                {% if form.quiz_questions_per_topic.errors %}
                                    <div class="text-danger small mt-1">{{ form.quiz_questions_per_topic.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.quiz_time_limit.id_for_label }}" class="form-label fw-bold">
                                    Quiz Time Limit (seconds) <span class="text-danger">*</span>
                                </label>
                                {{ form.quiz_time_limit }}
                                <div class="form-text">Total time for quiz completion (60-3600 seconds)</div>
                                {% if form.quiz_time_limit.errors %}
                                    <div class="text-danger small mt-1">{{ form.quiz_time_limit.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.question_time_limit.id_for_label }}" class="form-label fw-bold">
                                    Time per Question (seconds) <span class="text-danger">*</span>
                                </label>
                                {{ form.question_time_limit }}
                                <div class="form-text">Time limit for each question (10-300 seconds)</div>
                                {% if form.question_time_limit.errors %}
                                    <div class="text-danger small mt-1">{{ form.question_time_limit.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.explanation_display_time.id_for_label }}" class="form-label fw-bold">
                                    Explanation Display Time (seconds) <span class="text-danger">*</span>
                                </label>
                                {{ form.explanation_display_time }}
                                <div class="form-text">Time to show explanation after each question (1-30 seconds)</div>
                                {% if form.explanation_display_time.errors %}
                                    <div class="text-danger small mt-1">{{ form.explanation_display_time.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.minimum_pass_percentage.id_for_label }}" class="form-label fw-bold">
                                Minimum Pass Percentage <span class="text-danger">*</span>
                            </label>
                            {{ form.minimum_pass_percentage }}
                            <div class="form-text">Minimum score required to pass (1-100%)</div>
                            {% if form.minimum_pass_percentage.errors %}
                                <div class="text-danger small mt-1">{{ form.minimum_pass_percentage.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Test Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clipboard-check me-2 text-warning"></i>
                            Test Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.test_questions_per_topic.id_for_label }}" class="form-label fw-bold">
                                    Test Questions per Topic <span class="text-danger">*</span>
                                </label>
                                {{ form.test_questions_per_topic }}
                                <div class="form-text">Number of questions in each test (1-100)</div>
                                {% if form.test_questions_per_topic.errors %}
                                    <div class="text-danger small mt-1">{{ form.test_questions_per_topic.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.test_time_limit.id_for_label }}" class="form-label fw-bold">
                                    Test Time Limit (seconds) <span class="text-danger">*</span>
                                </label>
                                {{ form.test_time_limit }}
                                <div class="form-text">Total time for test completion (300-7200 seconds)</div>
                                {% if form.test_time_limit.errors %}
                                    <div class="text-danger small mt-1">{{ form.test_time_limit.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Exam Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-graduation-cap me-2 text-danger"></i>
                            Exam Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.exam_questions_per_level.id_for_label }}" class="form-label fw-bold">
                                    Exam Questions per Level <span class="text-danger">*</span>
                                </label>
                                {{ form.exam_questions_per_level }}
                                <div class="form-text">Number of questions in each exam (1-200)</div>
                                {% if form.exam_questions_per_level.errors %}
                                    <div class="text-danger small mt-1">{{ form.exam_questions_per_level.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.exam_time_limit.id_for_label }}" class="form-label fw-bold">
                                    Exam Time Limit (seconds) <span class="text-danger">*</span>
                                </label>
                                {{ form.exam_time_limit }}
                                <div class="form-text">Total time for exam completion (600-14400 seconds)</div>
                                {% if form.exam_time_limit.errors %}
                                    <div class="text-danger small mt-1">{{ form.exam_time_limit.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Website Branding -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-palette me-2 text-info"></i>
                            Website Branding
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.site_logo.id_for_label }}" class="form-label fw-bold">
                                    <i class="fas fa-image me-1"></i>Site Logo
                                    <span class="text-muted small">(Max: 2MB)</span>
                                </label>
                                {{ form.site_logo }}
                                <div class="form-text">Replaces "Pentora" text in navigation (recommended: 200x60px, PNG/JPG)</div>
                                {% if settings.site_logo %}
                                    <div class="mt-2">
                                        <img src="{{ settings.site_logo.url }}" alt="Current site logo" class="img-thumbnail" style="max-height: 60px;">
                                        <div class="form-text text-success">
                                            <i class="fas fa-check-circle me-1"></i>Currently active
                                        </div>
                                    </div>
                                {% else %}
                                    <div class="mt-2">
                                        <div class="bg-light border rounded d-flex align-items-center justify-content-center" style="width: 200px; height: 60px;">
                                            <span class="text-muted">{{ settings.site_name }}</span>
                                        </div>
                                        <div class="form-text text-muted">Using site name as fallback</div>
                                    </div>
                                {% endif %}
                                {% if form.site_logo.errors %}
                                    <div class="alert alert-danger mt-2 py-2">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        {{ form.site_logo.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.site_favicon.id_for_label }}" class="form-label fw-bold">
                                    <i class="fas fa-bookmark me-1"></i>Site Favicon
                                    <span class="text-muted small">(Max: 1MB)</span>
                                </label>
                                {{ form.site_favicon }}
                                <div class="form-text">Browser tab icon (recommended: 32x32px, ICO/PNG)</div>
                                {% if settings.site_favicon %}
                                    <div class="mt-2">
                                        <img src="{{ settings.site_favicon.url }}" alt="Current favicon" class="img-thumbnail" style="max-width: 32px;">
                                        <div class="form-text text-success">
                                            <i class="fas fa-check-circle me-1"></i>Currently active
                                        </div>
                                    </div>
                                {% else %}
                                    <div class="mt-2">
                                        <div class="bg-light border rounded d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                            <i class="fas fa-graduation-cap text-muted" style="font-size: 16px;"></i>
                                        </div>
                                        <div class="form-text text-muted">Using default icon</div>
                                    </div>
                                {% endif %}
                                {% if form.site_favicon.errors %}
                                    <div class="alert alert-danger mt-2 py-2">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        {{ form.site_favicon.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.hero_banner.id_for_label }}" class="form-label fw-bold">
                                    <i class="fas fa-image me-1"></i>Homepage Banner
                                    <span class="text-muted small">(Max: 5MB)</span>
                                </label>
                                {{ form.hero_banner }}
                                <div class="form-text">Replaces default homepage background (recommended: 1920x600px, JPG/PNG)</div>
                                {% if settings.hero_banner %}
                                    <div class="mt-2">
                                        <img src="{{ settings.hero_banner.url }}" alt="Current hero banner" class="img-thumbnail" style="max-width: 150px;">
                                        <div class="form-text text-success">
                                            <i class="fas fa-check-circle me-1"></i>Currently active
                                        </div>
                                    </div>
                                {% else %}
                                    <div class="mt-2">
                                        <div class="bg-light border rounded d-flex align-items-center justify-content-center" style="width: 150px; height: 47px;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                        <div class="form-text text-muted">Using default background</div>
                                    </div>
                                {% endif %}
                                {% if form.hero_banner.errors %}
                                    <div class="alert alert-danger mt-2 py-2">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        {{ form.hero_banner.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2 text-secondary"></i>
                            System Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {{ form.enable_email_notifications }}
                                    <label class="form-check-label fw-bold" for="{{ form.enable_email_notifications.id_for_label }}">
                                        Enable Email Notifications
                                    </label>
                                </div>
                                <div class="form-text">Send email notifications to users</div>
                                {% if form.enable_email_notifications.errors %}
                                    <div class="text-danger small mt-1">{{ form.enable_email_notifications.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {{ form.enable_user_registration }}
                                    <label class="form-check-label fw-bold" for="{{ form.enable_user_registration.id_for_label }}">
                                        Allow User Registration
                                    </label>
                                </div>
                                <div class="form-text">Allow new users to create accounts</div>
                                {% if form.enable_user_registration.errors %}
                                    <div class="text-danger small mt-1">{{ form.enable_user_registration.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.session_timeout_minutes.id_for_label }}" class="form-label fw-bold">
                                    Session Timeout (Minutes)
                                </label>
                                {{ form.session_timeout_minutes }}
                                <div class="form-text">How long users stay logged in</div>
                                {% if form.session_timeout_minutes.errors %}
                                    <div class="text-danger small mt-1">{{ form.session_timeout_minutes.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.max_login_attempts.id_for_label }}" class="form-label fw-bold">
                                    Max Login Attempts
                                </label>
                                {{ form.max_login_attempts }}
                                <div class="form-text">Failed attempts before account lockout</div>
                                {% if form.max_login_attempts.errors %}
                                    <div class="text-danger small mt-1">{{ form.max_login_attempts.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.max_retake_attempts.id_for_label }}" class="form-label fw-bold">
                                    Max Retake Attempts
                                </label>
                                {{ form.max_retake_attempts }}
                                <div class="form-text">Maximum quiz/test retake attempts</div>
                                {% if form.max_retake_attempts.errors %}
                                    <div class="text-danger small mt-1">{{ form.max_retake_attempts.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.daily_reminder_time.id_for_label }}" class="form-label fw-bold">
                                    Daily Reminder Time
                                </label>
                                {{ form.daily_reminder_time }}
                                <div class="form-text">Time for daily learning reminders</div>
                                {% if form.daily_reminder_time.errors %}
                                    <div class="text-danger small mt-1">{{ form.daily_reminder_time.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.max_file_size_mb.id_for_label }}" class="form-label fw-bold">
                                    Max File Size (MB)
                                </label>
                                {{ form.max_file_size_mb }}
                                <div class="form-text">Maximum upload file size</div>
                                {% if form.max_file_size_mb.errors %}
                                    <div class="text-danger small mt-1">{{ form.max_file_size_mb.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.allowed_file_types.id_for_label }}" class="form-label fw-bold">
                                    Allowed File Types
                                </label>
                                {{ form.allowed_file_types }}
                                <div class="form-text">Comma-separated file extensions</div>
                                {% if form.allowed_file_types.errors %}
                                    <div class="text-danger small mt-1">{{ form.allowed_file_types.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {{ form.maintenance_mode }}
                                    <label class="form-check-label fw-bold" for="{{ form.maintenance_mode.id_for_label }}">
                                        <span class="text-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            Enable Maintenance Mode
                                        </span>
                                    </label>
                                </div>
                                <div class="form-text text-warning">This will make the site unavailable to regular users</div>
                                {% if form.maintenance_mode.errors %}
                                    <div class="text-danger small mt-1">{{ form.maintenance_mode.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.maintenance_message.id_for_label }}" class="form-label fw-bold">
                                Maintenance Message
                            </label>
                            <textarea name="{{ form.maintenance_message.name }}"
                                     id="{{ form.maintenance_message.id_for_label }}"
                                     class="form-control"
                                     rows="3"
                                     placeholder="Message to display to users during maintenance">{{ form.maintenance_message.value|default_if_none:"" }}</textarea>
                            <div class="form-text">Message to display to users during maintenance (expandable)</div>
                            {% if form.maintenance_message.errors %}
                                <div class="text-danger small mt-1">{{ form.maintenance_message.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-2"></i>
                        Save Settings
                    </button>
                </div>

            </div>

            <!-- Sidebar Column -->
            <div class="col-lg-4">
                <!-- Current Settings Summary -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2 text-info"></i>
                            Current Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>Site Name:</strong><br>
                            <span class="text-muted">{{ settings.site_name }}</span>
                        </div>

                        <div class="mb-3">
                            <strong>Quiz Time Limit:</strong><br>
                            <span class="text-muted">{{ settings.quiz_time_limit }} seconds</span>
                        </div>

                        <div class="mb-3">
                            <strong>Questions per Quiz:</strong><br>
                            <span class="text-muted">{{ settings.quiz_questions_per_topic }}</span>
                        </div>

                        <div class="mb-3">
                            <strong>Passing Score:</strong><br>
                            <span class="text-muted">{{ settings.minimum_pass_percentage }}%</span>
                        </div>

                        <div class="mb-3">
                            <strong>Keep Users Logged In:</strong><br>
                            <span class="badge bg-{% if settings.keep_users_logged_in %}success{% else %}secondary{% endif %}">
                                {% if settings.keep_users_logged_in %}Enabled{% else %}Disabled{% endif %}
                            </span>
                        </div>

                        <div class="mb-3">
                            <strong>Email Notifications:</strong><br>
                            <span class="badge bg-{% if settings.enable_email_notifications %}success{% else %}secondary{% endif %}">
                                {% if settings.enable_email_notifications %}Enabled{% else %}Disabled{% endif %}
                            </span>
                        </div>

                        <div class="mb-3">
                            <strong>User Registration:</strong><br>
                            <span class="badge bg-{% if settings.allow_user_registration %}success{% else %}secondary{% endif %}">
                                {% if settings.allow_user_registration %}Open{% else %}Closed{% endif %}
                            </span>
                        </div>

                        <div class="mb-3">
                            <strong>Maintenance Mode:</strong><br>
                            <span class="badge bg-{% if settings.maintenance_mode %}warning{% else %}success{% endif %}">
                                {% if settings.maintenance_mode %}Active{% else %}Normal{% endif %}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- System Information -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-server me-2 text-secondary"></i>
                            System Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            <strong>Last Updated:</strong><br>
                            <small class="text-muted">{{ settings.updated_at|date:"M d, Y H:i" }}</small>
                        </div>

                        <div class="mb-2">
                            <strong>Updated By:</strong><br>
                            <small class="text-muted">
                                {% if settings.updated_by %}
                                    {{ settings.updated_by.get_full_name|default:settings.updated_by.email }}
                                {% else %}
                                    System
                                {% endif %}
                            </small>
                        </div>

                        <hr>

                        <div class="text-center">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt me-1"></i>
                                Settings are automatically saved<br>
                                and applied system-wide
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation and user experience enhancements
    const form = document.getElementById('settings-form');
    const submitBtn = form.querySelector('button[type="submit"]');

    // Add loading state to submit button
    form.addEventListener('submit', function() {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
        submitBtn.disabled = true;
    });

    // Auto-save indication for better UX
    const inputs = form.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        input.addEventListener('change', function() {
            // Add visual indication that changes need to be saved
            if (!form.classList.contains('form-modified')) {
                form.classList.add('form-modified');
                submitBtn.classList.add('btn-warning');
                submitBtn.classList.remove('btn-primary');
                submitBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Save Changes';
            }
        });
    });

    // Time conversion helpers for better UX
    const timeInputs = [
        'quiz_time_limit',
        'test_time_limit',
        'exam_time_limit',
        'question_time_limit',
        'explanation_display_time'
    ];

    timeInputs.forEach(inputName => {
        const input = document.querySelector(`input[name="${inputName}"]`);
        if (input) {
            // Add time conversion display
            const helpText = input.parentNode.querySelector('.form-text');
            if (helpText) {
                const updateTimeDisplay = () => {
                    const seconds = parseInt(input.value) || 0;
                    const minutes = Math.floor(seconds / 60);
                    const remainingSeconds = seconds % 60;

                    let timeDisplay = '';
                    if (minutes > 0) {
                        timeDisplay = `${minutes}m ${remainingSeconds}s`;
                    } else {
                        timeDisplay = `${seconds}s`;
                    }

                    // Update help text to include time conversion
                    const originalText = helpText.textContent.split(' (')[0];
                    helpText.textContent = `${originalText} (${timeDisplay})`;
                };

                input.addEventListener('input', updateTimeDisplay);
                updateTimeDisplay(); // Initial display
            }
        }
    });

    // Maintenance mode warning
    const maintenanceCheckbox = document.querySelector('input[name="maintenance_mode"]');
    if (maintenanceCheckbox) {
        maintenanceCheckbox.addEventListener('change', function() {
            if (this.checked) {
                if (!confirm('⚠️ WARNING: Enabling maintenance mode will make the site unavailable to regular users.\n\nAre you sure you want to continue?')) {
                    this.checked = false;
                }
            }
        });
    }
});
</script>
{% endblock %}
