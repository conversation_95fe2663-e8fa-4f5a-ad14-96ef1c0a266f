#!/usr/bin/env python3
"""
Test the improved CSV import system
"""
import os
import sys
import django

# Setup Django
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pentora_platform.settings')
django.setup()

from core.utils.csv_import import CSVImporter
from django.contrib.auth import get_user_model

def test_improved_import():
    """Test the improved CSV import with conflict handling"""
    
    print("🧪 TESTING IMPROVED CSV IMPORT SYSTEM...")
    print("=" * 60)
    
    # Get a user for the import
    User = get_user_model()
    user = User.objects.filter(is_superuser=True).first()
    if not user:
        print("❌ No superuser found. Please create one first.")
        return
    
    # Read the CSV file
    try:
        with open('Grade_9_questions_PERFECT.csv', 'r', encoding='utf-8') as f:
            file_content = f.read()
    except FileNotFoundError:
        print("❌ Grade_9_questions_PERFECT.csv not found")
        return
    
    # Create importer with partial mode (more tolerant)
    importer = CSVImporter(
        import_type='questions',
        file_content=file_content,
        user=user,
        import_mode='partial'  # This allows skipping problematic rows
    )
    
    print(f"📁 File loaded: {len(file_content)} characters")
    
    # Test the import
    try:
        result = importer.import_data()
        
        print(f"\n📊 IMPORT RESULTS:")
        print(f"   ✅ Successful rows: {importer.successful_rows}")
        print(f"   ❌ Failed rows: {importer.failed_rows}")
        print(f"   ⏭️  Skipped rows: {len(importer.skipped_rows)}")
        print(f"   📈 Success rate: {(importer.successful_rows/(importer.successful_rows + importer.failed_rows))*100:.1f}%")
        
        if importer.errors:
            print(f"\n❌ ERRORS (first 5):")
            for error in importer.errors[:5]:
                print(f"   {error}")
        
        if importer.skipped_rows:
            print(f"\n⏭️  SKIPPED ROWS (first 5):")
            for skipped in importer.skipped_rows[:5]:
                print(f"   Row {skipped['row_number']}: {skipped['error']}")
        
        if result.get('success'):
            print(f"\n🎉 IMPORT COMPLETED SUCCESSFULLY!")
            print(f"   You can now use the questions in your system!")
        else:
            print(f"\n⚠️  IMPORT COMPLETED WITH ISSUES")
            print(f"   Check the errors above for details")
            
    except Exception as e:
        print(f"❌ IMPORT FAILED: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_improved_import()
