{% extends 'base.html' %}

{% block title %}Reset Password - Pentora{% endblock %}

{% block extra_css %}
<style>
    .reset-container {
        background: linear-gradient(135deg, #F9FAFB 0%, #E5E7EB 100%);
        min-height: calc(100vh - 80px);
    }
    
    .reset-card {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        border: 1px solid #E5E7EB;
    }
    
    .form-input {
        border: 2px solid #E5E7EB;
        border-radius: 0.75rem;
        padding: 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        background: #F9FAFB;
    }
    
    .form-input:focus {
        border-color: var(--primary-blue);
        background: white;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        outline: none;
    }
    
    .form-label {
        font-weight: 600;
        color: var(--text-dark);
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
    }
    
    .btn-primary-custom {
        background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
        border: none;
        border-radius: 0.75rem;
        padding: 1rem;
        font-weight: 600;
        font-size: 1rem;
        color: white;
        transition: all 0.2s ease;
    }
    
    .btn-primary-custom:hover {
        transform: translateY(-1px);
        box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.4);
    }
    
    .reset-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #EF4444, #DC2626);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        box-shadow: 0 10px 25px -5px rgba(239, 68, 68, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="reset-container flex items-center justify-center px-4 py-8">
    <div class="w-full max-w-md">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="reset-icon">
                <i class="fas fa-key text-white text-2xl"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Reset Your Password</h1>
            <p class="text-gray-600 text-lg">Enter your email to receive reset instructions</p>
        </div>

        <!-- Reset Form -->
        <div class="reset-card p-8">
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- Email Field -->
                <div>
                    <label class="form-label block">
                        <i class="fas fa-envelope text-blue-500 mr-2"></i>
                        Email Address
                    </label>
                    <input 
                        type="email" 
                        name="email" 
                        placeholder="Enter your email address" 
                        class="form-input w-full"
                        required 
                        autofocus
                        autocomplete="email"
                    >
                    <div class="text-xs text-gray-500 mt-1">
                        <i class="fas fa-info-circle mr-1"></i>
                        We'll send password reset instructions to this email
                    </div>
                </div>

                <!-- Submit Button -->
                <button type="submit" class="btn-primary-custom w-full">
                    <i class="fas fa-paper-plane mr-2"></i>
                    Send Reset Instructions
                </button>
            </form>

            <!-- Divider -->
            <div class="relative my-8">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-200"></div>
                </div>
                <div class="relative flex justify-center text-sm">
                    <span class="px-4 bg-white text-gray-500 font-medium">Remember your password?</span>
                </div>
            </div>

            <!-- Back to Login -->
            <div class="text-center">
                <a href="{% url 'users:login' %}" class="btn btn-outline w-full border-2 border-gray-200 hover:border-blue-300 hover:bg-blue-50 text-gray-700 hover:text-blue-600 font-medium py-3 rounded-xl">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Sign In
                </a>
            </div>
        </div>

        <!-- Help Section -->
        <div class="mt-8 text-center">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center justify-center mb-2">
                    <i class="fas fa-question-circle text-blue-500 mr-2"></i>
                    <span class="font-medium text-blue-800">Need Help?</span>
                </div>
                <p class="text-sm text-blue-700 mb-3">
                    Having trouble? Our support team is here to help you get back to learning.
                </p>
                <a href="{% url 'core:contact' %}" class="text-xs text-blue-600 hover:text-blue-800 font-medium">
                    Contact Support Team
                </a>
            </div>
        </div>

        <!-- Security Notice -->
        <div class="mt-6 text-center">
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-center mb-2">
                    <i class="fas fa-shield-alt text-gray-500 mr-2"></i>
                    <span class="font-medium text-gray-700">Security Notice</span>
                </div>
                <p class="text-xs text-gray-600">
                    Password reset links expire after 1 hour for your security. 
                    If you don't receive an email, check your spam folder.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
