{% extends 'base.html' %}

{% block title %}Billing Dashboard - Pentora{% endblock %}

{% block extra_css %}
<style>
    .dashboard-card {
        transition: all 0.3s ease;
    }

    .dashboard-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    }

    .status-indicator {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-base-200 via-base-100 to-base-200">
    <div class="container mx-auto px-4 py-8 lg:py-12">
        <!-- Header -->
        <div class="mb-8 lg:mb-12">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-3xl lg:text-4xl font-bold mb-2 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                        Billing Dashboard
                    </h1>
                    <p class="text-base-content/70 text-lg">Manage your subscription and billing information</p>
                </div>
                <div class="flex gap-2">
                    <a href="{% url 'billing:plans' %}" class="btn btn-outline btn-sm">
                        <i class="fas fa-list mr-2"></i>
                        View Plans
                    </a>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Subscription Status -->
            <div class="lg:col-span-2">
                <div class="card bg-base-100 shadow-lg mb-6">
                    <div class="card-body">
                        <h2 class="card-title mb-4">Current Subscription</h2>
                        
                        {% if subscription %}
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                <div>
                                    <div class="text-sm text-base-content/70">Plan</div>
                                    <div class="text-lg font-semibold">{{ subscription.plan.name }}</div>
                                </div>
                                <div>
                                    <div class="text-sm text-base-content/70">Status</div>
                                    <div class="badge {% if subscription.status == 'active' %}badge-success{% elif subscription.status == 'trialing' %}badge-info{% else %}badge-warning{% endif %}">
                                        {{ subscription.get_status_display }}
                                    </div>
                                </div>
                                <div>
                                    <div class="text-sm text-base-content/70">Price</div>
                                    <div class="text-lg font-semibold">
                                        {% if subscription.plan.is_free %}
                                            Free
                                        {% else %}
                                            ${{ subscription.plan.price }}/{{ subscription.plan.billing_cycle }}
                                        {% endif %}
                                    </div>
                                </div>
                                <div>
                                    <div class="text-sm text-base-content/70">
                                        {% if subscription.is_trial %}Next Billing{% else %}Renewal Date{% endif %}
                                    </div>
                                    <div class="text-lg font-semibold">
                                        {% if subscription.current_period_end %}
                                            {{ subscription.current_period_end|date:"M d, Y" }}
                                        {% else %}
                                            N/A
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Subscription Actions -->
                            <div class="flex flex-wrap gap-2">
                                <a href="{% url 'billing:plans' %}" class="btn btn-primary btn-sm">
                                    Change Plan
                                </a>
                                
                                {% if subscription.status == 'active' %}
                                    <form method="post" action="{% url 'billing:cancel' %}" class="inline">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-outline btn-error btn-sm" 
                                                onclick="return confirm('Are you sure you want to cancel your subscription?')">
                                            Cancel Subscription
                                        </button>
                                    </form>
                                {% elif subscription.status == 'canceled' %}
                                    <form method="post" action="{% url 'billing:reactivate' %}" class="inline">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-success btn-sm">
                                            Reactivate Subscription
                                        </button>
                                    </form>
                                {% endif %}
                            </div>

                            <!-- Usage Information -->
                            {% if subscription.plan.max_subjects or subscription.plan.max_quizzes_per_day %}
                            <div class="divider"></div>
                            <h3 class="font-semibold mb-2">Usage Limits</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {% if subscription.plan.max_subjects %}
                                <div>
                                    <div class="text-sm text-base-content/70">Subjects</div>
                                    <div>Up to {{ subscription.plan.max_subjects }}</div>
                                </div>
                                {% endif %}
                                {% if subscription.plan.max_quizzes_per_day %}
                                <div>
                                    <div class="text-sm text-base-content/70">Daily Quizzes</div>
                                    <div>Up to {{ subscription.plan.max_quizzes_per_day }}</div>
                                </div>
                                {% endif %}
                            </div>
                            {% endif %}

                        {% else %}
                            <div class="text-center py-8">
                                <i class="fas fa-credit-card text-4xl text-base-content/30 mb-4"></i>
                                <h3 class="text-lg font-semibold mb-2">No Active Subscription</h3>
                                <p class="text-base-content/70 mb-4">Choose a plan to get started with premium features.</p>
                                <a href="{% url 'billing:plans' %}" class="btn btn-primary">
                                    View Plans
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Payment History -->
                <div class="card bg-base-100 shadow-lg">
                    <div class="card-body">
                        <h2 class="card-title mb-4">Payment History</h2>
                        
                        {% if payments %}
                            <div class="overflow-x-auto">
                                <table class="table table-zebra">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for payment in payments %}
                                        <tr>
                                            <td>{{ payment.created_at|date:"M d, Y" }}</td>
                                            <td>${{ payment.amount }}</td>
                                            <td>
                                                <div class="badge {% if payment.status == 'succeeded' %}badge-success{% elif payment.status == 'failed' %}badge-error{% else %}badge-warning{% endif %} badge-sm">
                                                    {{ payment.get_status_display }}
                                                </div>
                                            </td>
                                            <td>{{ payment.description|default:"Subscription payment" }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-8">
                                <i class="fas fa-receipt text-4xl text-base-content/30 mb-4"></i>
                                <p class="text-base-content/70">No payment history yet.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Quick Actions -->
                <div class="card bg-base-100 shadow-lg">
                    <div class="card-body">
                        <h3 class="card-title text-lg mb-4">Quick Actions</h3>
                        <div class="space-y-2">
                            <a href="{% url 'billing:plans' %}" class="btn btn-outline btn-sm w-full justify-start">
                                <i class="fas fa-list mr-2"></i>
                                View All Plans
                            </a>
                            {% if subscription %}
                            <button class="btn btn-outline btn-sm w-full justify-start" onclick="downloadInvoice()">
                                <i class="fas fa-download mr-2"></i>
                                Download Invoice
                            </button>
                            {% endif %}
                            <a href="{% url 'core:contact' %}" class="btn btn-outline btn-sm w-full justify-start">
                                <i class="fas fa-support mr-2"></i>
                                Contact Support
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Billing Information -->
                {% if subscription %}
                <div class="card bg-base-100 shadow-lg">
                    <div class="card-body">
                        <h3 class="card-title text-lg mb-4">Billing Information</h3>
                        <div class="space-y-3">
                            <div>
                                <div class="text-sm text-base-content/70">Next Billing Date</div>
                                <div class="font-semibold">
                                    {% if subscription.current_period_end %}
                                        {{ subscription.current_period_end|date:"M d, Y" }}
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </div>
                            </div>
                            <div>
                                <div class="text-sm text-base-content/70">Auto Renewal</div>
                                <div class="font-semibold">
                                    {% if subscription.auto_renew %}
                                        <span class="text-success">Enabled</span>
                                    {% else %}
                                        <span class="text-warning">Disabled</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
function downloadInvoice() {
    // This would implement invoice download functionality
    alert('Invoice download functionality will be implemented with payment provider integration.');
}
</script>
{% endblock %}
