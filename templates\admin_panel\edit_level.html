{% extends 'admin_panel/base.html' %}

{% block title %}Edit Level{% endblock %}
{% block page_title %}Edit Level: {{ object.name }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    Edit Level: {{ object.name }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.subject.id_for_label }}" class="form-label">
                                Subject <span class="text-danger">*</span>
                            </label>
                            {{ form.subject }}
                            {% if form.subject.errors %}
                                <div class="text-danger small">{{ form.subject.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.level_number.id_for_label }}" class="form-label">
                                GES Level Number <span class="text-danger">*</span>
                            </label>
                            {{ form.level_number }}
                            <div class="form-text">
                                {{ form.level_number.help_text }}
                            </div>
                            {% if form.level_number.errors %}
                                <div class="text-danger small">{{ form.level_number.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">
                            GES Level Name <span class="text-danger">*</span>
                        </label>
                        {{ form.name }}
                        <div class="form-text">
                            {{ form.name.help_text }}
                        </div>
                        {% if form.name.errors %}
                            <div class="text-danger small">{{ form.name.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">
                            Description <span class="text-danger">*</span>
                        </label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger small">{{ form.description.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    Active
                                </label>
                            </div>
                            <div class="form-text">
                                Only active levels will be visible to students
                            </div>
                            {% if form.is_active.errors %}
                                <div class="text-danger small">{{ form.is_active.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Current Statistics -->
                    <div class="mb-4">
                        <h6>Current Statistics</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-light rounded">
                                    <h4 class="text-primary mb-1">{{ object.topics.count }}</h4>
                                    <small class="text-muted">Topics</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-light rounded">
                                    <h4 class="text-info mb-1">0</h4>
                                    <small class="text-muted">Questions</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-light rounded">
                                    <h4 class="text-success mb-1">0</h4>
                                    <small class="text-muted">Students</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Preview Section -->
                    <div class="mb-4">
                        <h6>Preview</h6>
                        <div class="border rounded p-3 bg-light">
                            <div class="d-flex align-items-center">
                                <div id="preview-subject-icon" 
                                     class="d-flex align-items-center justify-content-center me-3" 
                                     style="width: 40px; height: 40px; background-color: {{ object.subject.color }}; border-radius: 6px; color: white;">
                                    <i class="fas {{ object.subject.icon }}"></i>
                                </div>
                                <div>
                                    <h6 id="preview-name" class="mb-1">{{ object.name }}</h6>
                                    <p id="preview-description" class="mb-1 text-muted">{{ object.description }}</p>
                                    <small class="text-muted">
                                        <span id="preview-subject">{{ object.subject.name }}</span> • 
                                        Level <span id="preview-number">{{ object.level_number }}</span> • 
                                        GES Curriculum
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'admin_panel:levels' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Levels
                        </a>
                        <div>
                            <button type="button" 
                                    class="btn btn-danger me-2"
                                    onclick="confirmDelete('{{ object.name }}', '{% url 'admin_panel:delete_level' object.id %}')">
                                <i class="fas fa-trash me-2"></i>
                                Delete
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Update Level
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the level "<span id="deleteItemName"></span>"?</p>
                <p class="text-danger"><strong>Warning:</strong> This will also delete all associated topics and questions.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const subjectSelect = document.getElementById('{{ form.subject.id_for_label }}');
    const nameSelect = document.getElementById('{{ form.name.id_for_label }}');
    const descriptionInput = document.getElementById('{{ form.description.id_for_label }}');
    const levelNumberInput = document.getElementById('{{ form.level_number.id_for_label }}');
    
    const previewName = document.getElementById('preview-name');
    const previewDescription = document.getElementById('preview-description');
    const previewSubject = document.getElementById('preview-subject');
    const previewNumber = document.getElementById('preview-number');
    const previewIcon = document.getElementById('preview-subject-icon');
    
    function updatePreview() {
        // Update name
        const selectedName = nameSelect.options[nameSelect.selectedIndex];
        previewName.textContent = selectedName.text || 'Level Name';
        
        // Auto-populate level number based on name
        const levelName = selectedName.text;
        if (levelName.includes('Primary')) {
            const num = levelName.split(' ')[1];
            levelNumberInput.value = num;
            previewNumber.textContent = num;
        } else if (levelName.includes('JHS')) {
            const num = parseInt(levelName.split(' ')[1]) + 6;
            levelNumberInput.value = num;
            previewNumber.textContent = num;
        } else if (levelName.includes('SHS')) {
            const num = parseInt(levelName.split(' ')[1]) + 9;
            levelNumberInput.value = num;
            previewNumber.textContent = num;
        }
        
        // Update description
        previewDescription.textContent = descriptionInput.value || 'Level description will appear here';
        
        // Update subject
        const selectedOption = subjectSelect.options[subjectSelect.selectedIndex];
        previewSubject.textContent = selectedOption.text || 'Subject';
    }
    
    // Add event listeners
    subjectSelect.addEventListener('change', updatePreview);
    nameSelect.addEventListener('change', updatePreview);
    descriptionInput.addEventListener('input', updatePreview);
    levelNumberInput.addEventListener('input', updatePreview);
});

function confirmDelete(itemName, deleteUrl) {
    document.getElementById('deleteItemName').textContent = itemName;
    document.getElementById('deleteForm').action = deleteUrl;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
