# Generated by Django 5.2.1 on 2025-07-05 14:54

import django.core.validators
import django.utils.timezone
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CSVImportLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('import_type', models.CharField(choices=[('questions', 'Questions'), ('study_notes', 'Study Notes'), ('users', 'Users'), ('subjects', 'Subjects'), ('topics', 'Topics')], max_length=20)),
                ('file_name', models.CharField(max_length=255)),
                ('file_path', models.CharField(blank=True, max_length=500)),
                ('total_rows', models.PositiveIntegerField(default=0)),
                ('successful_rows', models.PositiveIntegerField(default=0)),
                ('failed_rows', models.PositiveIntegerField(default=0)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('partial', 'Partially Completed')], default='pending', max_length=20)),
                ('error_log', models.TextField(blank=True)),
                ('started_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'CSV Import Log',
                'verbose_name_plural': 'CSV Import Logs',
                'db_table': 'csv_import_logs',
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='HeroSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(default='An Easy Way to Learn and Grow', max_length=200)),
                ('subtitle', models.CharField(default='No matter your background', max_length=200)),
                ('description', models.TextField(default='Quality education for everyone. Start your learning journey today with our comprehensive, mobile-friendly platform designed for learners worldwide.')),
                ('hero_image', models.ImageField(blank=True, help_text='Upload a high-quality image for the hero section (recommended: 1920x1080px)', null=True, upload_to='hero_images/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'webp'])])),
                ('cta_text', models.CharField(default='Get Started Free', max_length=50)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Hero Section',
                'verbose_name_plural': 'Hero Sections',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('notification_type', models.CharField(choices=[('info', 'Information'), ('success', 'Success'), ('warning', 'Warning'), ('error', 'Error'), ('achievement', 'Achievement'), ('reminder', 'Reminder')], default='info', max_length=20)),
                ('is_read', models.BooleanField(default=False)),
                ('is_sent', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Notification',
                'verbose_name_plural': 'Notifications',
                'db_table': 'notifications',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SiteStatistic',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('label', models.CharField(max_length=100)),
                ('value', models.CharField(max_length=50)),
                ('icon', models.CharField(default='fas fa-chart-line', help_text="FontAwesome icon class (e.g., 'fas fa-users')", max_length=50)),
                ('order', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Site Statistic',
                'verbose_name_plural': 'Site Statistics',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='SystemSettings',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('key', models.CharField(max_length=100, unique=True)),
                ('value', models.TextField()),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'System Setting',
                'verbose_name_plural': 'System Settings',
                'db_table': 'system_settings',
                'ordering': ['key'],
            },
        ),
    ]
