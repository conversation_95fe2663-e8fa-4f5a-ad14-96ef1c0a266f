#!/usr/bin/env python
"""
Simple script to run Django migrations
"""

import os
import sys
import django
from django.core.management import call_command

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pentora_platform.settings')
django.setup()

def main():
    """Run migrations"""
    print("🚀 Running Django migrations...")
    
    try:
        # Run migrations
        call_command('migrate', verbosity=2)
        print("✅ Migrations completed successfully!")
        
        # Run collectstatic for production
        print("\n📦 Collecting static files...")
        call_command('collectstatic', '--noinput', verbosity=1)
        print("✅ Static files collected successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
