{% extends 'base.html' %}
{% load markdown_extras %}

{% block title %}{{ topic.title }} - {{ level.name }} {{ subject.name }} - Pentora{% endblock %}

{% block extra_head %}
{% csrf_token %}
<meta name="csrf-token" content="{{ csrf_token }}">
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
    <!-- Modern Header with Gradient Background -->
    <div class="relative bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-800 overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 bg-black/10"></div>
        <div class="absolute inset-0 bg-gradient-to-r from-blue-600/90 to-indigo-800/90"></div>

        <!-- Decorative Elements -->
        <div class="absolute top-0 left-0 w-full h-full overflow-hidden">
            <div class="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
            <div class="absolute top-20 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
            <div class="absolute bottom-10 right-20 w-16 h-16 bg-white/10 rounded-full blur-lg"></div>
        </div>

        <div class="relative container mx-auto px-4 py-4 sm:py-6">
            <div class="max-w-6xl mx-auto">
                <!-- Compact Breadcrumb -->
                <nav class="flex items-center space-x-2 text-sm text-white/80 mb-4">
                    <a href="{% url 'subjects:list' %}" class="hover:text-white transition-colors duration-200 flex items-center">
                        <i class="fas fa-home mr-1"></i>
                        Learn
                    </a>
                    <i class="fas fa-chevron-right text-xs text-white/60"></i>
                    <a href="{% url 'subjects:levels' subject.id %}" class="hover:text-white transition-colors duration-200">{{ subject.name }}</a>
                    <i class="fas fa-chevron-right text-xs text-white/60"></i>
                    <a href="{% url 'subjects:level_detail' subject.id level.id %}" class="hover:text-white transition-colors duration-200">{{ level.name }}</a>
                    <i class="fas fa-chevron-right text-xs text-white/60"></i>
                    <span class="text-white font-medium">{{ topic.title }}</span>
                </nav>

                <!-- Compact Hero Content -->
                <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
                    <div class="flex-1">
                        <!-- Subject Badge -->
                        <div class="inline-flex items-center px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-white/90 text-sm font-medium mb-3">
                            <i class="fas fa-graduation-cap mr-2"></i>
                            {{ level.name }} • {{ subject.name }}
                        </div>

                        <h1 class="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-3 leading-tight">
                            {{ topic.title }}
                        </h1>

                        {% if topic.description %}
                            <p class="text-base sm:text-lg text-white/90 leading-relaxed mb-4 max-w-3xl">
                                {{ topic.description }}
                            </p>
                        {% endif %}

                        <!-- Compact Topic Metadata -->
                        <div class="flex flex-wrap items-center gap-3 text-white/80">
                            {% if topic.estimated_duration %}
                            <div class="flex items-center bg-white/10 backdrop-blur-sm rounded-lg px-2 py-1">
                                <i class="fas fa-clock mr-1 text-blue-200 text-sm"></i>
                                <span class="text-xs font-medium">{{ topic.estimated_duration }} min</span>
                            </div>
                            {% endif %}
                            {% if topic.difficulty_level %}
                            <div class="flex items-center bg-white/10 backdrop-blur-sm rounded-lg px-2 py-1">
                                <i class="fas fa-signal mr-1 text-green-200 text-sm"></i>
                                <span class="text-xs font-medium capitalize">{{ topic.difficulty_level }}</span>
                            </div>
                            {% endif %}
                            <div class="flex items-center bg-white/10 backdrop-blur-sm rounded-lg px-2 py-1">
                                <i class="fas fa-list-ol mr-1 text-purple-200 text-sm"></i>
                                <span class="text-xs font-medium">Topic {{ topic.order }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Smaller Hero Icon -->
                    <div class="flex-shrink-0 lg:block hidden">
                        <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-xl">
                            <i class="fas fa-book-open text-white text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="lg:container lg:mx-auto lg:px-4">
        <div class="max-w-6xl mx-auto">

            <!-- Study Notes Section - Always Visible -->
            <div id="studyNotesSection" class="lg:bg-white/80 lg:backdrop-blur-sm lg:rounded-2xl lg:shadow-xl lg:border lg:border-white/20 mb-12 overflow-hidden">
                <div class="hidden lg:block bg-gradient-to-r from-blue-600 to-indigo-600 p-6">
                    <h2 class="text-2xl font-bold text-white flex items-center">
                        <i class="fas fa-book-open mr-3"></i>
                        Study Materials
                    </h2>
                </div>

                <div class="px-4 py-6 sm:px-6 lg:p-8">
                    <!-- CSRF Token for AJAX requests -->
                    {% csrf_token %}

                    <!-- Study Notes Content -->
                    {% for note_data in notes_with_progress %}
                        <div class="mb-8 last:mb-0" data-note-id="{{ note_data.note.id }}">
                            <!-- Enhanced study card with better interactions -->
                            <div class="lg:bg-gradient-to-br lg:from-blue-50 lg:via-indigo-50 lg:to-purple-50 lg:border lg:border-blue-200/50 lg:rounded-xl lg:shadow-lg p-4 sm:p-6 lg:p-8 topic-card group hover:shadow-xl transition-all duration-300 relative overflow-hidden">
                                <!-- Enhanced visual indicator -->
                                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-600 group-hover:h-2 transition-all duration-300"></div>
                                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-4">
                                    <h3 class="text-xl sm:text-2xl font-bold text-gray-800 flex items-center">
                                        <div class="w-8 h-8 sm:w-10 sm:h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                                            <i class="fas fa-file-alt text-white text-sm sm:text-base"></i>
                                        </div>
                                        {{ note_data.note.title }}
                                    </h3>

                                    {% if user.is_authenticated %}
                                    <!-- Mark as Read Button -->
                                    {% if note_data.is_read %}
                                        <button class="mark-read-btn bg-gray-500 text-white px-4 py-2 rounded-lg font-medium cursor-not-allowed flex items-center w-full sm:w-auto justify-center"
                                                id="mark-read-{{ note_data.note.id }}" disabled>
                                            <i class="fas fa-check-circle mr-2"></i>
                                            <span class="btn-text">Read ✓</span>
                                        </button>
                                    {% else %}
                                        <button onclick="markNoteAsRead('{{ note_data.note.id }}')"
                                                class="mark-read-btn bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center w-full sm:w-auto justify-center"
                                                id="mark-read-{{ note_data.note.id }}">
                                            <i class="fas fa-check mr-2"></i>
                                            <span class="btn-text">Mark as Read</span>
                                        </button>
                                    {% endif %}
                                    {% endif %}
                                </div>

                                <div class="study-content prose prose-lg max-w-none text-gray-700 leading-relaxed">
                                    {{ note_data.note.content|markdown }}
                                </div>
                            </div>
                        </div>
                    {% empty %}
                        <div class="bg-gradient-to-br from-amber-50 to-orange-50 border border-amber-200 rounded-xl p-8 text-center">
                            <div class="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-info-circle text-amber-600 text-2xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-amber-800 mb-3">Study Materials Coming Soon!</h3>
                            <p class="text-amber-700 leading-relaxed max-w-2xl mx-auto">
                                We're preparing comprehensive study materials for <strong>{{ topic.title }}</strong>
                                aligned with international educational standards.
                            </p>
                        </div>
                    {% endfor %}

                    <!-- Single Note Pagination Controls -->
                    {% if total_notes > 1 %}
                    <div class="mt-8 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6">
                        <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
                            <!-- Previous Button -->
                            {% if has_previous %}
                                <a href="?note={{ previous_index }}"
                                   class="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center justify-center">
                                    <i class="fas fa-chevron-left mr-2"></i>
                                    Previous Note
                                </a>
                            {% else %}
                                <div class="w-full sm:w-auto"></div>
                            {% endif %}

                            <!-- Progress Indicator -->
                            <div class="text-center">
                                <div class="text-lg font-bold text-gray-800">{{ current_note_index }} of {{ total_notes }}</div>
                                <div class="text-sm text-gray-600">Study Notes</div>
                                <div class="w-32 bg-gray-200 rounded-full h-2 mt-2">
                                    <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                         style="width: {% widthratio current_note_index total_notes 100 %}%"></div>
                                </div>
                            </div>

                            <!-- Next Button -->
                            {% if has_next %}
                                <a href="?note={{ next_index }}"
                                   class="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center justify-center">
                                    Next Note
                                    <i class="fas fa-chevron-right ml-2"></i>
                                </a>
                            {% else %}
                                <div class="w-full sm:w-auto text-center">
                                    <div class="bg-green-600 text-white px-6 py-3 rounded-lg font-medium flex items-center justify-center">
                                        <i class="fas fa-check-circle mr-2"></i>
                                        All Notes Read!
                                    </div>
                                </div>
                            {% endif %}
                        </div>

                        <!-- Quick Navigation (if more than 3 notes) -->
                        {% if total_notes > 3 %}
                        <div class="mt-6 pt-6 border-t border-blue-200">
                            <div class="text-center mb-4">
                                <h4 class="text-lg font-semibold text-gray-800">Quick Navigation</h4>
                            </div>
                            <div class="flex flex-wrap justify-center gap-2">
                                {% for i in total_notes|make_list %}
                                    {% if forloop.counter <= total_notes %}
                                        {% if forloop.counter == current_note_index %}
                                            <span class="bg-blue-600 text-white px-3 py-2 rounded-lg font-medium text-sm">
                                                Note {{ forloop.counter }}
                                            </span>
                                        {% else %}
                                            <a href="?note={{ forloop.counter }}"
                                               class="bg-white hover:bg-blue-100 text-blue-600 border border-blue-300 px-3 py-2 rounded-lg font-medium text-sm transition-all duration-200">
                                                Note {{ forloop.counter }}
                                            </a>
                                        {% endif %}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- Start Quiz Section - Always at bottom -->
                    {% if topic.get_questions_count > 0 %}
                        <div class="mt-12 pt-8 border-t border-gray-200">
                            <div class="text-center">
                                <div class="mb-6">
                                    <h3 class="text-2xl font-bold text-gray-800 mb-3">Ready to Test Your Knowledge?</h3>
                                    <p class="text-gray-600 text-lg">Take the quiz to check your understanding of this topic</p>
                                </div>

                                <div class="flex flex-wrap items-center justify-center gap-4 sm:gap-6 text-sm text-gray-500 mb-6">
                                    <div class="flex items-center">
                                        <i class="fas fa-question-circle mr-2 text-blue-500"></i>
                                        <span>Interactive Questions</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-clock mr-2 text-green-500"></i>
                                        <span>Instant Results</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-chart-line mr-2 text-purple-500"></i>
                                        <span>Track Progress</span>
                                    </div>
                                </div>

                                <a href="{% url 'content:quiz' topic.id %}"
                                   class="inline-flex items-center justify-center bg-gradient-to-r from-blue-600 to-blue-700 text-white py-4 px-8 rounded-2xl font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl text-lg group">
                                    <i class="fas fa-play mr-3 group-hover:scale-110 transition-transform duration-200"></i>
                                    Start Quiz
                                </a>
                            </div>
                        </div>
                    {% else %}
                        <div class="mt-12 pt-8 border-t border-gray-200">
                            <div class="text-center">
                                <div class="bg-gray-50 rounded-xl p-8">
                                    <i class="fas fa-info-circle text-gray-400 text-3xl mb-4"></i>
                                    <h3 class="text-xl font-semibold text-gray-600 mb-2">Quiz Coming Soon</h3>
                                    <p class="text-gray-500">Quiz content is being prepared for this topic</p>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>



            <!-- Modern Navigation -->
            <div class="flex flex-col sm:flex-row items-center justify-between gap-4 bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                <a href="{% url 'subjects:level_detail' subject.id level.id %}"
                   class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-xl font-medium hover:from-gray-700 hover:to-gray-800 transition-all duration-300 shadow-lg hover:shadow-xl group">
                    <i class="fas fa-arrow-left mr-3 group-hover:-translate-x-1 transition-transform duration-200"></i>
                    Back to {{ level.name }}
                </a>

                <!-- Progress Indicator -->
                <div class="flex items-center space-x-2 text-sm text-gray-600">
                    <i class="fas fa-map-marker-alt text-blue-500"></i>
                    <span>Topic {{ topic.order }} of {{ level.name }}</span>
                </div>

                <!-- Future: Next/Previous Topic Navigation -->
                <div class="flex items-center space-x-3 opacity-50">
                    <span class="text-sm text-gray-500">Navigation coming soon</span>
                </div>
            </div>
        </div>
    </div>
</div>
<script>

// Mark note as read function
function markNoteAsRead(noteId) {
    const button = document.getElementById(`mark-read-${noteId}`);
    const btnText = button.querySelector('.btn-text');
    const btnIcon = button.querySelector('i');

    // Show loading state
    btnText.textContent = 'Marking...';
    btnIcon.className = 'fas fa-spinner fa-spin mr-2';
    button.disabled = true;

    // Get CSRF token
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                     document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                     getCookie('csrftoken');

    if (!csrfToken) {
        console.error('CSRF token not found');
        // Reset button state
        btnText.textContent = 'Mark as Read';
        btnIcon.className = 'fas fa-check mr-2';
        button.disabled = false;
        showNotification('Security token missing. Please refresh the page.', 'error');
        return;
    }

    fetch(`/subjects/api/mark-note-read/${noteId}/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // Update button to show completed state
            btnText.textContent = 'Read ✓';
            btnIcon.className = 'fas fa-check-circle mr-2';
            button.className = 'mark-read-btn bg-gray-500 text-white px-4 py-2 rounded-lg font-medium cursor-not-allowed flex items-center w-full sm:w-auto justify-center';
            button.disabled = true;

            // Show success message
            showNotification('Note marked as read!', 'success');
        } else {
            throw new Error(data.error || 'Failed to mark note as read');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // Reset button state
        btnText.textContent = 'Mark as Read';
        btnIcon.className = 'fas fa-check mr-2';
        button.disabled = false;

        showNotification('Failed to mark note as read. Please try again.', 'error');
    });
}

// Helper function to get cookie value
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Simple notification function
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white font-medium z-50 ${
        type === 'success' ? 'bg-green-600' : 'bg-red-600'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Add scroll animations on page load
document.addEventListener('DOMContentLoaded', function() {
    // Animate cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe action cards
    document.querySelectorAll('.group').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
        observer.observe(card);
    });
});
</script>

<style>
/* Modern glassmorphism and animation styles */
.backdrop-blur-sm {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

/* Enhanced hover effects for action cards */
.group:hover {
    transform: translateY(-8px) scale(1.02);
}

/* Smooth gradient animations */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.bg-gradient-to-r {
    background-size: 200% 200%;
    animation: gradientShift 6s ease infinite;
}

/* Study content styling with modern typography */
.study-content {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.study-content h1 {
    font-size: 2.25rem;
    font-weight: 800;
    color: #1e293b;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.study-content h2 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #334155;
    margin-top: 2.5rem;
    margin-bottom: 1.25rem;
    position: relative;
    padding-left: 1rem;
}

.study-content h2::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
    border-radius: 2px;
}

.study-content h3 {
    font-size: 1.375rem;
    font-weight: 600;
    color: #475569;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.study-content p {
    margin-bottom: 1.25rem;
    line-height: 1.8;
    color: #475569;
    font-size: 1.05rem;
}

.study-content ul, .study-content ol {
    margin-left: 1.75rem;
    margin-bottom: 1.5rem;
    color: #475569;
}

.study-content li {
    margin-bottom: 0.75rem;
    line-height: 1.7;
    position: relative;
}

.study-content ul li::marker {
    color: #3b82f6;
}

.study-content strong {
    font-weight: 700;
    color: #1e293b;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.study-content code {
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    color: #475569;
    padding: 0.375rem 0.75rem;
    border-radius: 0.5rem;
    font-family: 'JetBrains Mono', 'Fira Code', monospace;
    font-size: 0.9rem;
    border: 1px solid #cbd5e1;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.study-content blockquote {
    border-left: 4px solid #3b82f6;
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    padding: 1.5rem;
    margin: 1.5rem 0;
    border-radius: 0.75rem;
    font-style: italic;
    color: #475569;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Responsive design enhancements */
@media (max-width: 768px) {
    .study-content h1 {
        font-size: 1.875rem;
    }

    .study-content h2 {
        font-size: 1.5rem;
    }

    .study-content h3 {
        font-size: 1.25rem;
    }
}

/* Loading animation for smooth transitions */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced shadow effects */
.shadow-xl {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.hover\:shadow-2xl:hover {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}
</style>

{% endblock %}