# Git and version control
.git
.gitignore
.gitattributes

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.pytest_cache

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/

# Development files
.env.local
.env.development
.env.test
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Documentation
README.md
docs/
*.md

# Testing
.pytest_cache/
.coverage
htmlcov/

# Deployment
fly.toml
.fly/

# Temporary files
tmp/
temp/
*.tmp
