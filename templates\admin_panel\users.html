{% extends 'admin_panel/base.html' %}

{% block title %}Manage Users{% endblock %}
{% block page_title %}User Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">
        <i class="fas fa-users me-2"></i>
        User Management
    </h4>
    <div class="btn-group">
        <a href="{% url 'admin_panel:create_user' %}" class="btn btn-outline-primary">
            <i class="fas fa-plus me-2"></i>
            Add User
        </a>
        <a href="{% url 'admin_panel:download_template' %}?type=users" class="btn btn-outline-secondary">
            <i class="fas fa-file-csv me-2"></i>
            Export Users
        </a>
    </div>
</div>

<!-- Filter Section -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row">
            <div class="col-md-4">
                <label class="form-label">Search Users</label>
                <input type="text" name="search" class="form-control" placeholder="Search by name or email..." value="{{ request.GET.search }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">Status Filter</label>
                <select name="status" class="form-select">
                    <option value="">All Users</option>
                    <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>Active</option>
                    <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>Inactive</option>
                    <option value="staff" {% if request.GET.status == 'staff' %}selected{% endif %}>Staff</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Class Level</label>
                <select name="level" class="form-select">
                    <option value="">All Levels</option>
                    <option value="primary">Primary</option>
                    <option value="jhs">JHS</option>
                    <option value="shs">SHS</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-2"></i>
                    Filter
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">All Users</h5>
        <div class="d-flex gap-2">
            <button class="btn btn-sm btn-outline-warning" onclick="bulkEmail()">
                <i class="fas fa-envelope me-1"></i>
                Bulk Email
            </button>
            <button class="btn btn-sm btn-outline-danger" onclick="bulkDeactivate()">
                <i class="fas fa-ban me-1"></i>
                Bulk Deactivate
            </button>
        </div>
    </div>
    <div class="card-body">
        {% if users %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="30">
                                <input type="checkbox" class="form-check-input" id="selectAll">
                            </th>
                            <th>User</th>
                            <th>Email</th>
                            <th>Current Level</th>
                            <th>Progress</th>
                            <th>Quizzes</th>
                            <th>Status</th>
                            <th>Joined</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input user-checkbox" value="{{ user.id }}">
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                <span class="text-white fw-bold">{{ user.first_name.0 }}{{ user.last_name.0 }}</span>
                                            </div>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ user.full_name }}</h6>
                                            <small class="text-muted">{{ user.phone_number|default:"No phone" }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        {{ user.email }}
                                        {% if user.is_email_verified %}
                                            <i class="fas fa-check-circle text-success ms-1" title="Verified"></i>
                                        {% else %}
                                            <i class="fas fa-exclamation-circle text-warning ms-1" title="Not verified"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if user.current_class_level %}
                                        <span class="badge bg-info">{{ user.current_class_level.name }}</span>
                                    {% else %}
                                        <span class="text-muted">Not set</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ user.progress_count }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ user.quizzes_count }}</span>
                                </td>
                                <td>
                                    {% if user.is_staff %}
                                        <span class="badge bg-warning">Staff</span>
                                    {% elif user.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">{{ user.date_joined|date:"M d, Y" }}</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'admin_panel:edit_user' user.id %}"
                                           class="btn btn-sm btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-sm btn-outline-info disabled"
                                                title="View Progress (Coming Soon)">
                                            <i class="fas fa-chart-line"></i>
                                        </button>
                                        {% if not user.is_superuser %}
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-danger"
                                                    title="Delete"
                                                    onclick="confirmDelete('{{ user.full_name }}', '{% url 'admin_panel:delete_user' user.id %}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if users.has_other_pages %}
                <nav aria-label="Users pagination">
                    <ul class="pagination justify-content-center">
                        {% if users.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ users.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">Previous</a>
                            </li>
                        {% endif %}

                        {% for num in users.paginator.page_range %}
                            {% if users.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > users.number|add:'-3' and num < users.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if users.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ users.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">Next</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fs-1 text-muted mb-3"></i>
                <h5 class="text-muted">No users found</h5>
                <p class="text-muted">No users match your current filters.</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Summary Cards -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-primary">{{ users|length }}</h4>
                <p class="mb-0">Total Users</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-success">
                    {% for user in users %}
                        {% if user.is_active %}{{ forloop.counter0|add:1 }}{% endif %}
                    {% empty %}0{% endfor %}
                </h4>
                <p class="mb-0">Active Users</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-warning">
                    {% for user in users %}
                        {% if user.is_staff %}{{ forloop.counter0|add:1 }}{% endif %}
                    {% empty %}0{% endfor %}
                </h4>
                <p class="mb-0">Staff Users</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-info">
                    {% for user in users %}
                        {% if user.is_email_verified %}{{ forloop.counter0|add:1 }}{% endif %}
                    {% empty %}0{% endfor %}
                </h4>
                <p class="mb-0">Verified Emails</p>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the user "<span id="deleteItemName"></span>"?</p>
                <p class="text-danger"><strong>Warning:</strong> This will permanently delete all user data including progress and quiz results.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Delete User</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');

    selectAllCheckbox.addEventListener('change', function() {
        userCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
});

function confirmDelete(itemName, deleteUrl) {
    document.getElementById('deleteItemName').textContent = itemName;
    document.getElementById('deleteForm').action = deleteUrl;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function bulkEmail() {
    const selectedUsers = document.querySelectorAll('.user-checkbox:checked');
    if (selectedUsers.length === 0) {
        alert('Please select users to email.');
        return;
    }

    const subject = prompt('Enter email subject:');
    if (subject) {
        const message = prompt('Enter email message:');
        if (message) {
            alert(`Email functionality will send "${subject}" to ${selectedUsers.length} users.`);
            // Implement actual email functionality here
        }
    }
}

function bulkDeactivate() {
    const selectedUsers = document.querySelectorAll('.user-checkbox:checked');
    if (selectedUsers.length === 0) {
        alert('Please select users to deactivate.');
        return;
    }

    if (confirm(`Are you sure you want to deactivate ${selectedUsers.length} selected users?`)) {
        alert('Bulk deactivation functionality will be implemented.');
        // Implement actual bulk deactivation here
    }
}
</script>
{% endblock %}
