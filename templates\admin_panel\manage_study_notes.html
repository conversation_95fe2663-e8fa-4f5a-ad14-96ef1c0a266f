{% extends 'admin_panel/base.html' %}

{% block title %}Manage Study Notes{% endblock %}
{% block page_title %}Study Notes Management{% endblock %}

{% block extra_css %}
<style>
.reorder-mode {
    background-color: #f8f9fa !important;
    border: 2px dashed #dee2e6 !important;
}

.reorder-mode:hover {
    background-color: #e9ecef !important;
    border-color: #6c757d !important;
}

.drag-handle {
    opacity: 0.6;
    transition: opacity 0.2s;
}

.drag-handle:hover {
    opacity: 1;
}

.sortable-row.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.order-badge {
    min-width: 30px;
    display: inline-block;
}
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-book-open me-2"></i>
                    Study Notes Management
                </h5>
                <a href="{% url 'admin_panel:create_study_note' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Create Study Note
                </a>
            </div>
            <div class="card-body">
                <!-- Hidden CSRF Token for AJAX requests -->
                {% csrf_token %}

                <!-- Hierarchical Selection Form - NEW FLOW: Grade Level → Subject → Topic -->
                <form method="get" class="mb-4" id="filterForm">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="level_id" class="form-label">Select Grade Level</label>
                            <select class="form-select" id="level_id" name="level_id" onchange="handleLevelChange()">
                                <option value="">Choose Grade...</option>
                                {% for level in levels %}
                                    <option value="{{ level.level_number }}" {% if level.level_number|stringformat:"s" == selected_level_id %}selected{% endif %}>
                                        Grade {{ level.level_number }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label for="subject_id" class="form-label">Select Subject</label>
                            <select class="form-select" id="subject_id" name="subject_id" onchange="handleSubjectChange()" {% if not selected_level_id %}disabled{% endif %}>
                                <option value="">Choose Subject...</option>
                                {% if selected_level_id %}
                                    {% for subject in subjects %}
                                        <option value="{{ subject.id }}" {% if subject.id|stringformat:"s" == selected_subject_id %}selected{% endif %}>
                                            {{ subject.name }}
                                        </option>
                                    {% endfor %}
                                {% endif %}
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label for="topic_id" class="form-label">Select Topic</label>
                            <select class="form-select" id="topic_id" name="topic_id" onchange="handleTopicChange()" {% if not selected_subject_id %}disabled{% endif %}>
                                <option value="">Choose Topic...</option>
                                {% if selected_subject_id %}
                                    {% for topic in topics %}
                                        <option value="{{ topic.id }}" {% if topic.id|stringformat:"s" == selected_topic_id %}selected{% endif %}>
                                            {{ topic.title }}
                                        </option>
                                    {% endfor %}
                                {% endif %}
                            </select>
                        </div>

                        {% if selected_topic_id %}
                        <div class="col-md-3 d-flex align-items-end gap-2">
                            <a href="{% url 'admin_panel:create_study_note' %}?level_id={{ selected_level_id }}&subject_id={{ selected_subject_id }}&topic_id={{ selected_topic_id }}"
                               class="btn btn-success flex-fill">
                                <i class="fas fa-plus me-2"></i>
                                Add Note
                            </a>
                            {% if study_notes %}
                            <a href="{% url 'admin_panel:read_study_notes' selected_topic_id %}"
                               class="btn btn-primary flex-fill">
                                <i class="fas fa-book-reader me-2"></i>
                                Read Notes
                            </a>
                            {% if study_notes|length > 1 %}
                            <button type="button" class="btn btn-warning flex-fill" id="reorderBtn">
                                <i class="fas fa-sort me-2"></i>
                                Reorder
                            </button>
                            {% endif %}
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                </form>

                <!-- Study Notes List -->
                {% if selected_topic_id %}
                    {% if study_notes %}
                        <div class="table-responsive">
                            <table class="table table-hover" id="notesTable">
                                <thead class="table-light">
                                    <tr>
                                        <th width="50">Order</th>
                                        <th>Title</th>
                                        <th>Content Preview</th>
                                        <th>Created By</th>
                                        <th>Created Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="sortableNotes">
                                    {% for note in study_notes %}
                                    <tr data-note-id="{{ note.id }}" data-order="{{ note.order }}" class="sortable-row">
                                        <td class="text-center">
                                            <span class="badge bg-secondary order-badge">{{ note.order|default:forloop.counter }}</span>
                                            <i class="fas fa-grip-vertical text-muted ms-2 drag-handle" style="cursor: move; display: none;"></i>
                                        </td>
                                        <td>
                                            <strong>{{ note.title }}</strong>
                                        </td>
                                        <td>
                                            <div class="text-muted" style="max-width: 300px;">
                                                {{ note.content|truncatewords:15|striptags }}
                                            </div>
                                        </td>
                                        <td>
                                            {% if note.created_by %}
                                                {{ note.created_by.first_name }} {{ note.created_by.last_name }}
                                            {% else %}
                                                System
                                            {% endif %}
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                {{ note.created_at|date:"M d, Y" }}
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary" onclick="viewNote('{{ note.id }}')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <a href="{% url 'admin_panel:edit_study_note' note.id %}" class="btn btn-outline-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-outline-danger" onclick="deleteNote('{{ note.id }}')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if is_paginated %}
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <div class="text-muted">
                                Showing {{ study_notes.start_index }}-{{ study_notes.end_index }} of {{ study_notes.paginator.count }} study notes
                            </div>
                            <nav aria-label="Study notes pagination">
                                <ul class="pagination pagination-sm mb-0">
                                    {% if study_notes.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?level_id={{ selected_level_id }}&subject_id={{ selected_subject_id }}&topic_id={{ selected_topic_id }}&page=1">
                                                <i class="fas fa-angle-double-left"></i>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?level_id={{ selected_level_id }}&subject_id={{ selected_subject_id }}&topic_id={{ selected_topic_id }}&page={{ study_notes.previous_page_number }}">
                                                <i class="fas fa-angle-left"></i>
                                            </a>
                                        </li>
                                    {% endif %}

                                    {% for num in study_notes.paginator.page_range %}
                                        {% if study_notes.number == num %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ num }}</span>
                                            </li>
                                        {% elif num > study_notes.number|add:'-3' and num < study_notes.number|add:'3' %}
                                            <li class="page-item">
                                                <a class="page-link" href="?level_id={{ selected_level_id }}&subject_id={{ selected_subject_id }}&topic_id={{ selected_topic_id }}&page={{ num }}">{{ num }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}

                                    {% if study_notes.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?level_id={{ selected_level_id }}&subject_id={{ selected_subject_id }}&topic_id={{ selected_topic_id }}&page={{ study_notes.next_page_number }}">
                                                <i class="fas fa-angle-right"></i>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?level_id={{ selected_level_id }}&subject_id={{ selected_subject_id }}&topic_id={{ selected_topic_id }}&page={{ study_notes.paginator.num_pages }}">
                                                <i class="fas fa-angle-double-right"></i>
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-book-open text-muted" style="font-size: 3rem;"></i>
                            <h4 class="mt-3 text-muted">No Study Notes Found</h4>
                            <p class="text-muted">This topic doesn't have any study notes yet.</p>
                            <a href="{% url 'admin_panel:create_study_note' %}?level_id={{ selected_level_id }}&subject_id={{ selected_subject_id }}&topic_id={{ selected_topic_id }}"
                               class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                Create First Study Note
                            </a>
                        </div>
                    {% endif %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-filter text-muted" style="font-size: 3rem;"></i>
                        <h4 class="mt-3 text-muted">Select Grade, Subject & Topic</h4>
                        <p class="text-muted">Choose a grade level, subject, and topic to view and manage study notes.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- View Note Modal -->
<div class="modal fade" id="viewNoteModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Study Note Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="noteContent">
                <!-- Note content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Confirm Delete
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">Are you sure you want to delete this study note?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Warning:</strong> This action cannot be undone. The study note will be permanently removed.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>
                    Cancel
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-2"></i>
                    Delete Study Note
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Get CSRF token from Django
function getCSRFToken() {
    const csrfTokenElement = document.querySelector('[name=csrfmiddlewaretoken]');
    if (csrfTokenElement) {
        return csrfTokenElement.value;
    }

    // Fallback: try to get from cookie
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrftoken') {
            return value;
        }
    }

    console.error('CSRF token not found');
    return null;
}
</script>
<script>
function handleLevelChange() {
    const levelId = document.getElementById('level_id').value;

    // Clear and disable dependent dropdowns
    const subjectSelect = document.getElementById('subject_id');
    const topicSelect = document.getElementById('topic_id');

    subjectSelect.innerHTML = '<option value="">Choose Subject...</option>';
    topicSelect.innerHTML = '<option value="">Choose Topic...</option>';

    if (levelId) {
        // Enable subject dropdown and redirect with only level_id
        subjectSelect.disabled = false;
        topicSelect.disabled = true;

        // Redirect with only level_id parameter
        window.location.href = `?level_id=${levelId}`;
    } else {
        // Disable both dropdowns if no level selected
        subjectSelect.disabled = true;
        topicSelect.disabled = true;

        // Redirect to clean page
        window.location.href = window.location.pathname;
    }
}

function handleSubjectChange() {
    const levelId = document.getElementById('level_id').value;
    const subjectId = document.getElementById('subject_id').value;

    // Clear topic dropdown
    const topicSelect = document.getElementById('topic_id');
    topicSelect.innerHTML = '<option value="">Choose Topic...</option>';

    if (subjectId && levelId) {
        // Enable topic dropdown and redirect with level_id and subject_id
        topicSelect.disabled = false;

        // Redirect with level_id and subject_id parameters
        window.location.href = `?level_id=${levelId}&subject_id=${subjectId}`;
    } else {
        // Disable topic dropdown if no subject selected
        topicSelect.disabled = true;

        // Redirect with only level_id if subject is cleared
        if (levelId) {
            window.location.href = `?level_id=${levelId}`;
        }
    }
}

function handleTopicChange() {
    const levelId = document.getElementById('level_id').value;
    const subjectId = document.getElementById('subject_id').value;
    const topicId = document.getElementById('topic_id').value;

    if (topicId && subjectId && levelId) {
        // Redirect with all parameters
        window.location.href = `?level_id=${levelId}&subject_id=${subjectId}&topic_id=${topicId}`;
    } else if (subjectId && levelId) {
        // Redirect with level_id and subject_id if topic is cleared
        window.location.href = `?level_id=${levelId}&subject_id=${subjectId}`;
    }
}

function viewNote(noteId) {
    // Show modal with loading message
    document.getElementById('noteContent').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading note details...</div>';
    new bootstrap.Modal(document.getElementById('viewNoteModal')).show();

    // Fetch note details via AJAX
    fetch(`{% url 'admin_panel:view_study_note' '00000000-0000-0000-0000-000000000000' %}`.replace('00000000-0000-0000-0000-000000000000', noteId))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('noteContent').innerHTML = `
                    <div class="mb-3">
                        <h4>${data.title}</h4>
                        <div class="text-muted small mb-3">
                            <i class="fas fa-book me-1"></i> ${data.subject} - ${data.grade} - ${data.topic}<br>
                            <i class="fas fa-user me-1"></i> Created by ${data.created_by} on ${data.created_at}<br>
                            <i class="fas fa-clock me-1"></i> Last updated: ${data.updated_at}
                        </div>
                    </div>
                    <hr>
                    <div class="note-content">
                        ${data.content}
                    </div>
                `;
            } else {
                document.getElementById('noteContent').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Error loading note: ${data.error}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('noteContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error loading note details. Please try again.
                </div>
            `;
        });
}

function deleteNote(noteId) {
    // Show custom confirmation modal
    const modal = document.getElementById('deleteConfirmModal');
    const confirmBtn = document.getElementById('confirmDeleteBtn');

    // Set up the confirm button
    confirmBtn.onclick = function() {
        // Get CSRF token
        const csrfToken = getCSRFToken();
        if (!csrfToken) {
            alert('Error: CSRF token not found. Please refresh the page and try again.');
            // Close modal
            bootstrap.Modal.getInstance(modal).hide();
            return;
        }

        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `{% url 'admin_panel:delete_study_note' '00000000-0000-0000-0000-000000000000' %}`.replace('00000000-0000-0000-0000-000000000000', noteId);

        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        // Show loading state
        confirmBtn.disabled = true;
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Deleting...';

        // Add to body and submit
        document.body.appendChild(form);
        form.submit();
    };

    // Show the modal
    new bootstrap.Modal(modal).show();
}

// Note reordering functionality
let isReorderMode = false;
let sortable = null;

document.addEventListener('DOMContentLoaded', function() {
    const reorderBtn = document.getElementById('reorderBtn');
    const notesTable = document.getElementById('sortableNotes');

    if (reorderBtn) {
        reorderBtn.addEventListener('click', toggleReorderMode);
    }
});

function toggleReorderMode() {
    const reorderBtn = document.getElementById('reorderBtn');
    const dragHandles = document.querySelectorAll('.drag-handle');
    const sortableRows = document.querySelectorAll('.sortable-row');

    isReorderMode = !isReorderMode;

    if (isReorderMode) {
        // Enter reorder mode
        reorderBtn.innerHTML = '<i class="fas fa-save me-2"></i>Save Order';
        reorderBtn.className = 'btn btn-success flex-fill';

        // Show drag handles
        dragHandles.forEach(handle => handle.style.display = 'inline');

        // Add visual feedback
        sortableRows.forEach(row => {
            row.style.cursor = 'move';
            row.classList.add('reorder-mode');
        });

        // Initialize sortable
        initializeSortable();

        // Show instructions
        showReorderInstructions();

    } else {
        // Exit reorder mode and save
        saveNoteOrder();
    }
}

function initializeSortable() {
    const tbody = document.getElementById('sortableNotes');

    // Simple drag and drop implementation
    let draggedElement = null;

    tbody.addEventListener('dragstart', function(e) {
        if (e.target.closest('.sortable-row')) {
            draggedElement = e.target.closest('.sortable-row');
            draggedElement.style.opacity = '0.5';
        }
    });

    tbody.addEventListener('dragend', function(e) {
        if (draggedElement) {
            draggedElement.style.opacity = '';
            draggedElement = null;
        }
    });

    tbody.addEventListener('dragover', function(e) {
        e.preventDefault();
    });

    tbody.addEventListener('drop', function(e) {
        e.preventDefault();
        const targetRow = e.target.closest('.sortable-row');

        if (draggedElement && targetRow && draggedElement !== targetRow) {
            const tbody = targetRow.parentNode;
            const targetIndex = Array.from(tbody.children).indexOf(targetRow);
            const draggedIndex = Array.from(tbody.children).indexOf(draggedElement);

            if (draggedIndex < targetIndex) {
                tbody.insertBefore(draggedElement, targetRow.nextSibling);
            } else {
                tbody.insertBefore(draggedElement, targetRow);
            }

            updateOrderBadges();
        }
    });

    // Make rows draggable
    const rows = tbody.querySelectorAll('.sortable-row');
    rows.forEach(row => {
        row.draggable = true;
    });
}

function updateOrderBadges() {
    const rows = document.querySelectorAll('#sortableNotes .sortable-row');
    rows.forEach((row, index) => {
        const badge = row.querySelector('.order-badge');
        badge.textContent = index + 1;
    });
}

function showReorderInstructions() {
    const alertHtml = `
        <div class="alert alert-info alert-dismissible fade show mt-3" role="alert" id="reorderAlert">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Reorder Mode Active:</strong> Drag and drop rows to reorder study notes. Click "Save Order" when finished.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const tableContainer = document.querySelector('.table-responsive');
    tableContainer.insertAdjacentHTML('beforebegin', alertHtml);
}

function saveNoteOrder() {
    const rows = document.querySelectorAll('#sortableNotes .sortable-row');
    const noteOrders = [];

    rows.forEach((row, index) => {
        noteOrders.push({
            id: row.dataset.noteId,
            order: index + 1
        });
    });

    // Get CSRF token
    const csrfToken = getCSRFToken();
    if (!csrfToken) {
        alert('Error: CSRF token not found. Please refresh the page and try again.');
        return;
    }

    // Send AJAX request to save order
    fetch('{% url "admin_panel:reorder_study_notes" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            note_orders: noteOrders
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Exit reorder mode
            exitReorderMode();

            // Show success message
            showSuccessMessage('Study notes reordered successfully!');

            // Reload page to reflect new order
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            alert('Error saving order: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving note order. Please try again.');
    });
}

function exitReorderMode() {
    const reorderBtn = document.getElementById('reorderBtn');
    const dragHandles = document.querySelectorAll('.drag-handle');
    const sortableRows = document.querySelectorAll('.sortable-row');
    const reorderAlert = document.getElementById('reorderAlert');

    isReorderMode = false;

    // Reset button
    reorderBtn.innerHTML = '<i class="fas fa-sort me-2"></i>Reorder';
    reorderBtn.className = 'btn btn-warning flex-fill';

    // Hide drag handles
    dragHandles.forEach(handle => handle.style.display = 'none');

    // Remove visual feedback
    sortableRows.forEach(row => {
        row.style.cursor = '';
        row.classList.remove('reorder-mode');
        row.draggable = false;
    });

    // Remove alert
    if (reorderAlert) {
        reorderAlert.remove();
    }
}

function showSuccessMessage(message) {
    const alertHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.card-body');
    container.insertAdjacentHTML('afterbegin', alertHtml);
}
</script>
{% endblock %}
