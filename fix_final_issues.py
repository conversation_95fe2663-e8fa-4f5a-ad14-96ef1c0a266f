import csv

# Read all rows
rows = []
with open('Grade_9_questions_UPLOAD.csv', 'r', encoding='utf-8') as f:
    reader = csv.reader(f)
    for row in reader:
        rows.append(row)

# Fix the problematic rows
fixes = {
    229: 'a',  # Row 230 (0-based index 229)
    230: 'true',  # Row 231 (0-based index 230) 
    280: 'a',  # Row 281 (0-based index 280)
    500: 'a'   # Row 501 (0-based index 500)
}

for row_index, new_answer in fixes.items():
    if row_index < len(rows) and len(rows[row_index]) >= 6:
        rows[row_index][5] = new_answer
        print(f'Fixed row {row_index + 1}: set answer to "{new_answer}"')

# Write the final file
with open('Grade_9_questions_PERFECT.csv', 'w', encoding='utf-8', newline='') as f:
    writer = csv.writer(f)
    for row in rows:
        writer.writerow(row)

print('Saved final file as: Grade_9_questions_PERFECT.csv')
