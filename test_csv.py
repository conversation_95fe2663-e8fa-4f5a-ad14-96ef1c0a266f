#!/usr/bin/env python3
import csv

# Test the first few rows
with open('Grade_9_questions.csv', 'r', encoding='utf-8') as f:
    reader = csv.reader(f)
    for i, row in enumerate(reader):
        if i < 5:  # First 5 rows
            print(f"Row {i+1}: {len(row)} fields")
            for j, field in enumerate(row[:15]):  # First 15 fields
                print(f"  Field {j+1}: '{field}'")
            print()
        else:
            break
