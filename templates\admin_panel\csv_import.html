{% extends 'admin_panel/base.html' %}

{% block title %}Questions Import{% endblock %}
{% block page_title %}Questions Import{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <!-- Import Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-upload me-2"></i>
                    Import Questions from CSV
                </h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data" id="importForm">
                    {% csrf_token %}
                    <input type="hidden" name="import_mode" id="importMode" value="strict">

                    <div class="mb-3">
                        <label for="{{ form.csv_file.id_for_label }}" class="form-label">
                            Questions CSV File <span class="text-danger">*</span>
                        </label>
                        {{ form.csv_file }}
                        <div class="form-text">
                            Upload a CSV file with questions data (max 5MB). Download the template first to see the required format.
                        </div>
                        {% if form.csv_file.errors %}
                            <div class="text-danger small">{{ form.csv_file.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <!-- Progress Bar (hidden initially) -->
                    <div id="uploadProgress" class="mb-3" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="small text-muted">Uploading and processing...</span>
                            <span id="progressText" class="small text-muted">0%</span>
                        </div>
                        <div class="progress">
                            <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                                 role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                            </div>
                        </div>
                        <div id="statusText" class="small text-muted mt-2">Preparing upload...</div>
                    </div>

                    <!-- Live Preview Section (hidden initially) -->
                    <div id="livePreview" class="mt-4" style="display: none;">
                        <div class="card border-info">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-eye me-2 text-info"></i>
                                    Question Preview
                                    <span id="previewStats" class="badge bg-info ms-2"></span>
                                </h6>
                            </div>
                            <div class="card-body" style="max-height: 500px; overflow-y: auto;">
                                <div id="previewContent">
                                    <!-- Preview content will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Class Level Selection (hidden initially) -->
                    <div id="classLevelSelection" class="mb-3" style="display: none;">
                        <label class="form-label">
                            <strong>Target Class Levels</strong> <span class="text-danger">*</span>
                        </label>
                        <div class="form-text mb-2">
                            Select which grade levels these questions should be imported for. Missing topics will be auto-created.
                        </div>
                        <div class="row">
                            {% for value, label in form.target_class_levels.field.choices %}
                                <div class="col-md-3 col-sm-4 col-6 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="target_class_levels" value="{{ value }}" id="class_{{ value }}">
                                        <label class="form-check-label" for="class_{{ value }}">
                                            {{ label }}
                                        </label>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <a href="{% url 'admin_panel:download_template' %}?type=questions" class="btn btn-outline-info me-2">
                                <i class="fas fa-download me-2"></i>
                                Download Template
                            </a>
                            <a href="{% url 'admin_panel:import_logs' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-list-alt me-2"></i>
                                View Import Logs
                            </a>
                        </div>
                        <div>
                            <button type="submit" id="submitBtn" class="btn btn-primary" style="display: none;">
                                <i class="fas fa-upload me-2"></i>
                                <span id="submitText">Import Questions</span>
                            </button>
                            <button type="submit" id="partialSubmitBtn" class="btn btn-warning" style="display: none;">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <span id="partialSubmitText">Import Valid Questions Only</span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Recent Imports -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    Recent Imports
                </h5>
            </div>
            <div class="card-body">
                {% if recent_imports %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>File Name</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Total Rows</th>
                                    <th>Success</th>
                                    <th>Failed</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for import_log in recent_imports %}
                                    <tr>
                                        <td>{{ import_log.file_name }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ import_log.import_type|title }}</span>
                                        </td>
                                        <td>
                                            {% if import_log.status == 'completed' %}
                                                <span class="badge bg-success">Completed</span>
                                            {% elif import_log.status == 'failed' %}
                                                <span class="badge bg-danger">Failed</span>
                                            {% elif import_log.status == 'processing' %}
                                                <span class="badge bg-warning">Processing</span>
                                            {% else %}
                                                <span class="badge bg-secondary">Pending</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ import_log.total_rows }}</td>
                                        <td>
                                            <span class="text-success">{{ import_log.successful_imports }}</span>
                                        </td>
                                        <td>
                                            <span class="text-danger">{{ import_log.failed_imports }}</span>
                                        </td>
                                        <td>{{ import_log.started_at|date:"M d, Y H:i" }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-file-csv fs-1 text-muted mb-3"></i>
                        <p class="text-muted">No imports yet. Upload your first CSV file to get started.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Import Guidelines -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Import Guidelines
                </h5>
            </div>
            <div class="card-body">
                <h6>Required Fields:</h6>
                <ul class="small">
                    <li><strong>subject_name:</strong> Subject (must exist)</li>
                    <li><strong>class_level_name:</strong> Class level (must exist)</li>
                    <li><strong>topic_title:</strong> Topic (must exist)</li>
                    <li><strong>question_text:</strong> The question</li>
                    <li><strong>question_type:</strong> multiple_choice, fill_blank, true_false, short_answer</li>
                    <li><strong>correct_answer:</strong> Correct answer</li>
                </ul>

                <h6 class="mt-3">Optional Fields:</h6>
                <ul class="small">
                    <li><strong>explanation:</strong> Answer explanation</li>
                    <li><strong>difficulty:</strong> easy, medium, hard</li>
                    <li><strong>points:</strong> Points (default: 1)</li>
                    <li><strong>time_limit:</strong> Seconds (default: 45)</li>
                    <li><strong>choice_a to choice_d:</strong> Multiple choice options</li>
                </ul>

                <div class="alert alert-info small mt-3">
                    <i class="fas fa-lightbulb me-2"></i>
                    <strong>Tip:</strong> Download the template with samples to see the exact format and examples.
                </div>
            </div>
        </div>

        <!-- Export Options -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-download me-2"></i>
                    Export Data
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted small">Export your data for backup or migration purposes.</p>

                <div class="d-grid gap-2">
                    <a href="{% url 'admin_panel:download_template' %}?type=questions" class="btn btn-outline-primary">
                        <i class="fas fa-file-csv me-2"></i>
                        Questions Template
                    </a>
                    <a href="{% url 'admin_panel:export_questions' %}" class="btn btn-outline-success">
                        <i class="fas fa-database me-2"></i>
                        Export All Questions
                    </a>
                    <a href="{% url 'admin_panel:export_users' %}" class="btn btn-outline-info">
                        <i class="fas fa-users me-2"></i>
                        Export Users
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Import Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ recent_imports|length }}</h4>
                        <small class="text-muted">Total Imports</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">
                            {% for import_log in recent_imports %}
                                {% if import_log.status == 'completed' %}{{ forloop.counter0|add:1 }}{% endif %}
                            {% empty %}0{% endfor %}
                        </h4>
                        <small class="text-muted">Successful</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // File input validation
    const fileInput = document.getElementById('{{ form.csv_file.id_for_label }}');
    const importForm = document.getElementById('importForm');
    const submitBtn = document.getElementById('submitBtn');
    const submitText = document.getElementById('submitText');
    const partialSubmitBtn = document.getElementById('partialSubmitBtn');
    const partialSubmitText = document.getElementById('partialSubmitText');
    const uploadProgress = document.getElementById('uploadProgress');
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const statusText = document.getElementById('statusText');
    const livePreview = document.getElementById('livePreview');
    const previewContent = document.getElementById('previewContent');
    const previewStats = document.getElementById('previewStats');
    const classLevelSelection = document.getElementById('classLevelSelection');

    fileInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            if (!file.name.toLowerCase().endsWith('.csv')) {
                alert('Please select a CSV file.');
                this.value = '';
                livePreview.style.display = 'none';
                return;
            }

            if (file.size > 5 * 1024 * 1024) { // 5MB limit
                alert('File size should not exceed 5MB.');
                this.value = '';
                livePreview.style.display = 'none';
                return;
            }

            // Show file info and automatically generate preview
            statusText.textContent = `Selected: ${file.name} (${(file.size / 1024).toFixed(1)} KB)`;
            generateLivePreview(file);
        } else {
            livePreview.style.display = 'none';
            submitBtn.style.display = 'none';
            partialSubmitBtn.style.display = 'none';
            classLevelSelection.style.display = 'none';
        }
    });

    // Function to generate live preview automatically
    function generateLivePreview(file) {
        // Show loading state
        livePreview.style.display = 'block';
        previewContent.innerHTML = '<div class="text-center py-3"><i class="fas fa-spinner fa-spin me-2"></i>Loading preview...</div>';
        previewStats.textContent = 'Loading...';

        // Create FormData and send preview request
        const formData = new FormData();
        formData.append('csv_file', file);
        formData.append('action', 'preview');
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            console.log('Preview response:', data); // Debug log

            if (data.success) {
                // Hide upload progress since preview is ready
                uploadProgress.style.display = 'none';

                displayLivePreview(data.preview_data);

                // Show encoding info if available
                if (data.preview_data.encoding_info) {
                    console.log('File encoding:', data.preview_data.encoding_info.detected_encoding);
                    if (data.preview_data.encoding_info.detected_encoding !== 'utf-8') {
                        const encodingInfo = document.createElement('div');
                        encodingInfo.className = 'alert alert-info mt-2';
                        encodingInfo.innerHTML = `
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Encoding Info:</strong> File was read using ${data.preview_data.encoding_info.detected_encoding} encoding.
                            For best compatibility, save your CSV files as UTF-8 encoding.
                        `;
                        livePreview.appendChild(encodingInfo);
                    }
                }

                // Determine which buttons to show based on validation results
                const summary = data.preview_data.summary || {};
                const hasErrors = (summary.rows_with_errors || 0) > 0;
                const hasValidRows = (summary.valid_rows || summary.total_rows || 0) > 0;
                const totalRows = summary.total_rows || 0;

                console.log('Preview analysis:', { hasErrors, hasValidRows, totalRows, summary }); // Debug log

                // Always show class level selection if we have any rows
                if (totalRows > 0) {
                    classLevelSelection.style.display = 'block';

                    if (!hasErrors) {
                        // No errors - show normal import button
                        console.log('Showing normal import button');
                        submitBtn.style.display = 'inline-block';
                        partialSubmitBtn.style.display = 'none';
                    } else if (hasValidRows) {
                        // Has errors but also has valid rows - show partial import option
                        console.log('Showing partial import button');
                        submitBtn.style.display = 'none';
                        partialSubmitBtn.style.display = 'inline-block';

                        // Update button text to show counts
                        const validCount = summary.valid_rows || (totalRows - (summary.rows_with_errors || 0));
                        const errorCount = summary.rows_with_errors || 0;
                        partialSubmitText.textContent = `Import ${validCount} Valid Questions (Skip ${errorCount} with errors)`;
                    } else {
                        // All rows have errors - show neither button and hide class selection
                        console.log('All rows have errors - hiding everything');
                        classLevelSelection.style.display = 'none';
                        submitBtn.style.display = 'none';
                        partialSubmitBtn.style.display = 'none';

                        // Show error message
                        const errorAlert = document.createElement('div');
                        errorAlert.className = 'alert alert-danger mt-3';
                        errorAlert.innerHTML = `
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Cannot Import:</strong> All rows in your CSV file have validation errors.
                            Please fix the errors and try again. Common issues include invalid question types,
                            missing required fields, or incorrect data formats.
                        `;

                        // Remove any existing error alerts
                        const existingAlert = document.querySelector('.alert-danger.mt-3');
                        if (existingAlert) {
                            existingAlert.remove();
                        }

                        // Add the error alert after the preview
                        livePreview.appendChild(errorAlert);
                    }
                } else {
                    // No rows at all
                    console.log('No rows found - hiding everything');
                    classLevelSelection.style.display = 'none';
                    submitBtn.style.display = 'none';
                    partialSubmitBtn.style.display = 'none';
                }
            } else {
                let errorHtml = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Preview Failed:</strong> ${data.error}
                    </div>
                `;

                // Add specific help for encoding errors
                if (data.error.includes('encoding') || data.error.includes('UTF-8')) {
                    errorHtml += `
                        <div class="alert alert-info">
                            <i class="fas fa-lightbulb me-2"></i>
                            <strong>How to Fix Encoding Issues:</strong>
                            <ol class="mb-0 mt-2">
                                <li><strong>Excel Users:</strong> Use "Save As" → "CSV UTF-8 (Comma delimited)"</li>
                                <li><strong>Google Sheets:</strong> File → Download → "Comma Separated Values (.csv)"</li>
                                <li><strong>Text Editors:</strong> Save with UTF-8 encoding</li>
                                <li><strong>Special Characters:</strong> Remove or replace accented characters (é, ñ, etc.)</li>
                                <li><strong>Alternative:</strong> Copy data to a new spreadsheet and save again</li>
                            </ol>
                        </div>
                    `;
                }

                previewContent.innerHTML = errorHtml;
                previewStats.textContent = 'Error';
                classLevelSelection.style.display = 'none';
                submitBtn.style.display = 'none';
                partialSubmitBtn.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            previewContent.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Error:</strong> Failed to generate preview. Please check your CSV file format.
                </div>
            `;
            previewStats.textContent = 'Error';
            classLevelSelection.style.display = 'none';
            submitBtn.style.display = 'none';
            partialSubmitBtn.style.display = 'none';
        });
    }



    // Handle partial import button click
    partialSubmitBtn.addEventListener('click', function(e) {
        e.preventDefault();
        document.getElementById('importMode').value = 'partial';
        startImportWithProgress('partial');
    });

    // Handle normal import button click
    submitBtn.addEventListener('click', function(e) {
        e.preventDefault();
        document.getElementById('importMode').value = 'strict';
        startImportWithProgress('strict');
    });

    // Function to start import with progress bar
    function startImportWithProgress(mode) {
        // Validate class level selection
        const selectedClassLevels = document.querySelectorAll('input[name="target_class_levels"]:checked');
        if (selectedClassLevels.length === 0) {
            alert('Please select at least one class level for import.');
            return;
        }

        // Show progress bar
        uploadProgress.style.display = 'block';
        submitBtn.disabled = true;
        partialSubmitBtn.disabled = true;

        // Update button text based on mode
        if (mode === 'partial') {
            partialSubmitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
        } else {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
        }

        // Simulate progress (since we can't track actual server processing)
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += Math.random() * 10;
            if (progress > 85) {
                progress = 85;
                clearInterval(progressInterval);
                statusText.textContent = 'Finalizing import...';

                // Complete progress after a short delay
                setTimeout(() => {
                    progressBar.style.width = '100%';
                    progressBar.setAttribute('aria-valuenow', 100);
                    progressText.textContent = '100%';
                    statusText.textContent = 'Import completed!';
                }, 1000);
            }

            progressBar.style.width = progress + '%';
            progressBar.setAttribute('aria-valuenow', progress);
            progressText.textContent = Math.round(progress) + '%';

            if (progress < 20) {
                statusText.textContent = 'Uploading file...';
            } else if (progress < 40) {
                statusText.textContent = 'Validating data...';
            } else if (progress < 60) {
                statusText.textContent = 'Creating hierarchy (subjects, topics)...';
            } else if (progress < 85) {
                statusText.textContent = mode === 'partial' ? 'Bulk creating valid questions...' : 'Bulk creating questions...';
            }
        }, 300);

        // Store interval for cleanup
        window.importProgressInterval = progressInterval;

        // Submit the form
        importForm.submit();
    }

    // Handle form submission with progress
    importForm.addEventListener('submit', function(e) {
        const file = fileInput.files[0];
        if (!file) {
            alert('Please select a CSV file first.');
            e.preventDefault();
            return;
        }

        // Check if at least one class level is selected
        const selectedClassLevels = document.querySelectorAll('input[name="target_class_levels"]:checked');
        if (selectedClassLevels.length === 0) {
            alert('Please select at least one class level for import.');
            e.preventDefault();
            return;
        }

        // Add action field to form for import if not already present
        if (!document.querySelector('input[name="action"]')) {
            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'import';
            importForm.appendChild(actionInput);
        }
    });

    // Function to display live preview with simple document format
    function displayLivePreview(previewData) {
        // Update stats badge
        const totalQuestions = previewData.summary.total_rows;
        const errorCount = previewData.summary.rows_with_errors;
        const warningCount = previewData.summary.rows_with_warnings;

        const validRows = previewData.summary.valid_rows || (totalQuestions - errorCount);

        if (errorCount > 0) {
            if (validRows === 0) {
                previewStats.textContent = `${totalQuestions} questions, ALL HAVE ERRORS - Cannot Import`;
                previewStats.className = 'badge bg-danger ms-2';
            } else {
                previewStats.textContent = `${totalQuestions} questions, ${errorCount} errors, ${validRows} valid`;
                previewStats.className = 'badge bg-warning ms-2';
            }
        } else if (warningCount > 0) {
            previewStats.textContent = `${totalQuestions} questions, ${warningCount} warnings`;
            previewStats.className = 'badge bg-warning ms-2';
        } else {
            previewStats.textContent = `${totalQuestions} questions, ready to import`;
            previewStats.className = 'badge bg-success ms-2';
        }

        let html = '';

        // Show validation issues if any
        if (previewData.validation_results.length > 0) {
            html += `
                <div class="alert alert-${errorCount > 0 ? 'danger' : 'warning'} mb-3">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Issues Found</h6>
                    <div class="small">
                        ${previewData.validation_results.slice(0, 5).map(result => `
                            <div class="mb-1">
                                <strong>Row ${result.row_number}:</strong>
                                ${result.issues.concat(result.warnings).join(', ')}
                            </div>
                        `).join('')}
                        ${previewData.validation_results.length > 5 ? `<div class="text-muted">... and ${previewData.validation_results.length - 5} more issues</div>` : ''}
                    </div>
                </div>
            `;
        }

        // Show all questions in simple document format
        if (previewData.sample_rows.length > 0) {
            html += `<div style="font-family: 'Times New Roman', serif; line-height: 1.6; color: #333;">`;

            previewData.sample_rows.forEach((row, index) => {
                const questionType = row.question_type || 'unknown';
                const questionText = row.question_text || 'No question text';
                const subject = row.subject_name || 'Unknown Subject';
                const classLevel = row.class_level_name || 'Unknown Class';
                const topic = row.topic_title || 'Unknown Topic';
                const difficulty = row.difficulty || 'medium';
                const explanation = row.explanation || '';

                html += `
                    <div class="mb-4 pb-3" style="border-bottom: 1px solid #eee;">
                        <div class="mb-2">
                            <small class="text-muted">
                                <strong>Subject:</strong> ${subject} |
                                <strong>Class:</strong> ${classLevel} |
                                <strong>Topic:</strong> ${topic} |
                                <strong>Difficulty:</strong> ${difficulty}
                            </small>
                        </div>

                        <div class="mb-3">
                            <strong>${index + 1}. ${questionText}</strong>
                        </div>

                        ${formatQuestionSimple(row, questionType)}

                        ${explanation ? `
                            <div class="mt-2">
                                <small class="text-info">
                                    <strong>Explanation:</strong> ${explanation}
                                </small>
                            </div>
                        ` : ''}
                    </div>
                `;
            });

            html += `</div>`;

            // Show total count if there are more questions
            if (totalQuestions > previewData.sample_rows.length) {
                html += `
                    <div class="text-center mt-3 p-3 bg-light rounded">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-2"></i>
                            Showing first ${previewData.sample_rows.length} questions.
                            Total questions to import: <strong>${totalQuestions}</strong>
                        </small>
                    </div>
                `;
            }
        }

        previewContent.innerHTML = html;
    }

    // Function to format questions in simple text format
    function formatQuestionSimple(row, questionType) {
        switch (questionType) {
            case 'multiple_choice':
                const choices = [
                    { letter: 'A', text: row.choice_a || '' },
                    { letter: 'B', text: row.choice_b || '' },
                    { letter: 'C', text: row.choice_c || '' },
                    { letter: 'D', text: row.choice_d || '' }
                ].filter(choice => choice.text.trim());

                const correctAnswer = (row.correct_answer || '').toUpperCase();

                let choicesHtml = choices.map(choice => {
                    const isCorrect = choice.letter === correctAnswer;
                    return `<div class="mb-1">${choice.letter}. ${choice.text} ${isCorrect ? '<span class="text-success fw-bold">(Correct Answer)</span>' : ''}</div>`;
                }).join('');

                return `<div class="ms-3">${choicesHtml}</div>`;

            case 'true_false':
                const correctAnswer_tf = (row.correct_answer || '').toLowerCase();
                const isTrue = ['true', 't', '1', 'yes'].includes(correctAnswer_tf);

                return `
                    <div class="ms-3">
                        <div class="mb-1">A. True ${isTrue ? '<span class="text-success fw-bold">(Correct Answer)</span>' : ''}</div>
                        <div class="mb-1">B. False ${!isTrue ? '<span class="text-success fw-bold">(Correct Answer)</span>' : ''}</div>
                    </div>
                `;

            case 'fill_blank':
            case 'short_answer':
                return `
                    <div class="ms-3">
                        <div class="mb-2">
                            <em>Answer: ________________________</em>
                        </div>
                        <div>
                            <small class="text-success">
                                <strong>Correct Answer:</strong> ${row.correct_answer || 'Not specified'}
                            </small>
                        </div>
                    </div>
                `;

            default:
                return `
                    <div class="ms-3">
                        <small class="text-warning">
                            <em>Unknown question type: ${questionType}</em>
                        </small>
                    </div>
                `;
        }
    }

    // Function to reset progress bar and buttons
    function resetImportState() {
        uploadProgress.style.display = 'none';
        submitBtn.disabled = false;
        partialSubmitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-upload me-2"></i><span id="submitText">Import Questions</span>';
        partialSubmitBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i><span id="partialSubmitText">Import Valid Questions Only</span>';

        // Clear any progress intervals
        if (window.importProgressInterval) {
            clearInterval(window.importProgressInterval);
        }

        // Reset progress bar
        progressBar.style.width = '0%';
        progressBar.setAttribute('aria-valuenow', 0);
        progressText.textContent = '0%';
        statusText.textContent = 'Preparing upload...';
    }

    // Reset form state if there are errors
    {% if form.errors %}
        resetImportState();
    {% endif %}
});
</script>
{% endblock %}
