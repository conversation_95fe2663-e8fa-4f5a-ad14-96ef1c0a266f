#!/usr/bin/env python3
"""
Debug the topic creation issue by analyzing what topics are being created
"""
import csv
from collections import defaultdict

def analyze_csv_topics():
    """Analyze what topics are in the CSV and identify potential conflicts"""
    
    print("🔍 ANALYZING CSV TOPICS FOR POTENTIAL CONFLICTS...")
    print("=" * 60)
    
    # Group topics by subject and class level
    topics_by_subject = defaultdict(lambda: defaultdict(set))
    
    with open('Grade_9_questions_PERFECT.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # Skip header
        
        for row in reader:
            if len(row) >= 3:
                subject = row[0].strip()
                class_level = row[1].strip()
                topic = row[2].strip()
                
                topics_by_subject[subject][class_level].add(topic)
    
    # Display analysis
    total_subjects = 0
    total_topics = 0
    
    for subject, class_levels in topics_by_subject.items():
        total_subjects += 1
        print(f"\n📚 {subject}:")
        
        for class_level, topics in class_levels.items():
            topic_count = len(topics)
            total_topics += topic_count
            print(f"   📋 {class_level}: {topic_count} unique topics")
            
            # List topics with order they would get
            for i, topic in enumerate(sorted(topics), 1):
                print(f"      {i:2d}. {topic}")
    
    print(f"\n📊 SUMMARY:")
    print(f"   Subjects: {total_subjects}")
    print(f"   Total unique topics: {total_topics}")
    
    return topics_by_subject

def create_topic_creation_script():
    """Create a script to pre-create topics to avoid conflicts"""
    
    topics_data = analyze_csv_topics()
    
    print(f"\n🔧 CREATING TOPIC PRE-CREATION SCRIPT...")
    
    script_content = '''#!/usr/bin/env python3
"""
Pre-create topics to avoid database conflicts during CSV import
"""
import os
import sys
import django

# Setup Django
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pentora_platform.settings')
django.setup()

from subjects.models import Subject, ClassLevel, Topic

def create_topics():
    """Pre-create all topics to avoid conflicts"""
    
    print("🔧 PRE-CREATING TOPICS TO AVOID CONFLICTS...")
    print("=" * 50)
    
    topics_data = {
'''
    
    # Add the topics data to the script
    for subject, class_levels in topics_data.items():
        script_content += f'        "{subject}": {{\n'
        for class_level, topics in class_levels.items():
            topics_list = sorted(list(topics))
            script_content += f'            "{class_level}": {topics_list},\n'
        script_content += '        },\n'
    
    script_content += '''    }
    
    created_count = 0
    
    for subject_name, class_levels in topics_data.items():
        print(f"\\n📚 Processing {subject_name}...")
        
        # Get or create subject
        subject, created = Subject.objects.get_or_create(
            name=subject_name,
            defaults={
                'description': f'Auto-created subject for {subject_name}',
                'icon': '📚',
                'color': '#3B82F6',
                'order': 1
            }
        )
        if created:
            print(f"   ✅ Created subject: {subject_name}")
        
        for class_level_name, topics in class_levels.items():
            print(f"   📋 Processing {class_level_name}...")
            
            # Get or create class level
            class_level, created = ClassLevel.objects.get_or_create(
                subject=subject,
                name=class_level_name,
                defaults={
                    'level_number': 9,  # Assuming Grade 9
                    'description': f'Auto-created class level for {class_level_name}',
                    'pass_percentage': 60
                }
            )
            if created:
                print(f"      ✅ Created class level: {class_level_name}")
            
            # Create topics with proper ordering
            for i, topic_title in enumerate(topics, 1):
                topic, created = Topic.objects.get_or_create(
                    class_level=class_level,
                    title=topic_title,
                    defaults={
                        'description': f'Auto-created topic for {topic_title}',
                        'order': i,  # Use sequential order
                        'difficulty_level': 'beginner',
                        'estimated_duration': 30,
                        'is_active': True
                    }
                )
                if created:
                    print(f"         ✅ Created topic {i:2d}: {topic_title}")
                    created_count += 1
                else:
                    print(f"         ℹ️  Topic exists {i:2d}: {topic_title}")
    
    print(f"\\n🎉 COMPLETED!")
    print(f"   Created {created_count} new topics")
    print(f"   Now you can upload your CSV without conflicts!")

if __name__ == "__main__":
    create_topics()
'''
    
    # Write the script
    with open('pre_create_topics.py', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"✅ Created script: pre_create_topics.py")
    print(f"\n🚀 SOLUTION:")
    print(f"   1. Run: python pre_create_topics.py")
    print(f"   2. Then upload: Grade_9_questions_PERFECT.csv")
    print(f"   3. The topics will exist, so only questions will be added!")

if __name__ == "__main__":
    create_topic_creation_script()
