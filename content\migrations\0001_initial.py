# Generated by Django 5.2.1 on 2025-07-05 14:54

import django.core.validators
import django.utils.timezone
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AnswerChoice',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('choice_text', models.CharField(max_length=500)),
                ('is_correct', models.BooleanField(default=False)),
                ('order', models.PositiveIntegerField(default=0)),
            ],
            options={
                'verbose_name': 'Answer Choice',
                'verbose_name_plural': 'Answer Choices',
                'db_table': 'answer_choices',
                'ordering': ['question', 'order'],
            },
        ),
        migrations.CreateModel(
            name='Passage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.Char<PERSON>ield(max_length=200)),
                ('content', models.TextField(help_text='The reading passage content')),
                ('passage_type', models.Char<PERSON>ield(choices=[('story', 'Story'), ('article', 'Article'), ('poem', 'Poem'), ('dialogue', 'Dialogue'), ('informational', 'Informational Text')], default='story', max_length=20)),
                ('author', models.CharField(blank=True, max_length=100)),
                ('source', models.CharField(blank=True, max_length=200)),
                ('reading_level', models.CharField(blank=True, help_text='Grade level or difficulty', max_length=20)),
                ('estimated_reading_time', models.PositiveIntegerField(default=5, help_text='Estimated reading time in minutes')),
                ('is_active', models.BooleanField(default=True)),
                ('order', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Reading Passage',
                'verbose_name_plural': 'Reading Passages',
                'db_table': 'passages',
                'ordering': ['topic', 'order', 'title'],
            },
        ),
        migrations.CreateModel(
            name='Question',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('question_text', models.TextField()),
                ('question_type', models.CharField(choices=[('multiple_choice', 'Multiple Choice'), ('fill_blank', 'Fill in the Blank'), ('true_false', 'True/False'), ('short_answer', 'Short Answer')], default='multiple_choice', max_length=20)),
                ('difficulty', models.CharField(choices=[('easy', 'Easy'), ('medium', 'Medium'), ('hard', 'Hard')], default='medium', max_length=10)),
                ('order', models.PositiveIntegerField(default=0, help_text='Order within passage or topic')),
                ('image', models.ImageField(blank=True, null=True, upload_to='questions/images/')),
                ('audio_file', models.FileField(blank=True, null=True, upload_to='questions/audio/')),
                ('correct_answer', models.TextField(help_text="Correct answer or answer key. For text questions, separate multiple acceptable answers with commas (e.g., 'smart, clever, intelligent, wise')")),
                ('explanation', models.TextField(blank=True, help_text='Explanation for the correct answer')),
                ('points', models.PositiveIntegerField(default=1)),
                ('time_limit', models.PositiveIntegerField(default=45, help_text='Time limit per question in seconds')),
                ('explanation_display_time', models.PositiveIntegerField(default=5, help_text='Time to show explanation in seconds')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Question',
                'verbose_name_plural': 'Questions',
                'db_table': 'questions',
                'ordering': ['topic', 'difficulty', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='Quiz',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('total_questions', models.PositiveIntegerField(default=10)),
                ('time_limit', models.PositiveIntegerField(default=300, help_text='Time limit in seconds')),
                ('question_ids', models.JSONField(default=list, help_text='Shuffled question IDs for this quiz attempt')),
                ('seed', models.IntegerField(default=0, help_text='Random seed for consistent shuffling')),
                ('is_completed', models.BooleanField(default=False)),
                ('started_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('score', models.PositiveIntegerField(default=0)),
                ('total_points', models.PositiveIntegerField(default=0)),
                ('percentage', models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('attempt_number', models.PositiveIntegerField(default=1)),
            ],
            options={
                'verbose_name': 'Quiz',
                'verbose_name_plural': 'Quizzes',
                'db_table': 'quizzes',
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='QuizAnswer',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('user_answer', models.TextField()),
                ('is_correct', models.BooleanField(default=False)),
                ('points_earned', models.PositiveIntegerField(default=0)),
                ('time_taken', models.PositiveIntegerField(default=0, help_text='Time taken in seconds')),
                ('answered_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'verbose_name': 'Quiz Answer',
                'verbose_name_plural': 'Quiz Answers',
                'db_table': 'quiz_answers',
            },
        ),
        migrations.CreateModel(
            name='StudyNote',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('content', models.TextField(help_text='Main study content in markdown or HTML')),
                ('image', models.ImageField(blank=True, null=True, upload_to='study_notes/images/')),
                ('audio_file', models.FileField(blank=True, null=True, upload_to='study_notes/audio/')),
                ('video_url', models.URLField(blank=True, help_text='YouTube or other video URL')),
                ('order', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Study Note',
                'verbose_name_plural': 'Study Notes',
                'db_table': 'study_notes',
                'ordering': ['topic', 'order'],
            },
        ),
        migrations.CreateModel(
            name='Test',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('test_type', models.CharField(choices=[('topic_test', 'Topic Test'), ('level_exam', 'Level Exam'), ('practice_test', 'Practice Test')], default='topic_test', max_length=20)),
                ('total_questions', models.PositiveIntegerField(default=10)),
                ('time_limit', models.PositiveIntegerField(default=1800, help_text='Time limit in seconds')),
                ('pass_percentage', models.PositiveIntegerField(default=60)),
                ('question_ids', models.JSONField(default=list, help_text='Shuffled question IDs for this exam attempt')),
                ('seed', models.IntegerField(default=0, help_text='Random seed for consistent shuffling')),
                ('is_completed', models.BooleanField(default=False)),
                ('started_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('score', models.PositiveIntegerField(default=0)),
                ('total_points', models.PositiveIntegerField(default=0)),
                ('percentage', models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('passed', models.BooleanField(default=False)),
                ('attempt_number', models.PositiveIntegerField(default=1)),
            ],
            options={
                'verbose_name': 'Test',
                'verbose_name_plural': 'Tests',
                'db_table': 'tests',
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='TestAnswer',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('user_answer', models.TextField()),
                ('is_correct', models.BooleanField(default=False)),
                ('points_earned', models.PositiveIntegerField(default=0)),
                ('time_taken', models.PositiveIntegerField(default=0, help_text='Time taken in seconds')),
                ('answered_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'verbose_name': 'Test Answer',
                'verbose_name_plural': 'Test Answers',
                'db_table': 'test_answers',
            },
        ),
    ]
