#!/usr/bin/env python3
"""
Fix correct_answer field to use letters (a, b, c, d) instead of full text
"""
import csv

def fix_correct_answers():
    """Fix correct_answer field to use proper letter format"""
    
    print("🔧 FIXING CORRECT ANSWER FORMAT...")
    print("=" * 60)
    
    fixed_rows = []
    total_rows = 0
    fixed_count = 0
    error_count = 0
    
    with open('Grade_9_questions_ORDERED.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        header = next(reader)
        fixed_rows.append(header)
        
        for row_num, row in enumerate(reader, 2):  # Start from 2 (after header)
            total_rows += 1
            
            if len(row) < 14:
                print(f"❌ Row {row_num}: Insufficient fields")
                error_count += 1
                continue
            
            question_type = row[4].strip().lower()
            correct_answer = row[5].strip()
            
            # Fix based on question type
            if question_type == 'multiple_choice':
                # Get the choices
                choice_a = row[10].strip()
                choice_b = row[11].strip()
                choice_c = row[12].strip()
                choice_d = row[13].strip()
                
                # Find which choice matches the correct answer
                new_correct_answer = find_matching_choice(correct_answer, choice_a, choice_b, choice_c, choice_d)
                
                if new_correct_answer:
                    row[5] = new_correct_answer
                    fixed_count += 1
                else:
                    print(f"❌ Row {row_num}: Could not match answer '{correct_answer}' to choices")
                    print(f"   Choices: a='{choice_a}', b='{choice_b}', c='{choice_c}', d='{choice_d}'")
                    error_count += 1
                    # Keep original for now
            
            elif question_type == 'true_false':
                # Standardize true/false answers
                if correct_answer.upper() in ['TRUE', 'T', 'YES', '1']:
                    row[5] = 'true'
                    fixed_count += 1
                elif correct_answer.upper() in ['FALSE', 'F', 'NO', '0']:
                    row[5] = 'false'
                    fixed_count += 1
                elif correct_answer.lower() in ['true', 'false']:
                    # Already correct
                    pass
                else:
                    print(f"❌ Row {row_num}: Invalid true/false answer '{correct_answer}'")
                    error_count += 1
            
            elif question_type in ['short_answer', 'fill_blank']:
                # These can keep their text answers
                pass
            
            fixed_rows.append(row)
    
    # Write the fixed file
    with open('Grade_9_questions_FINAL.csv', 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        for row in fixed_rows:
            writer.writerow(row)
    
    print(f"\n📊 RESULTS:")
    print(f"   📁 Total rows processed: {total_rows}")
    print(f"   ✅ Fixed answers: {fixed_count}")
    print(f"   ❌ Errors: {error_count}")
    print(f"   📈 Success rate: {((total_rows - error_count)/total_rows)*100:.1f}%")
    print(f"\n💾 Fixed file saved as: Grade_9_questions_FINAL.csv")
    
    # Validate the fixes
    validate_fixes()

def find_matching_choice(correct_answer, choice_a, choice_b, choice_c, choice_d):
    """Find which choice (a, b, c, d) matches the correct answer text"""
    
    # Clean the correct answer for comparison
    correct_clean = correct_answer.strip().lower()
    
    choices = {
        'a': choice_a.strip().lower(),
        'b': choice_b.strip().lower(),
        'c': choice_c.strip().lower(),
        'd': choice_d.strip().lower()
    }
    
    # Try exact match first
    for letter, choice_text in choices.items():
        if correct_clean == choice_text:
            return letter
    
    # Try partial match (correct answer contained in choice)
    for letter, choice_text in choices.items():
        if correct_clean in choice_text or choice_text in correct_clean:
            return letter
    
    # Try word-by-word match for longer answers
    correct_words = set(correct_clean.split())
    best_match = None
    best_score = 0
    
    for letter, choice_text in choices.items():
        choice_words = set(choice_text.split())
        common_words = correct_words.intersection(choice_words)
        score = len(common_words) / max(len(correct_words), len(choice_words))
        
        if score > best_score and score > 0.5:  # At least 50% word overlap
            best_score = score
            best_match = letter
    
    return best_match

def validate_fixes():
    """Validate that the fixes are correct"""
    
    print(f"\n🔍 VALIDATING FIXES...")
    
    valid_mc_answers = ['a', 'b', 'c', 'd']
    valid_tf_answers = ['true', 'false']
    
    validation_errors = []
    total_questions = 0
    
    with open('Grade_9_questions_FINAL.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # Skip header
        
        for row_num, row in enumerate(reader, 2):
            total_questions += 1
            
            if len(row) >= 6:
                question_type = row[4].strip().lower()
                correct_answer = row[5].strip().lower()
                
                if question_type == 'multiple_choice':
                    if correct_answer not in valid_mc_answers:
                        validation_errors.append(f"Row {row_num}: Invalid MC answer '{correct_answer}'")
                
                elif question_type == 'true_false':
                    if correct_answer not in valid_tf_answers:
                        validation_errors.append(f"Row {row_num}: Invalid T/F answer '{correct_answer}'")
    
    print(f"   📊 Total questions validated: {total_questions}")
    print(f"   ❌ Validation errors: {len(validation_errors)}")
    
    if validation_errors:
        print(f"\n🔍 VALIDATION ERRORS (first 10):")
        for error in validation_errors[:10]:
            print(f"   {error}")
        if len(validation_errors) > 10:
            print(f"   ... and {len(validation_errors) - 10} more errors")
    else:
        print(f"   ✅ All answers are properly formatted!")

if __name__ == "__main__":
    fix_correct_answers()
