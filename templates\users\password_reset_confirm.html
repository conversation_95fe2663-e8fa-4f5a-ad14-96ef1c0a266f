{% extends 'base.html' %}

{% block title %}Set New Password - Pentora{% endblock %}

{% block extra_css %}
<style>
    .confirm-container {
        background: linear-gradient(135deg, #F9FAFB 0%, #E5E7EB 100%);
        min-height: calc(100vh - 80px);
    }
    
    .confirm-card {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        border: 1px solid #E5E7EB;
    }
    
    .form-input {
        border: 2px solid #E5E7EB;
        border-radius: 0.75rem;
        padding: 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        background: #F9FAFB;
    }
    
    .form-input:focus {
        border-color: var(--primary-blue);
        background: white;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        outline: none;
    }
    
    .form-label {
        font-weight: 600;
        color: var(--text-dark);
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
    }
    
    .btn-primary-custom {
        background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
        border: none;
        border-radius: 0.75rem;
        padding: 1rem;
        font-weight: 600;
        font-size: 1rem;
        color: white;
        transition: all 0.2s ease;
    }
    
    .btn-primary-custom:hover {
        transform: translateY(-1px);
        box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.4);
    }
    
    .btn-primary-custom:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }
    
    .confirm-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.3);
    }
    
    .password-strength {
        height: 4px;
        border-radius: 2px;
        margin-top: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .strength-weak { background: #EF4444; width: 25%; }
    .strength-fair { background: #F59E0B; width: 50%; }
    .strength-good { background: #10B981; width: 75%; }
    .strength-strong { background: #059669; width: 100%; }
</style>
{% endblock %}

{% block content %}
<div class="confirm-container flex items-center justify-center px-4 py-8">
    <div class="w-full max-w-md">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="confirm-icon">
                <i class="fas fa-lock text-white text-2xl"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Set New Password</h1>
            <p class="text-gray-600 text-lg">Create a strong password for your account</p>
        </div>

        <!-- Password Reset Form -->
        <div class="confirm-card p-8">
            <form method="post" class="space-y-6" id="password-reset-form">
                {% csrf_token %}
                
                <!-- New Password -->
                <div>
                    <label class="form-label block">
                        <i class="fas fa-key text-blue-500 mr-2"></i>
                        New Password
                    </label>
                    <div class="relative">
                        <input 
                            type="password" 
                            name="password1" 
                            placeholder="Enter your new password" 
                            class="form-input w-full pr-12"
                            required
                            autocomplete="new-password"
                            id="password1"
                            onkeyup="checkPasswordStrength()"
                        >
                        <button 
                            type="button" 
                            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                            onclick="togglePassword('password1', 'password1-icon')"
                        >
                            <i class="fas fa-eye" id="password1-icon"></i>
                        </button>
                    </div>
                    <div class="password-strength bg-gray-200" id="password-strength"></div>
                    <div class="text-xs mt-1" id="password-feedback">
                        Password must be at least 8 characters long
                    </div>
                </div>

                <!-- Confirm Password -->
                <div>
                    <label class="form-label block">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        Confirm New Password
                    </label>
                    <div class="relative">
                        <input 
                            type="password" 
                            name="password2" 
                            placeholder="Confirm your new password" 
                            class="form-input w-full pr-12"
                            required
                            autocomplete="new-password"
                            id="password2"
                            onkeyup="checkPasswordMatch()"
                        >
                        <button 
                            type="button" 
                            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                            onclick="togglePassword('password2', 'password2-icon')"
                        >
                            <i class="fas fa-eye" id="password2-icon"></i>
                        </button>
                    </div>
                    <div class="text-xs mt-1" id="password-match-feedback"></div>
                </div>

                <!-- Password Requirements -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 class="font-semibold text-blue-800 mb-2">
                        <i class="fas fa-info-circle mr-2"></i>
                        Password Requirements
                    </h3>
                    <ul class="text-sm text-blue-700 space-y-1">
                        <li id="req-length" class="flex items-center">
                            <i class="fas fa-times text-red-500 mr-2 w-4"></i>
                            At least 8 characters long
                        </li>
                        <li id="req-lowercase" class="flex items-center">
                            <i class="fas fa-times text-red-500 mr-2 w-4"></i>
                            Contains lowercase letters
                        </li>
                        <li id="req-uppercase" class="flex items-center">
                            <i class="fas fa-times text-red-500 mr-2 w-4"></i>
                            Contains uppercase letters
                        </li>
                        <li id="req-numbers" class="flex items-center">
                            <i class="fas fa-times text-red-500 mr-2 w-4"></i>
                            Contains numbers
                        </li>
                    </ul>
                </div>

                <!-- Submit Button -->
                <button type="submit" class="btn-primary-custom w-full" id="submit-btn" disabled>
                    <i class="fas fa-shield-alt mr-2"></i>
                    Update Password
                </button>
            </form>

            <!-- Security Notice -->
            <div class="mt-6 bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-center mb-2">
                    <i class="fas fa-shield-alt text-gray-500 mr-2"></i>
                    <span class="font-medium text-gray-700">Security Tips</span>
                </div>
                <ul class="text-xs text-gray-600 space-y-1">
                    <li>• Use a unique password you haven't used before</li>
                    <li>• Consider using a mix of letters, numbers, and symbols</li>
                    <li>• Avoid using personal information in your password</li>
                    <li>• Keep your password private and secure</li>
                </ul>
            </div>
        </div>

        <!-- Help Section -->
        <div class="mt-8 text-center">
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div class="flex items-center justify-center mb-2">
                    <i class="fas fa-question-circle text-yellow-600 mr-2"></i>
                    <span class="font-medium text-yellow-800">Need Help?</span>
                </div>
                <p class="text-sm text-yellow-700 mb-3">
                    Having trouble creating a new password? Our support team is here to help.
                </p>
                <a href="{% url 'core:contact' %}" class="text-sm text-yellow-700 hover:text-yellow-900 font-medium underline">
                    Contact Support Team
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(inputId, iconId) {
    const passwordInput = document.getElementById(inputId);
    const passwordIcon = document.getElementById(iconId);
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        passwordIcon.className = 'fas fa-eye';
    }
}

function checkPasswordStrength() {
    const password = document.getElementById('password1').value;
    const strengthBar = document.getElementById('password-strength');
    const feedback = document.getElementById('password-feedback');
    
    // Check requirements
    const requirements = {
        length: password.length >= 8,
        lowercase: /[a-z]/.test(password),
        uppercase: /[A-Z]/.test(password),
        numbers: /[0-9]/.test(password)
    };
    
    // Update requirement indicators
    updateRequirement('req-length', requirements.length);
    updateRequirement('req-lowercase', requirements.lowercase);
    updateRequirement('req-uppercase', requirements.uppercase);
    updateRequirement('req-numbers', requirements.numbers);
    
    // Calculate strength
    let strength = 0;
    Object.values(requirements).forEach(met => {
        if (met) strength++;
    });
    
    // Update strength bar
    strengthBar.className = 'password-strength bg-gray-200';
    
    if (password.length === 0) {
        feedback.textContent = 'Password must be at least 8 characters long';
        feedback.className = 'text-xs text-gray-500 mt-1';
    } else if (strength <= 1) {
        strengthBar.classList.add('strength-weak');
        feedback.textContent = 'Weak password. Meet more requirements.';
        feedback.className = 'text-xs text-red-500 mt-1';
    } else if (strength === 2) {
        strengthBar.classList.add('strength-fair');
        feedback.textContent = 'Fair password. Consider adding more variety.';
        feedback.className = 'text-xs text-yellow-600 mt-1';
    } else if (strength === 3) {
        strengthBar.classList.add('strength-good');
        feedback.textContent = 'Good password!';
        feedback.className = 'text-xs text-green-600 mt-1';
    } else {
        strengthBar.classList.add('strength-strong');
        feedback.textContent = 'Strong password!';
        feedback.className = 'text-xs text-green-600 mt-1';
    }
    
    checkFormValidity();
}

function updateRequirement(elementId, met) {
    const element = document.getElementById(elementId);
    const icon = element.querySelector('i');
    
    if (met) {
        icon.className = 'fas fa-check text-green-500 mr-2 w-4';
    } else {
        icon.className = 'fas fa-times text-red-500 mr-2 w-4';
    }
}

function checkPasswordMatch() {
    const password1 = document.getElementById('password1').value;
    const password2 = document.getElementById('password2').value;
    const feedback = document.getElementById('password-match-feedback');
    
    if (password2.length === 0) {
        feedback.textContent = '';
        feedback.className = 'text-xs mt-1';
    } else if (password1 === password2) {
        feedback.textContent = 'Passwords match!';
        feedback.className = 'text-xs text-green-600 mt-1';
    } else {
        feedback.textContent = 'Passwords do not match';
        feedback.className = 'text-xs text-red-500 mt-1';
    }
    
    checkFormValidity();
}

function checkFormValidity() {
    const password1 = document.getElementById('password1').value;
    const password2 = document.getElementById('password2').value;
    const submitBtn = document.getElementById('submit-btn');
    
    const requirements = {
        length: password1.length >= 8,
        lowercase: /[a-z]/.test(password1),
        uppercase: /[A-Z]/.test(password1),
        numbers: /[0-9]/.test(password1)
    };
    
    const allRequirementsMet = Object.values(requirements).every(met => met);
    const passwordsMatch = password1 === password2 && password1.length > 0;
    const formValid = allRequirementsMet && passwordsMatch;
    
    submitBtn.disabled = !formValid;
}

// Add event listeners
document.addEventListener('DOMContentLoaded', function() {
    const password1 = document.getElementById('password1');
    const password2 = document.getElementById('password2');
    
    password1.addEventListener('input', checkPasswordStrength);
    password2.addEventListener('input', checkPasswordMatch);
});
</script>
{% endblock %}
