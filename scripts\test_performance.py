#!/usr/bin/env python
"""
Test performance improvements for the Pentora platform
This script measures page load times and database query performance
"""

import os
import sys
import django
import time
import requests
from urllib.parse import urljoin

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pentora_platform.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from django.db import connection
from content.models import Question
from subjects.models import Subject, ClassLevel, Topic

User = get_user_model()


class PerformanceTester:
    """
    Test performance of various system components
    """
    
    def __init__(self):
        self.client = Client()
        self.admin_user = None
        self.results = {}
    
    def setup_test_user(self):
        """Create or get admin user for testing"""
        try:
            self.admin_user = User.objects.filter(is_staff=True).first()
            if not self.admin_user:
                self.admin_user = User.objects.create_superuser(
                    email='<EMAIL>',
                    password='testpass123',
                    first_name='Test',
                    last_name='Admin'
                )
            
            # Login the user
            self.client.force_login(self.admin_user)
            print("✅ Test user setup completed")
            
        except Exception as e:
            print(f"❌ Test user setup failed: {e}")
            return False
        
        return True
    
    def test_database_queries(self):
        """Test database query performance"""
        print("\n🧪 Testing Database Query Performance")
        print("-" * 40)
        
        # Reset query log
        connection.queries_log.clear() if hasattr(connection, 'queries_log') else None
        
        # Test 1: Question loading
        start_time = time.time()
        questions = list(Question.objects.select_related(
            'topic__class_level__subject'
        ).filter(is_active=True)[:100])
        end_time = time.time()
        
        query_time = end_time - start_time
        query_count = len(connection.queries) if hasattr(connection, 'queries') else 0
        
        print(f"📊 Question Loading:")
        print(f"   - Time: {query_time:.3f} seconds")
        print(f"   - Records: {len(questions)}")
        print(f"   - Queries: {query_count}")
        
        self.results['question_loading'] = {
            'time': query_time,
            'records': len(questions),
            'queries': query_count
        }
        
        # Test 2: Filter options loading
        start_time = time.time()
        subjects = list(Subject.objects.filter(is_active=True))
        class_levels = list(ClassLevel.objects.filter(is_active=True))
        topics = list(Topic.objects.filter(is_active=True)[:50])
        end_time = time.time()
        
        filter_time = end_time - start_time
        
        print(f"📊 Filter Options Loading:")
        print(f"   - Time: {filter_time:.3f} seconds")
        print(f"   - Subjects: {len(subjects)}")
        print(f"   - Class Levels: {len(class_levels)}")
        print(f"   - Topics: {len(topics)}")
        
        self.results['filter_loading'] = {
            'time': filter_time,
            'subjects': len(subjects),
            'class_levels': len(class_levels),
            'topics': len(topics)
        }
        
        return True
    
    def test_page_load_times(self):
        """Test page load times for admin panel"""
        print("\n🧪 Testing Page Load Times")
        print("-" * 40)
        
        pages_to_test = [
            ('/my-admin/', 'Dashboard'),
            ('/my-admin/questions/', 'Questions Management'),
            ('/my-admin/subjects/', 'Subjects Management'),
            ('/my-admin/topics/', 'Topics Management'),
            ('/my-admin/duplicate-questions/', 'Duplicate Questions'),
        ]
        
        page_results = {}
        
        for url, name in pages_to_test:
            try:
                start_time = time.time()
                response = self.client.get(url)
                end_time = time.time()
                
                load_time = end_time - start_time
                status_code = response.status_code
                
                print(f"📄 {name}:")
                print(f"   - URL: {url}")
                print(f"   - Time: {load_time:.3f} seconds")
                print(f"   - Status: {status_code}")
                
                page_results[name] = {
                    'url': url,
                    'time': load_time,
                    'status': status_code,
                    'success': status_code == 200
                }
                
            except Exception as e:
                print(f"❌ Failed to test {name}: {e}")
                page_results[name] = {
                    'url': url,
                    'error': str(e),
                    'success': False
                }
        
        self.results['page_loads'] = page_results
        return True
    
    def test_pagination_performance(self):
        """Test pagination performance"""
        print("\n🧪 Testing Pagination Performance")
        print("-" * 40)
        
        try:
            # Test different page sizes
            page_sizes = [25, 50, 100]
            pagination_results = {}
            
            for page_size in page_sizes:
                start_time = time.time()
                
                url = f'/my-admin/questions/?page=1&page_size={page_size}'
                response = self.client.get(url)
                
                end_time = time.time()
                load_time = end_time - start_time
                
                print(f"📄 Page Size {page_size}:")
                print(f"   - Time: {load_time:.3f} seconds")
                print(f"   - Status: {response.status_code}")
                
                pagination_results[page_size] = {
                    'time': load_time,
                    'status': response.status_code
                }
            
            self.results['pagination'] = pagination_results
            
        except Exception as e:
            print(f"❌ Pagination test failed: {e}")
            return False
        
        return True
    
    def test_cache_performance(self):
        """Test cache performance"""
        print("\n🧪 Testing Cache Performance")
        print("-" * 40)
        
        try:
            from django.core.cache import cache
            
            # Test cache operations
            start_time = time.time()
            
            # Set some test data
            for i in range(100):
                cache.set(f'test_key_{i}', f'test_value_{i}', 300)
            
            # Get the data back
            for i in range(100):
                value = cache.get(f'test_key_{i}')
            
            end_time = time.time()
            cache_time = end_time - start_time
            
            print(f"📊 Cache Operations:")
            print(f"   - Time: {cache_time:.3f} seconds")
            print(f"   - Operations: 200 (100 sets + 100 gets)")
            
            # Clean up
            for i in range(100):
                cache.delete(f'test_key_{i}')
            
            self.results['cache'] = {
                'time': cache_time,
                'operations': 200
            }
            
        except Exception as e:
            print(f"❌ Cache test failed: {e}")
            return False
        
        return True
    
    def generate_report(self):
        """Generate performance test report"""
        print("\n📊 PERFORMANCE TEST REPORT")
        print("="*50)
        
        # Database performance
        if 'question_loading' in self.results:
            q_result = self.results['question_loading']
            print(f"📊 Database Performance:")
            print(f"   - Question loading: {q_result['time']:.3f}s ({q_result['records']} records)")
            
        if 'filter_loading' in self.results:
            f_result = self.results['filter_loading']
            print(f"   - Filter loading: {f_result['time']:.3f}s")
        
        # Page load performance
        if 'page_loads' in self.results:
            print(f"\n📄 Page Load Performance:")
            for page_name, result in self.results['page_loads'].items():
                if result.get('success'):
                    print(f"   - {page_name}: {result['time']:.3f}s")
                else:
                    print(f"   - {page_name}: FAILED")
        
        # Pagination performance
        if 'pagination' in self.results:
            print(f"\n📄 Pagination Performance:")
            for page_size, result in self.results['pagination'].items():
                print(f"   - Page size {page_size}: {result['time']:.3f}s")
        
        # Cache performance
        if 'cache' in self.results:
            c_result = self.results['cache']
            print(f"\n💾 Cache Performance:")
            print(f"   - 200 operations: {c_result['time']:.3f}s")
        
        print("\n🎯 Performance Analysis:")
        
        # Analyze results
        if 'question_loading' in self.results:
            q_time = self.results['question_loading']['time']
            if q_time < 0.5:
                print("✅ Question loading: EXCELLENT (< 0.5s)")
            elif q_time < 1.0:
                print("✅ Question loading: GOOD (< 1.0s)")
            elif q_time < 2.0:
                print("⚠️ Question loading: ACCEPTABLE (< 2.0s)")
            else:
                print("❌ Question loading: NEEDS IMPROVEMENT (> 2.0s)")
        
        # Page load analysis
        if 'page_loads' in self.results:
            avg_time = sum(r['time'] for r in self.results['page_loads'].values() if r.get('success', False))
            success_count = sum(1 for r in self.results['page_loads'].values() if r.get('success', False))
            
            if success_count > 0:
                avg_time = avg_time / success_count
                if avg_time < 1.0:
                    print("✅ Average page load: EXCELLENT (< 1.0s)")
                elif avg_time < 2.0:
                    print("✅ Average page load: GOOD (< 2.0s)")
                elif avg_time < 5.0:
                    print("⚠️ Average page load: ACCEPTABLE (< 5.0s)")
                else:
                    print("❌ Average page load: NEEDS IMPROVEMENT (> 5.0s)")
        
        print("="*50)
        print("✅ Performance testing completed!")
        print("="*50)
    
    def run_all_tests(self):
        """Run all performance tests"""
        print("🚀 Starting Performance Tests")
        print("="*50)
        
        if not self.setup_test_user():
            return False
        
        tests = [
            self.test_database_queries,
            self.test_page_load_times,
            self.test_pagination_performance,
            self.test_cache_performance
        ]
        
        success_count = 0
        for test in tests:
            if test():
                success_count += 1
        
        self.generate_report()
        
        return success_count == len(tests)


def main():
    """Main testing function"""
    tester = PerformanceTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 All performance tests completed successfully!")
    else:
        print("\n⚠️ Some performance tests failed. Check the output above.")
    
    return success


if __name__ == '__main__':
    main()
