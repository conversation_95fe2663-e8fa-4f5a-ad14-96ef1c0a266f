import csv

with open('Grade_9_questions_UPLOAD.csv', 'r', encoding='utf-8') as f:
    reader = csv.reader(f)
    next(reader)  # Skip header
    
    for row_num, row in enumerate(reader, 2):
        if len(row) >= 6:
            qt = row[4].strip().lower()
            ca = row[5].strip().lower()
            
            valid = False
            if qt == 'multiple_choice' and ca in ['a','b','c','d']:
                valid = True
            elif qt == 'true_false' and ca in ['true','false']:
                valid = True
            elif qt in ['short_answer','fill_blank']:
                valid = True
            
            if not valid:
                print(f'Row {row_num}: {qt} -> "{ca}"')
