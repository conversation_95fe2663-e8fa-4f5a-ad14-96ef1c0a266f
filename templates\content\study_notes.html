{% extends 'base.html' %}

{% block title %}Describing with Adjectives - Study Notes - Pentora{% endblock %}

{% block extra_css %}
<style>
    .study-content {
        line-height: 1.8;
    }
    .study-content h2 {
        color: #3B82F6;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    .study-content h3 {
        color: #6366F1;
        margin-top: 1.5rem;
        margin-bottom: 0.75rem;
    }
    .example-box {
        background: linear-gradient(135deg, #EBF8FF 0%, #E0E7FF 100%);
        border-left: 4px solid #3B82F6;
    }
    .highlight {
        background-color: #FEF3C7;
        padding: 2px 4px;
        border-radius: 4px;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<!-- Mobile-first container with full width on mobile -->
<div class="w-full">
    <!-- Breadcrumb - only show on larger screens -->
    <div class="hidden sm:block container mx-auto px-4 py-4">
        <div class="breadcrumbs text-sm mb-6">
            <ul>
                <li><a href="{% url 'core:dashboard' %}">Dashboard</a></li>
                <li><a href="{% url 'subjects:list' %}">Subjects</a></li>
                <li><a href="#">English</a></li>
                <li><a href="#">Class 2</a></li>
                <li>Describing with Adjectives</li>
            </ul>
        </div>
    </div>

    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-xl mb-4 sm:mb-6">
        <div class="container mx-auto px-4 py-6 sm:py-8">
            <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between">
                <div class="flex-1">
                    <h1 class="text-2xl sm:text-3xl md:text-4xl font-bold mb-2">Describing with Adjectives</h1>
                    <p class="text-white/90 mb-4 text-base sm:text-lg">
                        Learn how adjectives describe nouns and make sentences more interesting.
                    </p>
                    <div class="flex flex-wrap gap-2">
                        <div class="bg-white/20 px-3 py-1 rounded-full text-sm">Topic 3 of 8</div>
                        <div class="bg-white/20 px-3 py-1 rounded-full text-sm">18 min read</div>
                        <div class="bg-white/20 px-3 py-1 rounded-full text-sm">Elementary</div>
                    </div>
                </div>
                <div class="mt-4 lg:mt-0 w-full lg:w-auto">
                    <div class="bg-white/10 backdrop-blur rounded-xl p-4 text-center lg:text-left">
                        <div class="text-white/80 text-sm mb-1">Reading Progress</div>
                        <div class="text-white text-3xl font-bold">60%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="lg:container lg:mx-auto lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-0 lg:gap-8">
            <!-- Study Content - Full width on mobile -->
            <div class="lg:col-span-3">
                <!-- Remove the card wrapper on mobile, keep it on desktop -->
                <div class="lg:bg-white lg:shadow-xl lg:rounded-xl">
                    <div class="px-4 py-6 sm:px-6 lg:p-8 study-content">
                    <!-- Learning Objectives -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <div class="flex items-start">
                            <i class="fas fa-bullseye text-blue-600 mr-3 mt-1"></i>
                            <div>
                                <h3 class="font-bold text-blue-900 mb-2">Learning Objectives</h3>
                                <div class="text-sm text-blue-800">
                                    By the end of this topic, you will be able to:
                                    <ul class="list-disc list-inside mt-2 space-y-1">
                                        <li>Identify adjectives in sentences</li>
                                        <li>Use adjectives to describe nouns</li>
                                        <li>Place adjectives correctly in sentences</li>
                                        <li>Choose appropriate adjectives for different situations</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Main Content -->
                    <h2>What are Adjectives?</h2>
                    <p>
                        <span class="highlight">Adjectives</span> are words that describe or give more information about nouns.
                        They help us understand what something looks like, feels like, sounds like, or what kind of thing it is.
                    </p>

                    <p>
                        Think of adjectives as "describing words" that make our sentences more interesting and help others
                        understand exactly what we're talking about.
                    </p>

                    <div class="example-box p-4 rounded-lg my-6">
                        <h4 class="font-bold text-primary mb-2">
                            <i class="fas fa-lightbulb mr-2"></i>Examples
                        </h4>
                        <div class="space-y-2">
                            <p>• The <span class="highlight">big</span> dog barked loudly.</p>
                            <p>• She wore a <span class="highlight">beautiful</span> dress to the party.</p>
                            <p>• The <span class="highlight">cold</span> water felt refreshing.</p>
                            <p>• We live in a <span class="highlight">small</span> house.</p>
                        </div>
                    </div>

                    <h2>Types of Adjectives</h2>

                    <h3>1. Size Adjectives</h3>
                    <p>These adjectives tell us how big or small something is.</p>
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2 my-4">
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">big</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">small</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">large</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">tiny</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">huge</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">little</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">enormous</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">mini</div>
                    </div>

                    <h3>2. Color Adjectives</h3>
                    <p>These adjectives tell us what color something is.</p>
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2 my-4">
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">red</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">blue</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">green</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">yellow</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">black</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">white</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">purple</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">orange</div>
                    </div>

                    <h3>3. Shape Adjectives</h3>
                    <p>These adjectives describe the shape of things.</p>
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2 my-4">
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">round</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">square</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">long</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">short</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">flat</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">curved</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">straight</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">thick</div>
                    </div>

                    <h3>4. Feeling Adjectives</h3>
                    <p>These adjectives describe how something feels or how we feel.</p>
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2 my-4">
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">happy</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">sad</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">angry</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">excited</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">tired</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">scared</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">proud</div>
                        <div class="bg-gray-100 border border-gray-300 px-3 py-2 rounded-lg text-center text-sm">worried</div>
                    </div>

                    <h2>Where Do Adjectives Go?</h2>
                    <p>
                        In English, adjectives usually come <span class="highlight">before</span> the noun they describe.
                    </p>

                    <div class="example-box p-4 rounded-lg my-6">
                        <h4 class="font-bold text-primary mb-2">
                            <i class="fas fa-check-circle mr-2"></i>Correct Examples
                        </h4>
                        <div class="space-y-2">
                            <p>• The <span class="highlight">red</span> car is fast.</p>
                            <p>• I have a <span class="highlight">new</span> book.</p>
                            <p>• She is a <span class="highlight">kind</span> teacher.</p>
                            <p>• We saw a <span class="highlight">funny</span> movie.</p>
                        </div>
                    </div>

                    <h2>Practice Activity</h2>
                    <p>Try to identify the adjectives in these sentences:</p>

                    <div class="space-y-4 my-6">
                        <div class="bg-gray-50 border border-gray-200 rounded-lg shadow-sm">
                            <div class="p-4">
                                <p class="font-mono text-lg mb-3">1. The brave soldier saved the village.</p>
                                <details class="mt-2">
                                    <summary class="cursor-pointer text-blue-600 font-medium">Show Answer</summary>
                                    <p class="mt-2 text-green-600">Answer: <span class="highlight">brave</span> (describes the soldier)</p>
                                </details>
                            </div>
                        </div>

                        <div class="bg-gray-50 border border-gray-200 rounded-lg shadow-sm">
                            <div class="p-4">
                                <p class="font-mono text-lg mb-3">2. My grandmother baked delicious cookies.</p>
                                <details class="mt-2">
                                    <summary class="cursor-pointer text-blue-600 font-medium">Show Answer</summary>
                                    <p class="mt-2 text-green-600">Answer: <span class="highlight">delicious</span> (describes the cookies)</p>
                                </details>
                            </div>
                        </div>

                        <div class="bg-gray-50 border border-gray-200 rounded-lg shadow-sm">
                            <div class="p-4">
                                <p class="font-mono text-lg mb-3">3. The old tree has green leaves.</p>
                                <details class="mt-2">
                                    <summary class="cursor-pointer text-blue-600 font-medium">Show Answer</summary>
                                    <p class="mt-2 text-green-600">Answer: <span class="highlight">old</span> (describes the tree) and <span class="highlight">green</span> (describes the leaves)</p>
                                </details>
                            </div>
                        </div>
                    </div>

                    <!-- Summary -->
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex items-start">
                            <i class="fas fa-check-circle text-green-600 mr-3 mt-1"></i>
                            <div>
                                <h3 class="font-bold text-green-900 mb-2">Key Points to Remember</h3>
                                <ul class="list-disc list-inside mt-2 space-y-1 text-sm text-green-800">
                                    <li>Adjectives describe nouns</li>
                                    <li>They usually come before the noun</li>
                                    <li>Adjectives can describe size, color, shape, feelings, and more</li>
                                    <li>Using adjectives makes your writing more interesting</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar - Hidden on mobile, visible on desktop -->
            <div class="hidden lg:block lg:col-span-1">
            <!-- Progress -->
            <div class="bg-white shadow-xl rounded-xl mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-bold mb-4">Your Progress</h3>
                    <div class="flex items-center justify-center w-20 h-20 mx-auto mb-4 bg-blue-100 rounded-full">
                        <span class="text-2xl font-bold text-blue-600">60%</span>
                    </div>
                    <p class="text-sm text-gray-600 mb-4 text-center">Keep reading to complete this topic</p>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-lg w-full hover:bg-blue-700 transition-colors" onclick="markAsRead()">
                        <i class="fas fa-check mr-2"></i>
                        Mark as Read
                    </button>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white shadow-xl rounded-xl mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-bold mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="#" class="block w-full text-center border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                            <i class="fas fa-question-circle mr-2"></i>
                            Take Quiz
                        </a>
                        <a href="#" class="block w-full text-center border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                            <i class="fas fa-file-alt mr-2"></i>
                            Take Test
                        </a>
                        <a href="#" class="block w-full text-center border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to Topics
                        </a>
                    </div>
                </div>
            </div>

            <!-- Study Tips -->
            <div class="bg-white shadow-xl rounded-xl">
                <div class="p-6">
                    <h3 class="text-lg font-bold mb-4">
                        <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                        Study Tips
                    </h3>
                    <div class="space-y-3 text-sm">
                        <div class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                            <span>Read each example out loud</span>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                            <span>Try to think of your own examples</span>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                            <span>Practice identifying adjectives in books</span>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                            <span>Take notes of new adjectives you learn</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Action Bar - Only visible on mobile -->
    <div class="lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 z-50">
        <div class="flex space-x-3">
            <button class="flex-1 bg-blue-600 text-white px-4 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors" onclick="markAsRead()">
                <i class="fas fa-check mr-2"></i>
                Mark as Read
            </button>
            <a href="#" class="flex-1 border border-gray-300 text-gray-700 px-4 py-3 rounded-lg text-center font-medium hover:bg-gray-50 transition-colors">
                <i class="fas fa-question-circle mr-2"></i>
                Take Quiz
            </a>
        </div>
    </div>

    <!-- Add bottom padding to prevent content from being hidden behind mobile action bar -->
    <div class="lg:hidden h-20"></div>
</div>

<script>
function markAsRead() {
    // Update progress
    const progressElement = document.querySelector('.radial-progress');
    progressElement.style.setProperty('--value', '100');
    progressElement.textContent = '100%';

    // Update button
    const button = event.target;
    button.innerHTML = '<i class="fas fa-check mr-2"></i>Completed';
    button.classList.remove('btn-primary');
    button.classList.add('btn-success');
    button.disabled = true;

    // Show success message
    const alert = document.createElement('div');
    alert.className = 'alert alert-success mt-4';
    alert.innerHTML = '<i class="fas fa-check-circle"></i><span>Topic marked as read! You can now take the quiz.</span>';
    button.parentNode.appendChild(alert);
}
</script>
{% endblock %}
