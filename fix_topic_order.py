#!/usr/bin/env python3
"""
Fix topic ordering to prevent database constraint violations
"""
import csv
from collections import defaultdict, OrderedDict

def fix_topic_order():
    """Reorganize CSV to ensure proper topic ordering"""
    
    print("🔧 FIXING TOPIC ORDER FOR DATABASE UPLOAD...")
    print("=" * 60)
    
    # Group questions by subject, class_level, and topic
    grouped_data = defaultdict(lambda: defaultdict(list))
    header = None
    
    # Read and group data
    with open('Grade_9_questions_CLEAN.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        header = next(reader)
        
        for row in reader:
            if len(row) >= 3:
                subject = row[0].strip()
                class_level = row[1].strip()
                topic = row[2].strip()
                
                # Standardize topic names
                topic = standardize_topic_name(topic)
                row[2] = topic
                
                key = f"{subject}|{class_level}"
                grouped_data[key][topic].append(row)
    
    # Create ordered output
    output_rows = [header]
    
    for subject_class, topics in sorted(grouped_data.items()):
        subject, class_level = subject_class.split('|', 1)
        
        print(f"\n📚 {subject} - {class_level}:")
        
        # Sort topics alphabetically to ensure consistent ordering
        sorted_topics = sorted(topics.keys())
        
        for i, topic in enumerate(sorted_topics, 1):
            questions = topics[topic]
            print(f"   {i:2d}. {topic} ({len(questions)} questions)")
            
            # Add all questions for this topic
            for question_row in questions:
                output_rows.append(question_row)
    
    # Write the reorganized file
    with open('Grade_9_questions_ORDERED.csv', 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        for row in output_rows:
            writer.writerow(row)
    
    print(f"\n✅ Reorganized file saved as: Grade_9_questions_ORDERED.csv")
    print(f"📊 Total questions: {len(output_rows) - 1}")
    
    # Verify the organization
    verify_organization()

def standardize_topic_name(topic):
    """Standardize topic names to avoid duplicates"""
    
    topic = topic.strip()
    
    # Fix known variations
    standardizations = {
        'Life cycle of a mosquito': 'Life Cycle of a Mosquito',
        'Life Cycle of a Mosquito': 'Life Cycle of a Mosquito',
        'Simultaneous Equation': 'Simultaneous Equations',
        'Simultaneous Equations': 'Simultaneous Equations',
        'Spreadseite Application': 'Spreadsheet Application',
        'Spreadsheet Application': 'Spreadsheet Application',
    }
    
    return standardizations.get(topic, topic)

def verify_organization():
    """Verify the organization is correct"""
    
    print(f"\n🔍 VERIFYING ORGANIZATION...")
    
    topics_by_subject = defaultdict(lambda: defaultdict(int))
    
    with open('Grade_9_questions_ORDERED.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # Skip header
        
        for row in reader:
            if len(row) >= 3:
                subject = row[0].strip()
                class_level = row[1].strip()
                topic = row[2].strip()
                
                key = f"{subject}|{class_level}"
                topics_by_subject[key][topic] += 1
    
    total_subjects = 0
    total_topics = 0
    total_questions = 0
    
    for subject_class, topics in sorted(topics_by_subject.items()):
        subject, class_level = subject_class.split('|', 1)
        total_subjects += 1
        
        unique_topics = len(topics)
        subject_questions = sum(topics.values())
        total_topics += unique_topics
        total_questions += subject_questions
        
        print(f"   📚 {subject}: {unique_topics} topics, {subject_questions} questions")
    
    print(f"\n📊 FINAL SUMMARY:")
    print(f"   📚 Subjects: {total_subjects}")
    print(f"   📋 Unique Topics: {total_topics}")
    print(f"   ❓ Total Questions: {total_questions}")
    print(f"\n🎯 This file should upload without topic order conflicts!")

if __name__ == "__main__":
    fix_topic_order()
