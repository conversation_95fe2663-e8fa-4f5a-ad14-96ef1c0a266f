import csv

total = 0
valid = 0

with open('Grade_9_questions_PERFECT.csv', 'r', encoding='utf-8') as f:
    reader = csv.reader(f)
    next(reader)  # Skip header
    
    for row in reader:
        if len(row) >= 6:
            total += 1
            qt = row[4].strip().lower()
            ca = row[5].strip().lower()
            
            if qt == 'multiple_choice' and ca in ['a','b','c','d']:
                valid += 1
            elif qt == 'true_false' and ca in ['true','false']:
                valid += 1
            elif qt in ['short_answer','fill_blank']:
                valid += 1

print(f'Total questions: {total}')
print(f'Valid answers: {valid}')
print(f'Success rate: {valid/total*100:.1f}%')

if valid == total:
    print('SUCCESS! All answers are properly formatted!')
    print('File ready for upload: Grade_9_questions_PERFECT.csv')
else:
    print(f'WARNING: {total-valid} questions still have issues')
