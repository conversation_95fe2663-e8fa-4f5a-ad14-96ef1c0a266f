subject_name,class_level_name,topic_title,question_text,question_type,correct_answer,explanation,difficulty,points,time_limit,choice_a,choice_b,choice_c,choice_d
ICT,Basic 9,Computer Basics,What does ICT stand for?,multiple_choice,c,ICT stands for Information and Communication Technology.,easy,10,30,Internet Computer Technology,International Computer Technology,Information and Communication Technology,Integrated Computer Technology
ICT,Basic 9,Computer Basics,What is a computer?,multiple_choice,b,A computer is an electronic device that processes data and performs calculations.,easy,10,60,Only a calculator,Electronic device that processes data,Only a gaming machine,Only a typewriter
ICT,Basic 9,Computer Basics,What are the main parts of a computer system?,multiple_choice,d,A computer system consists of hardware software and users.,medium,10,75,Only hardware,Only software,Hardware and software,Hardware software and users
ICT,Basic 9,Computer Basics,What is hardware?,multiple_choice,a,Hardware refers to the physical parts of a computer that you can touch.,easy,10,60,Physical parts you can touch,Computer programs,Internet connections,Data files
ICT,Basic 9,Computer Basics,What is software?,multiple_choice,b,Software consists of programs and instructions that tell the computer what to do.,medium,10,75,Physical computer parts,Programs and instructions for the computer,Internet websites,Computer games only
ICT,Basic 9,Computer Basics,What is the brain of the computer called?,multiple_choice,c,The CPU (Central Processing Unit) is often called the brain of the computer.,medium,10,60,Monitor,Keyboard,CPU,Mouse
ICT,Basic 9,Computer Basics,What does CPU stand for?,multiple_choice,d,CPU stands for Central Processing Unit.,medium,10,45,Computer Personal Unit,Central Program Unit,Computer Processing Unit,Central Processing Unit
ICT,Basic 9,Computer Basics,What is RAM?,multiple_choice,a,RAM (Random Access Memory) is temporary memory that stores data while the computer is running.,hard,15,90,Random Access Memory - temporary storage,Read Access Memory,Rapid Access Memory,Remote Access Memory
ICT,Basic 9,Computer Basics,What is the difference between RAM and ROM?,multiple_choice,b,RAM is temporary memory that loses data when power is off; ROM is permanent memory.,hard,15,120,RAM is permanent; ROM is temporary,RAM is temporary; ROM is permanent,They are the same,RAM is faster; ROM is slower
ICT,Basic 9,Computer Basics,What is an operating system?,multiple_choice,c,An operating system is software that manages computer hardware and other software.,medium,10,90,A computer game,A web browser,Software that manages hardware and other software,A word processor
ICT,Basic 9,Input and Output Devices,What is an input device?,multiple_choice,a,An input device allows users to enter data and commands into the computer.,medium,10,75,Device that allows users to enter data into computer,Device that displays information,Device that stores data,Device that processes data
ICT,Basic 9,Input and Output Devices,Which of these is an input device?,multiple_choice,b,A keyboard is an input device used to type text and commands.,easy,10,45,Monitor,Keyboard,Printer,Speaker
ICT,Basic 9,Input and Output Devices,What is an output device?,multiple_choice,c,An output device displays or presents information from the computer to the user.,medium,10,75,Device that enters data,Device that stores data,Device that displays information from computer,Device that processes data
ICT,Basic 9,Input and Output Devices,Which of these is an output device?,multiple_choice,d,A monitor is an output device that displays visual information.,easy,10,45,Keyboard,Mouse,Microphone,Monitor
ICT,Basic 9,Input and Output Devices,What is a mouse used for?,multiple_choice,a,A mouse is used to point click and navigate on the computer screen.,easy,10,60,Point click and navigate on screen,Type text,Print documents,Store data
ICT,Basic 9,Input and Output Devices,What is a printer?,multiple_choice,b,A printer is an output device that produces hard copies of documents on paper.,medium,10,75,Input device for typing,Output device that prints on paper,Storage device,Processing device
ICT,Basic 9,Input and Output Devices,What is a scanner?,multiple_choice,c,A scanner is an input device that converts physical documents into digital format.,medium,10,90,Output device for printing,Storage device,Input device that converts documents to digital,Processing device
ICT,Basic 9,Input and Output Devices,What is a microphone?,multiple_choice,a,A microphone is an input device that captures sound and voice.,medium,10,60,Input device that captures sound,Output device that plays sound,Storage device,Display device
ICT,Basic 9,Input and Output Devices,What are speakers?,multiple_choice,b,Speakers are output devices that produce sound from the computer.,easy,10,45,Input devices for sound,Output devices that produce sound,Storage devices,Processing devices
ICT,Basic 9,Input and Output Devices,What is a webcam?,multiple_choice,d,A webcam is an input device that captures video and images.,medium,10,60,Output device for video,Storage device,Processing device,Input device that captures video
ICT,Basic 9,Storage Devices,What is storage in computing?,multiple_choice,b,Storage refers to devices and methods used to save data permanently.,medium,10,75,Temporary memory only,Devices and methods to save data permanently,Only internet storage,Only paper storage
ICT,Basic 9,Storage Devices,What is a hard disk drive (HDD)?,multiple_choice,c,A hard disk drive is a storage device that uses magnetic disks to store data permanently.,medium,10,90,Temporary memory,Input device,Storage device using magnetic disks,Output device
ICT,Basic 9,Storage Devices,What is a USB flash drive?,multiple_choice,a,A USB flash drive is a portable storage device that uses flash memory.,medium,10,75,Portable storage device using flash memory,Input device,Output device,Processing device
ICT,Basic 9,Storage Devices,What is a CD?,multiple_choice,d,A CD (Compact Disc) is an optical storage medium that stores data using laser technology.,medium,10,90,Input device,Output device,Processing device,Optical storage medium using laser technology
ICT,Basic 9,Storage Devices,What is the difference between primary and secondary storage?,multiple_choice,b,Primary storage (RAM) is temporary; secondary storage (hard disk) is permanent.,hard,15,120,Primary is permanent; secondary is temporary,Primary is temporary; secondary is permanent,They are the same,Primary is faster; secondary is slower
ICT,Basic 9,Storage Devices,What is cloud storage?,multiple_choice,c,Cloud storage saves data on remote servers accessed through the internet.,hard,15,90,Storage in the sky,Storage on local computer only,Storage on remote servers via internet,Storage on paper
ICT,Basic 9,Storage Devices,What is an SSD?,multiple_choice,a,SSD stands for Solid State Drive - a fast storage device with no moving parts.,hard,15,90,Solid State Drive - fast storage with no moving parts,Super Speed Drive,Standard Storage Drive,Simple Storage Device
ICT,Basic 9,Storage Devices,Why do we need storage devices?,multiple_choice,d,Storage devices are needed to save data permanently for future use.,medium,10,75,Only for games,Only for pictures,Only for documents,To save data permanently for future use
ICT,Basic 9,Storage Devices,What is storage capacity?,multiple_choice,b,Storage capacity is the amount of data a storage device can hold.,medium,10,60,Speed of storage,Amount of data a device can hold,Type of storage,Cost of storage
ICT,Basic 9,Storage Devices,What units are used to measure storage capacity?,multiple_choice,c,Storage is measured in bytes kilobytes megabytes gigabytes and terabytes.,medium,10,90,Only gigabytes,Only megabytes,Bytes kilobytes megabytes gigabytes terabytes,Only terabytes
ICT,Basic 9,Internet and World Wide Web,What is the Internet?,multiple_choice,a,The Internet is a global network of interconnected computers that share information.,medium,10,90,Global network of interconnected computers,Only social media,Only email,Only websites
ICT,Basic 9,Internet and World Wide Web,What is the World Wide Web (WWW)?,multiple_choice,b,The WWW is a system of linked documents and resources accessed through the Internet.,medium,10,120,The same as Internet,System of linked documents accessed through Internet,Only search engines,Only online shopping
ICT,Basic 9,Internet and World Wide Web,What is a web browser?,multiple_choice,c,A web browser is software used to access and view websites on the Internet.,medium,10,75,A search engine,A website,Software to access and view websites,An email program
ICT,Basic 9,Internet and World Wide Web,What is a website?,multiple_choice,d,A website is a collection of related web pages with information about a topic.,easy,10,60,A single web page,Only pictures,Only text,Collection of related web pages
ICT,Basic 9,Internet and World Wide Web,What is a URL?,multiple_choice,a,URL stands for Uniform Resource Locator - the address of a website.,medium,10,75,Uniform Resource Locator - website address,Universal Reading Language,United Resource Link,Unique Reference Location
ICT,Basic 9,Internet and World Wide Web,What is a search engine?,multiple_choice,b,A search engine is a tool that helps find information on the Internet.,medium,10,75,A web browser,Tool that helps find information on Internet,A website,An email service
ICT,Basic 9,Internet and World Wide Web,What is email?,multiple_choice,c,Email (electronic mail) is a method of sending messages over the Internet.,easy,10,60,Only text messages,Only voice messages,Electronic mail - sending messages over Internet,Only video messages
ICT,Basic 9,Internet and World Wide Web,What is social media?,multiple_choice,d,Social media are online platforms where people connect share and communicate.,medium,10,90,Only Facebook,Only Twitter,Only Instagram,Online platforms for connecting sharing and communicating
ICT,Basic 9,Internet and World Wide Web,What is online safety?,multiple_choice,a,Online safety involves protecting yourself from dangers and risks on the Internet.,medium,10,120,Protecting yourself from Internet dangers and risks,Only using antivirus,Only avoiding websites,Only using passwords
ICT,Basic 9,Internet and World Wide Web,What is cyberbullying?,multiple_choice,b,Cyberbullying is using technology to harass intimidate or harm others online.,hard,15,120,Only computer viruses,Using technology to harass or harm others online,Only spam emails,Only slow internet
ICT,Basic 9,Software Applications,What is application software?,multiple_choice,c,Application software consists of programs designed to help users accomplish specific tasks.,medium,10,120,Operating system only,Hardware components,Programs designed to help users accomplish tasks,Internet connections
ICT,Basic 9,Software Applications,What is a word processor?,multiple_choice,a,A word processor is software used to create edit and format text documents.,medium,10,90,Software to create and edit text documents,Only for printing,Only for internet,Only for games
ICT,Basic 9,Software Applications,What is a spreadsheet?,multiple_choice,b,A spreadsheet is software used to organize calculate and analyze data in rows and columns.,medium,10,120,Only for writing,Software to organize and calculate data in rows and columns,Only for pictures,Only for internet
ICT,Basic 9,Software Applications,What is presentation software?,multiple_choice,d,Presentation software is used to create slideshows for displaying information.,medium,10,90,Only for writing,Only for calculations,Only for internet,Software to create slideshows
ICT,Basic 9,Software Applications,What is a database?,multiple_choice,c,A database is an organized collection of data that can be easily accessed and managed.,hard,15,120,Only text files,Only spreadsheets,Organized collection of data for easy access and management,Only pictures
ICT,Basic 9,Software Applications,What is antivirus software?,multiple_choice,a,Antivirus software protects computers from viruses and other malicious programs.,medium,10,120,Software that protects from viruses and malicious programs,Only for internet,Only for games,Only for documents
ICT,Basic 9,Software Applications,What is a computer virus?,multiple_choice,b,A computer virus is malicious software that can damage or disrupt computer systems.,hard,15,90,Helpful software,Malicious software that damages or disrupts systems,Only internet problems,Only hardware problems
ICT,Basic 9,Software Applications,What is system software?,multiple_choice,d,System software manages computer hardware and provides platform for other software.,hard,15,120,Only games,Only internet browsers,Only word processors,Software that manages hardware and provides platform
ICT,Basic 9,Software Applications,What is the difference between system and application software?,multiple_choice,c,System software manages the computer; application software helps users do specific tasks.,hard,15,150,They are the same,Application manages; system helps users,System manages computer; application helps users do tasks,System is newer; application is older
ICT,Basic 9,Software Applications,What is software installation?,multiple_choice,a,Software installation is the process of setting up software on a computer.,medium,10,75,Process of setting up software on computer,Only downloading software,Only buying software,Only using software
ICT,Basic 9,Digital Literacy,What is digital literacy?,multiple_choice,b,Digital literacy is the ability to use technology effectively and responsibly.,medium,10,120,Only typing skills,Ability to use technology effectively and responsibly,Only internet skills,Only gaming skills
ICT,Basic 9,Digital Literacy,What is a digital citizen?,multiple_choice,c,A digital citizen uses technology responsibly ethically and safely.,medium,10,90,Someone who only uses computers,Someone who lives online,Someone who uses technology responsibly ethically safely,Someone who avoids technology
ICT,Basic 9,Digital Literacy,What is digital footprint?,multiple_choice,d,Digital footprint is the trail of data you leave when using digital devices and internet.,hard,15,120,Only website visits,Only social media posts,Only emails sent,Trail of data left when using digital devices and internet
ICT,Basic 9,Digital Literacy,Why is password security important?,multiple_choice,a,Strong passwords protect personal information and accounts from unauthorized access.,medium,10,120,Protects information and accounts from unauthorized access,Only for email,Only for social media,Only for banking
ICT,Basic 9,Digital Literacy,What makes a strong password?,multiple_choice,b,A strong password is long complex and includes letters numbers and symbols.,medium,10,120,Only letters,Long complex with letters numbers and symbols,Only numbers,Only symbols
ICT,Basic 9,Digital Literacy,What is phishing?,multiple_choice,c,Phishing is attempting to steal personal information through fake emails or websites.,hard,15,120,Only computer viruses,Only spam emails,Attempting to steal information through fake emails/websites,Only slow internet
ICT,Basic 9,Digital Literacy,What is intellectual property?,multiple_choice,d,Intellectual property refers to creations of the mind like inventions artistic works and designs.,hard,15,150,Only physical objects,Only money,Only land,Creations of the mind like inventions and artistic works
ICT,Basic 9,Digital Literacy,What is copyright?,multiple_choice,a,Copyright is legal protection for original creative works like books music and software.,hard,15,120,Legal protection for original creative works,Only for books,Only for music,Only for software
ICT,Basic 9,Digital Literacy,Why should you respect others online?,multiple_choice,b,Respecting others online creates a positive safe environment for everyone.,medium,10,90,Only to avoid trouble,Creates positive safe environment for everyone,Only because it's required,Only for social media
ICT,Basic 9,Digital Literacy,What is netiquette?,multiple_choice,c,Netiquette is proper behavior and manners when communicating online.,medium,10,75,Only email rules,Only social media rules,Proper behavior and manners when communicating online,Only website rules
ICT,Basic 9,Programming Concepts,What is programming?,multiple_choice,a,Programming is writing instructions for computers to follow to solve problems.,medium,10,90,Writing instructions for computers to solve problems,Only playing games,Only using software,Only browsing internet
ICT,Basic 9,Programming Concepts,What is a program?,multiple_choice,b,A program is a set of instructions that tells a computer what to do.,medium,10,75,Only a game,Set of instructions telling computer what to do,Only a website,Only an app
ICT,Basic 9,Programming Concepts,What is an algorithm?,multiple_choice,c,An algorithm is a step-by-step procedure for solving a problem.,hard,15,90,Only computer code,Only a program,Step-by-step procedure for solving a problem,Only a website
ICT,Basic 9,Programming Concepts,What is a programming language?,multiple_choice,d,A programming language is a formal language used to write instructions for computers.,hard,15,120,Only English,Only numbers,Only symbols,Formal language for writing computer instructions
ICT,Basic 9,Programming Concepts,What is debugging?,multiple_choice,a,Debugging is finding and fixing errors in computer programs.,hard,15,90,Finding and fixing errors in programs,Only testing programs,Only writing programs,Only running programs
ICT,Basic 9,Programming Concepts,What is a loop in programming?,multiple_choice,b,A loop is a programming structure that repeats a set of instructions.,hard,15,120,Only a circle,Programming structure that repeats instructions,Only a mistake,Only a variable
ICT,Basic 9,Programming Concepts,What is a variable in programming?,multiple_choice,c,A variable is a storage location with a name that holds data.,hard,15,90,Only a number,Only text,Storage location with name that holds data,Only a program
ICT,Basic 9,Programming Concepts,What is input in programming?,multiple_choice,d,Input is data that is entered into a program from external sources.,medium,10,75,Only keyboard typing,Only mouse clicks,Only screen display,Data entered into program from external sources
ICT,Basic 9,Programming Concepts,What is output in programming?,multiple_choice,a,Output is data that a program sends out or displays to users.,medium,10,75,Data program sends out or displays to users,Only input data,Only program code,Only errors
ICT,Basic 9,Programming Concepts,Why is programming important?,multiple_choice,b,Programming helps solve problems automate tasks and create useful applications.,medium,10,120,Only for games,Helps solve problems automate tasks create applications,Only for websites,Only for fun
ICT,Basic 9,Data and Information,What is data?,multiple_choice,c,Data consists of raw facts and figures that have not been processed.,medium,10,75,Only numbers,Only text,Raw facts and figures not yet processed,Only pictures
ICT,Basic 9,Data and Information,What is information?,multiple_choice,a,Information is data that has been processed organized and given meaning.,medium,10,90,Data that has been processed and given meaning,Only raw data,Only numbers,Only text
ICT,Basic 9,Data and Information,What is the difference between data and information?,multiple_choice,b,Data is raw facts; information is processed data with meaning.,medium,10,120,They are the same,Data is raw facts; information is processed with meaning,Data is processed; information is raw,Data is newer; information is older
ICT,Basic 9,Data and Information,What is data processing?,multiple_choice,d,Data processing is converting raw data into useful information.,medium,10,90,Only storing data,Only collecting data,Only displaying data,Converting raw data into useful information
ICT,Basic 9,Data and Information,What are the types of data?,multiple_choice,c,Data types include text numbers images audio and video.,medium,10,90,Only text,Only numbers,Text numbers images audio and video,Only images
ICT,Basic 9,Data and Information,What is a database management system?,multiple_choice,a,A database management system is software that helps organize store and retrieve data.,hard,15,120,Software that helps organize store and retrieve data,Only a database,Only data,Only information
ICT,Basic 9,Data and Information,Why is data important?,multiple_choice,b,Data helps make informed decisions solve problems and understand patterns.,medium,10,120,Only for storage,Helps make decisions solve problems understand patterns,Only for computers,Only for internet
ICT,Basic 9,Data and Information,What is data security?,multiple_choice,c,Data security involves protecting data from unauthorized access damage or theft.,hard,15,120,Only passwords,Only antivirus,Protecting data from unauthorized access damage or theft,Only backups
ICT,Basic 9,Data and Information,What is a backup?,multiple_choice,d,A backup is a copy of data stored separately for protection against loss.,medium,10,90,Only original data,Only deleted data,Only new data,Copy of data stored separately for protection
ICT,Basic 9,Data and Information,Why should we backup data?,multiple_choice,a,Backing up data protects against loss due to hardware failure viruses or accidents.,medium,10,120,Protects against loss due to failure viruses or accidents,Only to save space,Only to organize data,Only to share data
