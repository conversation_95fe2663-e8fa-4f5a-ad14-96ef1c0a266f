# Generated by Django 5.2.1 on 2025-07-05 14:54

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Subject',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, unique=True)),
                ('slug', models.SlugField(blank=True, max_length=120, null=True, unique=True)),
                ('description', models.TextField(blank=True)),
                ('icon', models.CharField(blank=True, help_text='CSS icon class or emoji', max_length=50)),
                ('color', models.CharField(default='#3B82F6', help_text='Hex color code', max_length=7)),
                ('is_active', models.Boolean<PERSON>ield(default=True)),
                ('order', models.PositiveIntegerField(default=0, help_text='Display order')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Subject',
                'verbose_name_plural': 'Subjects',
                'db_table': 'subjects',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ClassLevel',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=50)),
                ('level_number', models.PositiveIntegerField()),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('pass_percentage', models.PositiveIntegerField(default=60, help_text='Minimum percentage to pass this level')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('prerequisite_level', models.ForeignKey(blank=True, help_text='Previous level required to unlock this level', null=True, on_delete=django.db.models.deletion.SET_NULL, to='subjects.classlevel')),
                ('subject', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='classlevels', to='subjects.subject')),
            ],
            options={
                'verbose_name': 'Class Level',
                'verbose_name_plural': 'Class Levels',
                'db_table': 'class_levels',
                'ordering': ['subject', 'level_number'],
                'unique_together': {('subject', 'level_number')},
            },
        ),
        migrations.CreateModel(
            name='Topic',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('slug', models.SlugField(blank=True, max_length=220)),
                ('description', models.TextField(blank=True)),
                ('order', models.PositiveIntegerField(default=0)),
                ('estimated_duration', models.PositiveIntegerField(default=30, help_text='Estimated study time in minutes')),
                ('is_active', models.BooleanField(default=True)),
                ('difficulty_level', models.CharField(choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced')], default='beginner', max_length=20)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('class_level', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='topics', to='subjects.classlevel')),
            ],
            options={
                'verbose_name': 'Topic',
                'verbose_name_plural': 'Topics',
                'db_table': 'topics',
                'ordering': ['class_level', 'order'],
                'unique_together': {('class_level', 'order')},
            },
        ),
    ]
