{% extends 'base.html' %}

{% block title %}Pentora Ghana | Official #1 Online Education Platform | Trusted by Thousands{% endblock %}
{% block description %}Pentora Ghana - The official #1 online education platform in Ghana. Trusted by thousands of students for BECE preparation, WASSCE preparation, free quality education, and comprehensive learning materials. Join Ghana's leading educational community.{% endblock %}
{% block keywords %}Pentora Ghana, Pentora education, Pentora official, official Pentora, Pentora learning platform, Pentora online school, #1 education Ghana, Ghana's best education platform, top education platform Ghana, Pentora.edu.gh, Mentora learn, Mentora education, online learning Ghana, free education Ghana, BECE preparation Ghana, WASSCE preparation Ghana, Ghana education platform, trusted education Ghana, verified education platform Ghana, authentic Pentora, genuine Pentora{% endblock %}

{% block og_title %}Pentora Ghana | Official #1 Online Education Platform{% endblock %}
{% block og_description %}Pentora Ghana - The official #1 online education platform in Ghana. Trusted by thousands of students for BECE preparation, WASSCE preparation, and comprehensive learning materials.{% endblock %}

{% block extra_js %}
<!-- Structured Data for SEO -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "EducationalOrganization",
  "name": "Pentora Ghana",
  "alternateName": ["Pentora", "Pentora Education", "Mentora", "Mentora Learn", "Official Pentora"],
  "description": "Ghana's official #1 online education platform providing free quality education, BECE preparation, WASSCE preparation, and comprehensive learning materials. Trusted by thousands of students across Ghana.",
  "url": "{{ request.build_absolute_uri }}",
  "logo": "{{ request.scheme }}://{{ request.get_host }}{% load static %}{% static 'images/Pentora-logo.png' %}",
  "sameAs": [
    "https://www.facebook.com/Pentora",
    "https://www.twitter.com/Pentora",
    "https://www.instagram.com/Pentora"
  ],
  "address": {
    "@type": "PostalAddress",
    "addressCountry": "Global"
  },
  "contactPoint": {
    "@type": "ContactPoint",
    "contactType": "customer service",
    "url": "{{ request.scheme }}://{{ request.get_host }}{% url 'core:contact' %}"
  },
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "USD",
    "description": "Free access to all educational content and features"
  },
  "educationalCredentialAwarded": "Grade completion certificates",
  "hasCredential": {
    "@type": "EducationalOccupationalCredential",
    "name": "Grade Level Completion",
    "description": "Completion certificates for each grade level"
  }
}
</script>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "Pentora Learning Platform",
  "url": "{{ request.build_absolute_uri }}",
  "description": "Free online learning platform for Grades 1-12",
  "potentialAction": {
    "@type": "SearchAction",
    "target": {
      "@type": "EntryPoint",
      "urlTemplate": "{{ request.scheme }}://{{ request.get_host }}/search?q={search_term_string}"
    },
    "query-input": "required name=search_term_string"
  }
}
</script>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Course",
  "name": "Comprehensive K-12 Education",
  "description": "Complete educational curriculum covering Grades 1-12 with subjects including English, Mathematics, Science, Social Studies, ICT, and Life Skills",
  "provider": {
    "@type": "EducationalOrganization",
    "name": "Pentora Learning Platform"
  },
  "educationalLevel": "K-12",
  "teaches": [
    "English Language",
    "Mathematics",
    "Science",
    "Social Studies",
    "Information and Communication Technology",
    "Life Skills"
  ],
  "courseMode": "online",
  "isAccessibleForFree": true,
  "inLanguage": "en",
  "availableLanguage": "en"
}
</script>
{% endblock %}

{% block extra_css %}
<style>
    .hero-gradient {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-purple) 100%);
    }

    .cta-gradient {
        background: linear-gradient(135deg, #059669 0%, #10b981 50%, #34d399 100%);
        position: relative;
    }

    .cta-gradient::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
        opacity: 0.3;
    }

    .feature-card {
        background: white;
        border-radius: 1.5rem;
        padding: 2rem;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
        height: 100%;
        position: relative;
        overflow: hidden;
    }

    .feature-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.15);
        border-color: #3b82f6;
    }

    /* Mobile-specific feature card adjustments */
    @media (max-width: 768px) {
        .feature-card {
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .feature-card:hover {
            transform: translateY(-4px);
        }
    }

    .feature-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 2rem;
        color: white;
    }

    .btn-cta {
        background: linear-gradient(135deg, var(--success-green), #16A34A);
        border: none;
        border-radius: 0.75rem;
        padding: 0.875rem 1.5rem;
        font-weight: 600;
        font-size: 1rem;
        color: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 160px;
        white-space: nowrap;
    }

    .btn-cta:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 30px -5px rgba(34, 197, 94, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-secondary {
        background: transparent;
        border: 2px solid white;
        border-radius: 0.75rem;
        padding: 0.875rem 1.5rem;
        font-weight: 600;
        font-size: 1rem;
        color: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 160px;
        white-space: nowrap;
    }

    .btn-secondary:hover {
        background: white;
        color: var(--primary-blue);
        text-decoration: none;
        transform: translateY(-2px);
    }

    /* Mobile-specific button adjustments */
    @media (max-width: 640px) {
        .btn-cta, .btn-secondary {
            width: 100%;
            max-width: 280px;
            margin: 0 auto;
            padding: 1rem 1.5rem;
            font-size: 1rem;
        }
    }

    .stats-card {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 1rem;
        padding: 1.5rem;
        text-align: center;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .floating-element {
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }

    /* Remove inline styles for better SEO */
    .floating-delay-2s {
        animation-delay: -2s;
    }

    .floating-delay-4s {
        animation-delay: -4s;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="relative min-h-screen flex items-center overflow-hidden">
    <!-- Background Image or Gradient -->
    {% load site_images %}
    {% get_hero_banner as custom_hero %}
    {% if custom_hero %}
        <div class="absolute inset-0 z-0">
            <img src="{{ custom_hero }}" alt="Hero Background" class="w-full h-full object-cover">
            <div class="absolute inset-0 bg-gradient-to-r from-blue-900/80 to-purple-900/80"></div>
        </div>
    {% elif hero_section and hero_section.hero_image %}
        <div class="absolute inset-0 z-0">
            <img src="{{ hero_section.hero_image.url }}" alt="Hero Background" class="w-full h-full object-cover">
            <div class="absolute inset-0 bg-gradient-to-r from-blue-900/80 to-purple-900/80"></div>
        </div>
    {% else %}
        <div class="absolute inset-0 z-0">
            <img src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80" alt="Students Learning" class="w-full h-full object-cover">
            <div class="absolute inset-0 bg-gradient-to-r from-blue-900/80 to-purple-900/80"></div>
        </div>
    {% endif %}

    <!-- Floating Elements -->
    <div class="absolute top-20 left-10 floating-element opacity-20 hidden lg:block">
        <i class="fas fa-graduation-cap text-white text-6xl"></i>
    </div>
    <div class="absolute top-40 right-20 floating-element floating-delay-2s opacity-20 hidden lg:block">
        <i class="fas fa-book text-white text-4xl"></i>
    </div>
    <div class="absolute bottom-40 left-20 floating-element floating-delay-4s opacity-20 hidden lg:block">
        <i class="fas fa-lightbulb text-white text-5xl"></i>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <div class="max-w-4xl mx-auto text-center text-white">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                {% if hero_section %}{{ hero_section.title }}{% else %}An Easy Way to Learn and Grow{% endif %}
            </h1>
            <p class="text-xl md:text-2xl mb-4 opacity-90">
                {% if hero_section %}{{ hero_section.subtitle }}{% else %}No matter your background{% endif %}
            </p>
            <p class="text-lg md:text-xl mb-12 opacity-80 max-w-2xl mx-auto">
                {% if hero_section %}{{ hero_section.description }}{% else %}Quality education for everyone. Start your learning journey today with our comprehensive, mobile-friendly platform designed for learners worldwide.{% endif %}
            </p>

            <div class="flex flex-col sm:flex-row gap-3 justify-center mb-12">
                {% if user.is_authenticated %}
                    <a href="{% url 'core:dashboard' %}" class="btn-cta text-sm px-6 py-3">
                        <i class="fas fa-tachometer-alt mr-2"></i>
                        Continue Learning
                    </a>
                {% else %}
                    <a href="{% url 'users:register' %}" class="btn-cta text-sm px-6 py-3">
                        <i class="fas fa-rocket mr-2"></i>
                        {% if hero_section %}{{ hero_section.cta_text }}{% else %}Get Started Free{% endif %}
                    </a>
                    <a href="{% url 'users:login' %}" class="btn-secondary text-sm px-6 py-3">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Sign In
                    </a>
                {% endif %}
            </div>

            <!-- Stats - Mobile Optimized -->
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-2xl mx-auto">
                {% if site_statistics %}
                    {% for stat in site_statistics %}
                        <div class="stats-card">
                            <div class="text-2xl md:text-3xl font-bold mb-1">
                                {% if stat.icon %}
                                    <i class="{{ stat.icon }}"></i>
                                {% else %}
                                    {{ stat.value }}
                                {% endif %}
                            </div>
                            <div class="text-sm md:text-base opacity-80">{{ stat.label }}</div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="stats-card hidden sm:block">
                        <div class="text-3xl md:text-4xl font-bold mb-2"><i class="fas fa-heart text-red-400"></i></div>
                        <div class="text-sm md:text-base opacity-80 font-medium">Free Forever</div>
                    </div>
                    <div class="stats-card hidden sm:block">
                        <div class="text-3xl md:text-4xl font-bold mb-2"><i class="fas fa-mobile-alt text-green-400"></i></div>
                        <div class="text-sm md:text-base opacity-80 font-medium">Mobile Friendly</div>
                    </div>
                    <div class="stats-card hidden sm:block">
                        <div class="text-3xl md:text-4xl font-bold mb-2"><i class="fas fa-globe text-blue-400"></i></div>
                        <div class="text-sm md:text-base opacity-80 font-medium">Global Access</div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Why Choose Pentora?</h2>
            <p class="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto">
                Comprehensive education platform designed for learners worldwide with mobile-first accessibility
            </p>
        </div>

        <!-- Mobile-First Feature Layout -->
        <div class="space-y-8 md:space-y-0 md:grid md:grid-cols-3 md:gap-8 max-w-6xl mx-auto">
            <!-- Feature 1 -->
            <div class="feature-card">
                <div class="feature-icon mb-4" style="background: linear-gradient(135deg, #3B82F6, #1D4ED8);">
                    <i class="fas fa-graduation-cap text-2xl"></i>
                </div>
                <h3 class="text-xl md:text-2xl font-bold text-gray-800 mb-4 text-center">Quality Education</h3>
                <p class="text-gray-600 text-center mb-6 text-base md:text-lg leading-relaxed">
                    International curriculum covering English, Mathematics, Science, and more.
                    Professional content designed for students worldwide.
                </p>
                <!-- Horizontal layout for mobile -->
                <div class="flex flex-wrap gap-2 justify-center md:block md:space-y-2">
                    <span class="inline-block bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm font-medium">✓ K-12 Standards</span>
                    <span class="inline-block bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm font-medium">✓ Progressive Levels</span>
                    <span class="inline-block bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm font-medium">✓ Interactive Materials</span>
                </div>
            </div>

            <!-- Feature 2 -->
            <div class="feature-card">
                <div class="feature-icon mb-4" style="background: linear-gradient(135deg, #22C55E, #16A34A);">
                    <i class="fas fa-mobile-alt text-2xl"></i>
                </div>
                <h3 class="text-xl md:text-2xl font-bold text-gray-800 mb-4 text-center">Learn Anywhere</h3>
                <p class="text-gray-600 text-center mb-6 text-base md:text-lg leading-relaxed">
                    Mobile-first design that works on any device. Optimized for smartphones
                    with clean, accessible interfaces.
                </p>
                <!-- Horizontal layout for mobile -->
                <div class="flex flex-wrap gap-2 justify-center md:block md:space-y-2">
                    <span class="inline-block bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm font-medium">✓ Any Smartphone</span>
                    <span class="inline-block bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm font-medium">✓ Fast Loading</span>
                    <span class="inline-block bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm font-medium">✓ Touch Optimized</span>
                </div>
            </div>

            <!-- Feature 3 -->
            <div class="feature-card">
                <div class="feature-icon mb-4" style="background: linear-gradient(135deg, #EF4444, #DC2626);">
                    <i class="fas fa-heart text-2xl"></i>
                </div>
                <h3 class="text-xl md:text-2xl font-bold text-gray-800 mb-4 text-center">Completely Free</h3>
                <p class="text-gray-600 text-center mb-6 text-base md:text-lg leading-relaxed">
                    No hidden costs, no subscriptions, no premium features.
                    Quality education should be accessible to everyone.
                </p>
                <!-- Horizontal layout for mobile -->
                <div class="flex flex-wrap gap-2 justify-center md:block md:space-y-2">
                    <span class="inline-block bg-red-50 text-red-700 px-3 py-1 rounded-full text-sm font-medium">✓ No Registration Fees</span>
                    <span class="inline-block bg-red-50 text-red-700 px-3 py-1 rounded-full text-sm font-medium">✓ No Monthly Charges</span>
                    <span class="inline-block bg-red-50 text-red-700 px-3 py-1 rounded-full text-sm font-medium">✓ All Features Included</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section class="py-16 md:py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12 md:mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">How Pentora Works</h2>
            <p class="text-lg md:text-xl text-gray-600">Simple steps to start your learning journey</p>
        </div>

        <div class="max-w-4xl mx-auto">
            <!-- Mobile: Vertical layout, Desktop: Horizontal -->
            <div class="space-y-6 md:space-y-0 md:grid md:grid-cols-2 lg:grid-cols-4 md:gap-6 lg:gap-8">
                <div class="text-center bg-gray-50 p-6 rounded-xl">
                    <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4 shadow-lg">1</div>
                    <h3 class="text-lg md:text-xl font-bold text-gray-800 mb-3">Sign Up Free</h3>
                    <p class="text-base text-gray-600 leading-relaxed">Create your account in minutes with just your email</p>
                </div>
                <div class="text-center bg-gray-50 p-6 rounded-xl">
                    <div class="w-20 h-20 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4 shadow-lg">2</div>
                    <h3 class="text-lg md:text-xl font-bold text-gray-800 mb-3">Choose Your Grade</h3>
                    <p class="text-base text-gray-600 leading-relaxed">Select your grade level and subjects to study</p>
                </div>
                <div class="text-center bg-gray-50 p-6 rounded-xl">
                    <div class="w-20 h-20 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4 shadow-lg">3</div>
                    <h3 class="text-lg md:text-xl font-bold text-gray-800 mb-3">Start Learning</h3>
                    <p class="text-base text-gray-600 leading-relaxed">Study notes, take quizzes, and learn at your pace</p>
                </div>
                <div class="text-center bg-gray-50 p-6 rounded-xl">
                    <div class="w-20 h-20 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4 shadow-lg">4</div>
                    <h3 class="text-lg md:text-xl font-bold text-gray-800 mb-3">Track Progress</h3>
                    <p class="text-base text-gray-600 leading-relaxed">Monitor your improvement and advance to next grade</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Subjects Overview Section -->
<section class="py-16 md:py-20 bg-gradient-to-br from-gray-50 to-blue-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Comprehensive K-12 Curriculum</h2>
            <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
                Our educational platform covers all essential subjects for Grades 1-12, providing structured learning paths
                that align with international educational standards. Each subject includes detailed study notes, interactive
                quizzes, and progress tracking to ensure effective learning outcomes.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
            <!-- English Language -->
            <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-book-open text-blue-600 text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800">English Language</h3>
                </div>
                <p class="text-gray-600 mb-4">
                    Develop reading comprehension, writing skills, grammar, and vocabulary through engaging lessons
                    and interactive exercises designed for all grade levels.
                </p>
                <a href="{% url 'subjects:list' %}" class="text-blue-600 hover:text-blue-800 font-semibold inline-flex items-center">
                    Explore English Courses <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>

            <!-- Mathematics -->
            <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-calculator text-green-600 text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800">Mathematics</h3>
                </div>
                <p class="text-gray-600 mb-4">
                    Master mathematical concepts from basic arithmetic to advanced algebra and geometry.
                    Step-by-step explanations make complex topics easy to understand.
                </p>
                <a href="{% url 'subjects:list' %}" class="text-green-600 hover:text-green-800 font-semibold inline-flex items-center">
                    Start Math Learning <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>

            <!-- Science -->
            <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-microscope text-purple-600 text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800">Science</h3>
                </div>
                <p class="text-gray-600 mb-4">
                    Explore the wonders of biology, chemistry, and physics through interactive content
                    that brings scientific concepts to life with real-world applications.
                </p>
                <a href="{% url 'subjects:list' %}" class="text-purple-600 hover:text-purple-800 font-semibold inline-flex items-center">
                    Discover Science <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>

            <!-- Social Studies -->
            <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-globe text-orange-600 text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800">Social Studies</h3>
                </div>
                <p class="text-gray-600 mb-4">
                    Learn about history, geography, civics, and cultural studies to develop a comprehensive
                    understanding of the world and society around us.
                </p>
                <a href="{% url 'subjects:list' %}" class="text-orange-600 hover:text-orange-800 font-semibold inline-flex items-center">
                    Study Social Sciences <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>

            <!-- ICT -->
            <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-laptop text-indigo-600 text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800">ICT</h3>
                </div>
                <p class="text-gray-600 mb-4">
                    Build essential digital literacy skills including computer basics, internet safety,
                    and technology applications for modern learning and communication.
                </p>
                <a href="{% url 'subjects:list' %}" class="text-indigo-600 hover:text-indigo-800 font-semibold inline-flex items-center">
                    Learn Technology <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>

            <!-- Life Skills -->
            <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-heart text-red-600 text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800">Life Skills</h3>
                </div>
                <p class="text-gray-600 mb-4">
                    Develop practical life skills including personal development, health education,
                    financial literacy, and social skills for success in daily life.
                </p>
                <a href="{% url 'subjects:list' %}" class="text-red-600 hover:text-red-800 font-semibold inline-flex items-center">
                    Build Life Skills <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>
        </div>

        <!-- Additional Learning Resources -->
        <div class="mt-16 text-center">
            <h3 class="text-2xl font-bold text-gray-800 mb-6">Additional Learning Resources</h3>
            <div class="flex flex-wrap justify-center gap-4 max-w-4xl mx-auto">
                <a href="{% url 'subjects:quiz_list' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors inline-flex items-center">
                    <i class="fas fa-question-circle mr-2"></i>
                    Practice Quizzes
                </a>
                <a href="{% url 'subjects:simple_learn' %}" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors inline-flex items-center">
                    <i class="fas fa-book-reader mr-2"></i>
                    Study Notes
                </a>
                <a href="{% url 'core:help' %}" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors inline-flex items-center">
                    <i class="fas fa-question mr-2"></i>
                    Learning Guide
                </a>
                <a href="{% url 'core:about' %}" class="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors inline-flex items-center">
                    <i class="fas fa-info-circle mr-2"></i>
                    About Pentora
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Educational Excellence Section -->
<section class="py-16 md:py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-8">Why Pentora Stands Out in Online Education</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                <div class="text-left">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Comprehensive Curriculum Design</h3>
                    <p class="text-gray-600 leading-relaxed mb-4">
                        Our educational platform follows international standards for K-12 education, ensuring students receive
                        quality instruction across all core subjects. Each lesson is carefully crafted by education professionals
                        to provide clear explanations, practical examples, and engaging activities that promote deep understanding.
                    </p>
                    <p class="text-gray-600 leading-relaxed">
                        From foundational concepts in early grades to advanced topics in higher levels, our curriculum builds
                        progressively to ensure students develop strong academic foundations while maintaining engagement through
                        interactive learning experiences.
                    </p>
                </div>

                <div class="text-left">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Accessibility and Inclusion</h3>
                    <p class="text-gray-600 leading-relaxed mb-4">
                        Pentora was specifically designed with underprivileged learners in mind, recognizing that quality education
                        should be accessible to everyone regardless of economic background. Our mobile-first approach ensures that
                        students can learn effectively using basic smartphones with limited internet connectivity.
                    </p>
                    <p class="text-gray-600 leading-relaxed">
                        We believe that every student deserves access to excellent educational resources. Our platform removes
                        financial barriers while maintaining high educational standards, making it possible for students worldwide
                        to pursue their academic goals and build brighter futures.
                    </p>
                </div>
            </div>

            <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-4">Interactive Learning Experience</h3>
                <p class="text-gray-600 leading-relaxed mb-6">
                    Our platform combines traditional study materials with modern interactive elements to create an engaging
                    learning environment. Students can access comprehensive study notes, take practice quizzes with immediate
                    feedback, and track their progress through detailed analytics. The gamified approach to learning helps
                    maintain motivation while ensuring educational objectives are met.
                </p>
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 text-center">
                    <div class="bg-white rounded-lg p-4 shadow-sm">
                        <div class="text-2xl font-bold text-blue-600 mb-2">15+</div>
                        <div class="text-sm text-gray-600">Questions per Topic</div>
                    </div>
                    <div class="bg-white rounded-lg p-4 shadow-sm">
                        <div class="text-2xl font-bold text-green-600 mb-2">6</div>
                        <div class="text-sm text-gray-600">Core Subjects</div>
                    </div>
                    <div class="bg-white rounded-lg p-4 shadow-sm">
                        <div class="text-2xl font-bold text-purple-600 mb-2">12</div>
                        <div class="text-sm text-gray-600">Grade Levels</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-12 sm:py-16 md:py-20 cta-gradient relative overflow-hidden">
    <!-- Decorative elements -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute top-10 right-10 w-32 h-32 bg-white rounded-full blur-3xl"></div>
        <div class="absolute bottom-10 left-10 w-24 h-24 bg-white rounded-full blur-2xl"></div>
    </div>

    <div class="container mx-auto px-4 sm:px-6 text-center text-white relative z-10">
        <div class="max-w-4xl mx-auto">
            <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 sm:mb-6 leading-tight">
                Ready to Start Your Learning Journey?
            </h2>
            <p class="text-base sm:text-lg md:text-xl mb-6 sm:mb-8 opacity-90 max-w-2xl mx-auto leading-relaxed">
                Join thousands of learners worldwide who are building their future through quality education.
                Start with any grade level and progress at your own pace with our comprehensive curriculum.
            </p>
            {% if not user.is_authenticated %}
                <div class="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center max-w-md sm:max-w-none mx-auto">
                    <a href="{% url 'users:register' %}" class="bg-white text-green-600 hover:text-green-700 font-semibold px-6 sm:px-8 py-3 sm:py-4 rounded-xl hover:bg-gray-50 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                        <i class="fas fa-rocket mr-2"></i>
                        Start Learning Today
                    </a>
                    <a href="{% url 'subjects:list' %}" class="border-2 border-white text-white hover:bg-white hover:text-green-600 font-semibold px-6 sm:px-8 py-3 sm:py-4 rounded-xl transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-book mr-2"></i>
                        Browse Subjects
                    </a>
                </div>
            {% else %}
                <a href="{% url 'core:dashboard' %}" class="bg-white text-green-600 hover:text-green-700 font-semibold px-6 sm:px-8 py-3 sm:py-4 rounded-xl hover:bg-gray-50 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl inline-block">
                    <i class="fas fa-tachometer-alt mr-2"></i>
                    Continue Learning
                </a>
            {% endif %}
        </div>
    </div>
</section>
{% endblock %}
