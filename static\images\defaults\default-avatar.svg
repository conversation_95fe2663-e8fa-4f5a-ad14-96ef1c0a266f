<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="avatarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="100" cy="100" r="100" fill="url(#avatarGradient)"/>
  
  <!-- User icon -->
  <g fill="white" opacity="0.9">
    <!-- Head -->
    <circle cx="100" cy="75" r="25"/>
    <!-- Body -->
    <path d="M100 110 C75 110, 55 130, 55 155 L55 180 C55 185, 60 190, 65 190 L135 190 C140 190, 145 185, 145 180 L145 155 C145 130, 125 110, 100 110 Z"/>
  </g>
  
  <!-- Subtle border -->
  <circle cx="100" cy="100" r="98" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
</svg>
